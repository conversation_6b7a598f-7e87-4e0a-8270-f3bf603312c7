{"name": "App Settings AU_ANDROID", "created": "2025-08-12 08:30:41", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "XEbZHdi0GT", "executionTime": "103ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "terminateApp"}, {"action_id": "XEbZHdi0GT", "executionTime": "1211ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "launchApp"}, {"action_id": "veukWo4573", "executionTime": "4752ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtHomeAccountCtaSignIn\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ArAkdzcpEN", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "zxSSc6NS3A", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754699352537, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}, {"action_id": "QugrQS9qYH", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Sign in\"]", "method": "locator", "timeout": 10, "timestamp": 1754709806783, "type": "tap"}], "test_case_steps_count": 7, "timestamp": 1752580492266, "type": "multiStep"}, {"action_id": "mIKA85kXaW", "executionTime": "122ms", "package_id": "com.android.settings", "timestamp": 1749444829578, "type": "terminateApp"}, {"action_id": "LfyQctrEJn", "executionTime": "1425ms", "package_id": "com.android.settings", "timestamp": 1749444810598, "type": "launchApp"}, {"action_id": "w1RV76df9x", "executionTime": "415ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//androidx.recyclerview.widget.RecyclerView[@resource-id=\"com.android.settings:id/recycler_view\"]/android.widget.LinearLayout[3]", "method": "locator", "text_to_find": "Wi-Fi", "timeout": 10, "timestamp": 1749444898707, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5039ms", "time": 5, "timestamp": 1749445273934, "type": "wait"}, {"action_id": "jUCAk6GJc4", "executionTime": "1421ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Switch[@resource-id=\"android:id/switch_widget\"]", "method": "locator", "timeout": 10, "timestamp": 1749445081254, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5036ms", "time": 5, "timestamp": 1750843578808, "type": "wait"}, {"action_id": "oSQ8sPdVOJ", "executionTime": "167ms", "package_id": "au.com.kmart", "timestamp": 1752581195187, "type": "terminateApp"}, {"action_id": "oSQ8sPdVOJ", "executionTime": "1306ms", "package_id": "au.com.kmart", "timestamp": 1749445437019, "type": "launchApp"}, {"action_id": "cokvFXhj4c", "executionTime": "14791ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445125565, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "910ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445177888, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "686ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581287764, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "41097ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752581297924, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "176ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581320274, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "881ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752581338392, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "169ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581366187, "type": "exists"}, {"action_id": "mIKA85kXaW", "executionTime": "155ms", "package_id": "com.android.settings", "timestamp": 1752581395092, "type": "terminateApp"}, {"action_id": "LfyQctrEJn", "executionTime": "1178ms", "package_id": "com.android.settings", "timestamp": 1752581426909, "type": "launchApp"}, {"action_id": "w1RV76df9x", "executionTime": "514ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//androidx.recyclerview.widget.RecyclerView[@resource-id=\"com.android.settings:id/recycler_view\"]/android.widget.LinearLayout[3]", "method": "locator", "text_to_find": "Wi-Fi", "timeout": 10, "timestamp": 1752581453309, "type": "tap"}, {"action_id": "jUCAk6GJc4", "executionTime": "960ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Switch[@resource-id=\"android:id/switch_widget\"]", "method": "locator", "timeout": 10, "timestamp": 1752581473143, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5037ms", "time": 5, "timestamp": 1750843560629, "type": "wait"}, {"action_id": "hCCEvRtj1A", "executionTime": "165ms", "package_id": "au.com.kmart", "timestamp": 1749445309230, "type": "terminateApp"}, {"action_id": "hCCEvRtj1A", "executionTime": "1305ms", "package_id": "au.com.kmart", "timestamp": 1752581522673, "type": "launchApp"}, {"action_id": "UpUSVInizv", "executionTime": "3070ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445687300, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 10, "executionTime": "17815ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749445784426, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.7]}, {"action_id": "LcYLwUffqj", "executionTime": "1643ms", "text_to_find": "out", "timeout": 30, "timestamp": 1749445809311, "type": "tapOnText"}, {"action_id": "rmqVgsHPp8", "executionTime": "1572ms", "package_id": "com.android.chrome", "timestamp": 1753532546014, "type": "restartApp"}, {"action_id": "KAyXxO6c02", "executionTime": "222ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"com.android.chrome:id/signin_fre_dismiss_button\"]", "timeout": 10, "timestamp": 1753532616815, "type": "tapIfLocatorExists"}, {"action_id": "pOJrD4NK3F", "executionTime": "227ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"com.android.chrome:id/more_button\"]", "timeout": 10, "timestamp": 1753532718163, "type": "tapIfLocatorExists"}, {"action_id": "t6c0RSgUVq", "executionTime": "382ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"com.android.chrome:id/ack_button\"]", "timeout": 10, "timestamp": 1753532783771, "type": "tapIfLocatorExists"}, {"action_id": "Pj4FuA5oi5", "executionTime": "670ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"com.android.chrome:id/url_bar\"]", "method": "locator", "timeout": 15, "timestamp": 1753532862179, "type": "tap"}, {"action_id": "fTdGMJ3NH3", "enter": true, "executionTime": "2608ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "method": "locator", "text": "kmart au", "timeout": 10, "timestamp": 1749446027317, "type": "text"}, {"action_id": "mPIklklBO0", "executionTime": "176ms", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754741841119, "type": "androidFunctions"}, {"action_id": "UpUSVInizv", "executionTime": "2022ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"https://www.kmart.com.au\"]", "method": "locator", "timeout": 10, "timestamp": 1749472397515, "type": "tap"}, {"action_id": "QUeGIASAxV", "count": 1, "direction": "up", "duration": 300, "enabled": true, "end_x": 50, "end_y": 30, "executionTime": "647ms", "interval": 0.5, "method": "coordinates", "start_x": 50, "start_y": 70, "timestamp": 1751711412051, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "x": 0, "y": 0}, {"action_id": "Cmvm82hiAa", "executionTime": "976ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Home & Living\"]", "method": "locator", "timeout": 10, "timestamp": 1749472379198, "type": "tap"}, {"action_id": "QUeGIASAxV", "count": 1, "direction": "up", "duration": 300, "enabled": true, "end_x": 50, "end_y": 30, "executionTime": "20042ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[@content-desc=\"Styled by You\"]/android.view.View[2]/android.view.View/android.widget.ImageView[1]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1752582182766, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "x": 0, "y": 0}, {"action_id": "gkkQzTCmma", "executionTime": "31880ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"$\")]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1749470262060, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "1374ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1751711463086, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "gkkQzTCmma", "executionTime": "18651ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1752582562441, "type": "tap"}, {"action_id": "gkkQzTCmma", "executionTime": "130ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1752582616397, "type": "androidFunctions"}, {"action_id": "gkkQzTCmma", "executionTime": "10277ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1752582664264, "type": "androidFunctions"}, {"action_id": "UpUSVInizv", "executionTime": "369ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752582676415, "type": "tap"}, {"action_id": "BTvTGpQ9IK", "executionTime": "54233ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1753534062594, "type": "tapIfLocatorExists"}, {"action_id": "igReeDqips", "executionTime": "498ms", "image_filename": "delivery-tab-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1749471352255, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 50, "executionTime": "1375ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1753536378673, "type": "swipe", "vector_end": [0.5, 0.5], "vector_start": [0.5, 0.7]}, {"action_id": "Pd7cReoJM6", "executionTime": "448ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Increase quantity\"]", "method": "locator", "text_to_find": "List", "timeout": 10, "timestamp": 1749472320276, "type": "tap"}, {"action_id": "Pd7cReoJM6", "executionTime": "555ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Decrease quantity\"]", "method": "locator", "text_to_find": "List", "timeout": 10, "timestamp": 1752582906295, "type": "tap"}, {"action_id": "JcAR0JctQ6", "executionTime": "489ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1749472290567, "type": "tap"}, {"action_id": "KqTcr10JDm", "executionTime": "15190ms", "image_filename": "bag-close-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752583076309, "type": "tap"}, {"action_id": "UpUSVInizv", "executionTime": "309ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752583125267, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "1362ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752583159491, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "gkkQzTCmma", "executionTime": "2606ms", "text_to_find": "Catalogue", "timeout": 30, "timestamp": 1749472424498, "type": "tapOnText"}, {"action_id": "S7PVvWSmaK", "executionTime": "6063ms", "image_filename": "catalogue-menu-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1753536639512, "type": "tap"}, {"action_id": "YHaMIjULRf", "executionTime": "6543ms", "text_to_find": "List", "timeout": 30, "timestamp": 1749472769571, "type": "tapOnText"}, {"action_id": "Qy0Y0uJchm", "executionTime": "1613ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.TextView[contains(@text,\"$\")])[1]", "method": "locator", "timeout": 20, "timestamp": 1749472775719, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 50, "executionTime": "1656ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1753534634063, "type": "swipe", "vector_end": [0.5, 0.5], "vector_start": [0.5, 0.7]}, {"action_id": "Iab9zCfpqO", "double_tap": false, "executionTime": "3375ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "text_to_find": "Add", "timeout": 30, "timestamp": 1749472786795, "type": "tapOnText"}, {"action_id": "UpUSVInizv", "executionTime": "644ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1753534156795, "type": "tap"}, {"action_id": "jW6OPorKBq", "executionTime": "37050ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1753534201332, "type": "tapIfLocatorExists"}, {"action_id": "K0c1gL9UK1", "executionTime": "421ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1749472862396, "type": "tap"}, {"action_id": "3NOS1fbxZs", "executionTime": "4029ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749473253040, "type": "tap"}, {"action_id": "Qb1AArnpCH", "duration": 5, "executionTime": "5038ms", "time": 5, "timestamp": 1750975463814, "type": "wait"}, {"type": "multiStep", "timestamp": 1754951426117, "test_case_id": "Kmart_AU_Cleanup_ANDROID_20250810070223.json", "test_case_name": "Kmart_AU_Cleanup_ANDROID", "test_case_steps_count": 7, "expanded": false, "loading_in_progress": false, "test_case_steps": [{"action_id": "CMyeX5W55A", "package_id": "au.com.kmart", "timestamp": 1754864056092, "type": "terminateApp", "is_cleanup_step": true}, {"action_id": "ZCxsRcveYt", "package_id": "au.com.kmart", "timestamp": 1754864072724, "type": "launchApp", "is_cleanup_step": true}, {"action_id": "ix3JNUNHO5", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1754864107006, "type": "tap", "is_cleanup_step": true}, {"action_id": "yTiLSpGVCk", "duration": 5, "time": 5, "timestamp": 1754864137271, "type": "wait", "is_cleanup_step": true}, {"action_id": "R8mQpTxoDS", "count": 2, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1754900266403, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "is_cleanup_step": true}, {"action_id": "zapjXsPOwn", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "timeout": 10, "timestamp": 1754946124893, "type": "tapIfLocatorExists", "is_cleanup_step": true}, {"action_id": "CMyeX5W55A", "package_id": "au.com.kmart", "timestamp": 1754864348654, "type": "terminateApp", "is_cleanup_step": true}], "steps_loaded": true, "display_depth": 0, "action_id": "37GOEJixHo"}], "labels": [], "updated": "2025-08-12 08:30:41"}