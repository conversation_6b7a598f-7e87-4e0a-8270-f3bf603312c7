2025-08-12 18:24:11,916 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 18:24:11,916 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 18:24:11,917 - app_android.config_android - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 18:24:11,917 - app_android.config_android - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android
2025-08-12 18:24:11,918 - app_android.config_android - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_android
2025-08-12 18:24:11,918 - app_android.config_android - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-08-12 18:24:11,919 - app_android.config_android - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-12 18:24:11,919 - app_android.config_android - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android/suites
2025-08-12 18:24:11,919 - app_android.config_android - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_android
2025-08-12 18:24:11,920 - app_android.config_android - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android
2025-08-12 18:24:11,920 - app_android.config_android - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/files_to_push
2025-08-12 18:24:11,920 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-08-12 18:24:11,920 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4724, WDA: 8300) - preserving existing processes for multi-instance support
2025-08-12 18:24:11,920 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-08-12 18:24:13,042 - app_android.utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
2025-08-12 18:24:13,042 - app_android.utils.global_values_db - INFO - Global values database initialized successfully
2025-08-12 18:24:13,043 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 18:24:13,043 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 18:24:13,044 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/android_data/reports
2025-08-12 18:24:13,044 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
2025-08-12 18:24:13,044 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-12 18:24:13,045 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-12 18:24:13,045 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/suites
2025-08-12 18:24:13,045 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_ios
2025-08-12 18:24:13,046 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_ios
2025-08-12 18:24:13,046 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-08-12 18:24:13,046 - app_android.utils.global_values_db - INFO - Using global values from config.py
2025-08-12 18:24:13,046 - app_android.utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-08-12 18:24:13,048 - app_android.utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-08-12 18:24:13,050 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-08-12 18:24:13,085 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-08-12 18:24:13,406 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 18:24:13,481 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-08-12 18:24:13,482 - utils.database - INFO - Test_steps table schema updated successfully
2025-08-12 18:24:13,482 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-08-12 18:24:13,482 - utils.database - INFO - Screenshots table schema updated successfully
2025-08-12 18:24:13,482 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-08-12 18:24:13,483 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-08-12 18:24:13,483 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-08-12 18:24:13,483 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-08-12 18:24:13,483 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-08-12 18:24:13,483 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-08-12 18:24:13,483 - utils.database - INFO - Database initialized successfully
2025-08-12 18:24:13,483 - utils.database - INFO - Checking initial database state...
2025-08-12 18:24:13,484 - utils.database - INFO - Database state: 0 suites, 0 cases, 1147 steps, 0 screenshots, 1273 tracking entries
2025-08-12 18:24:13,497 - app - INFO - Using directories from config.py:
2025-08-12 18:24:13,497 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 18:24:13,498 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-12 18:24:13,498 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
[2025-08-12 18:24:13,502] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-12 18:24:13,503] INFO in database: Test_steps table schema updated successfully
[2025-08-12 18:24:13,503] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 18:24:13,504] INFO in database: Screenshots table schema updated successfully
[2025-08-12 18:24:13,504] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 18:24:13,504] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 18:24:13,504] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 18:24:13,504] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 18:24:13,505] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 18:24:13,505] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 18:24:13,505] INFO in database: Database initialized successfully
[2025-08-12 18:24:13,505] INFO in database: Checking initial database state...
[2025-08-12 18:24:13,506] INFO in database: Database state: 0 suites, 0 cases, 1147 steps, 0 screenshots, 1273 tracking entries
[2025-08-12 18:24:13,507] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-12 18:24:13,508] INFO in database: Test_steps table schema updated successfully
[2025-08-12 18:24:13,508] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 18:24:13,509] INFO in database: Screenshots table schema updated successfully
[2025-08-12 18:24:13,509] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 18:24:13,509] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 18:24:13,509] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 18:24:13,509] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 18:24:13,509] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 18:24:13,509] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 18:24:13,510] INFO in database: Database initialized successfully
[2025-08-12 18:24:13,510] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 18:24:13,510] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 18:24:13,510] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 18:24:13,510] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 18:24:13,510] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 18:24:13,510] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 18:24:13,511] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 18:24:13,511] INFO in database: Screenshots table schema updated successfully
[2025-08-12 18:24:13,586] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4724, WDA port: 8200
[2025-08-12 18:24:13,601] INFO in appium_device_controller: Appium server is running and ready
[2025-08-12 18:24:13,601] INFO in appium_device_controller: Appium server is already running and responsive
[2025-08-12 18:24:13,601] INFO in optimized_session_manager: OptimizedSessionManager initialized
[2025-08-12 18:24:13,601] INFO in appium_device_controller: Optimized session manager initialized
Starting Mobile App Automation Tool (Android)...
Configuration:
  - Flask server port: 8081
  - Appium server port: 4724
  - WebDriverAgent port: 8300
Open your web browser and navigate to: http://localhost:8081
 * Serving Flask app 'app'
 * Debug mode: on
[2025-08-12 18:24:13,670] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://**************:8081
[2025-08-12 18:24:13,670] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-08-12 18:24:14,987] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 18:24:14] "GET /api/reports/latest HTTP/1.1" 200 -
