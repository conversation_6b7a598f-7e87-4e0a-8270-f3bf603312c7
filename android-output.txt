2025-08-12 15:35:31,056 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 15:35:31,057 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 15:35:31,057 - app_android.config_android - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 15:35:31,058 - app_android.config_android - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android
2025-08-12 15:35:31,058 - app_android.config_android - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_android
2025-08-12 15:35:31,059 - app_android.config_android - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-08-12 15:35:31,059 - app_android.config_android - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-12 15:35:31,060 - app_android.config_android - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android/suites
2025-08-12 15:35:31,060 - app_android.config_android - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_android
2025-08-12 15:35:31,061 - app_android.config_android - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android
2025-08-12 15:35:31,061 - app_android.config_android - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/files_to_push
2025-08-12 15:35:31,061 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-08-12 15:35:31,061 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4724, WDA: 8300) - preserving existing processes for multi-instance support
2025-08-12 15:35:31,062 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-08-12 15:35:31,936 - app_android.utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
2025-08-12 15:35:31,937 - app_android.utils.global_values_db - INFO - Global values database initialized successfully
2025-08-12 15:35:31,938 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 15:35:31,938 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 15:35:31,938 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/android_data/reports
2025-08-12 15:35:31,939 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
2025-08-12 15:35:31,939 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-12 15:35:31,940 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-12 15:35:31,940 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/suites
2025-08-12 15:35:31,941 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_ios
2025-08-12 15:35:31,941 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_ios
2025-08-12 15:35:31,942 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-08-12 15:35:31,942 - app_android.utils.global_values_db - INFO - Using global values from config.py
2025-08-12 15:35:31,942 - app_android.utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-08-12 15:35:31,944 - app_android.utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-08-12 15:35:31,946 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-08-12 15:35:31,981 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-08-12 15:35:32,302 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 15:35:32,374 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-08-12 15:35:32,374 - utils.database - INFO - Test_steps table schema updated successfully
2025-08-12 15:35:32,374 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-08-12 15:35:32,375 - utils.database - INFO - Screenshots table schema updated successfully
2025-08-12 15:35:32,375 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-08-12 15:35:32,375 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-08-12 15:35:32,375 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-08-12 15:35:32,375 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-08-12 15:35:32,375 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-08-12 15:35:32,375 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-08-12 15:35:32,376 - utils.database - INFO - Database initialized successfully
2025-08-12 15:35:32,376 - utils.database - INFO - Checking initial database state...
2025-08-12 15:35:32,376 - utils.database - INFO - Database state: 0 suites, 0 cases, 1147 steps, 0 screenshots, 1273 tracking entries
2025-08-12 15:35:32,390 - app - INFO - Using directories from config.py:
2025-08-12 15:35:32,391 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 15:35:32,391 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-12 15:35:32,391 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
[2025-08-12 15:35:32,394] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-12 15:35:32,395] INFO in database: Test_steps table schema updated successfully
[2025-08-12 15:35:32,395] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 15:35:32,396] INFO in database: Screenshots table schema updated successfully
[2025-08-12 15:35:32,397] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 15:35:32,397] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 15:35:32,397] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 15:35:32,398] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 15:35:32,398] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 15:35:32,398] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 15:35:32,398] INFO in database: Database initialized successfully
[2025-08-12 15:35:32,398] INFO in database: Checking initial database state...
[2025-08-12 15:35:32,399] INFO in database: Database state: 0 suites, 0 cases, 1147 steps, 0 screenshots, 1273 tracking entries
[2025-08-12 15:35:32,402] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-12 15:35:32,403] INFO in database: Test_steps table schema updated successfully
[2025-08-12 15:35:32,403] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 15:35:32,403] INFO in database: Screenshots table schema updated successfully
[2025-08-12 15:35:32,403] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 15:35:32,404] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 15:35:32,404] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 15:35:32,404] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 15:35:32,404] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 15:35:32,404] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 15:35:32,404] INFO in database: Database initialized successfully
[2025-08-12 15:35:32,404] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 15:35:32,405] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 15:35:32,405] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 15:35:32,405] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 15:35:32,405] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 15:35:32,405] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 15:35:32,405] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 15:35:32,405] INFO in database: Screenshots table schema updated successfully
[2025-08-12 15:35:32,477] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4724, WDA port: 8200
[2025-08-12 15:35:32,488] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4724): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x117f31400>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-08-12 15:35:32,488] INFO in appium_device_controller: Using custom ports (Appium: 4724, WDA: 8200) - preserving existing processes for multi-instance support
[2025-08-12 15:35:32,490] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-08-12 15:35:32,490] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-08-12 15:35:33,301] INFO in appium_device_controller: Installed Appium drivers: 
[2025-08-12 15:35:33,301] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-08-12 15:35:34,089] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-08-12 15:35:34,090] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-08-12 15:35:34,090] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-08-12 15:35:34,097] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4724 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-08-12 15:35:36,118] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:36,118] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:35:38,125] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:38,125] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:35:40,136] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:40,136] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:35:42,146] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:42,146] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:35:44,155] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:44,155] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:35:46,163] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:46,163] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:35:48,170] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:48,170] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:35:50,180] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:50,180] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:35:52,192] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:52,192] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:35:54,201] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:54,201] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:35:56,212] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:56,212] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:35:58,219] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:35:58,220] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:36:00,230] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:36:00,230] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:36:02,241] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:36:02,241] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:36:04,247] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 15:36:04,247] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 15:36:04,247] INFO in optimized_session_manager: OptimizedSessionManager initialized
[2025-08-12 15:36:04,247] INFO in appium_device_controller: Optimized session manager initialized
Starting Mobile App Automation Tool (Android)...
Configuration:
  - Flask server port: 8081
  - Appium server port: 4724
  - WebDriverAgent port: 8300
Open your web browser and navigate to: http://localhost:8081
 * Serving Flask app 'app'
 * Debug mode: on
[2025-08-12 15:36:04,324] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://**************:8081
[2025-08-12 15:36:04,325] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-08-12 15:36:08,131] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET / HTTP/1.1" 200 -
[2025-08-12 15:36:08,158] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,160] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,163] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,163] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,164] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,169] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,171] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,173] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/css/detachable-device-screen.css HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,174] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,177] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/css/test-case-modification.css HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,178] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/css/bulk-locator-manager.css HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,180] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,181] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,188] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,188] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,189] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,190] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/bulk-locator-manager.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,198] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/action-manager.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,199] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,203] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,205] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,207] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/detachable-device-screen.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,210] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,210] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,212] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,216] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,221] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,224] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,229] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,230] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,232] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,237] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,238] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,242] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,244] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,248] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,249] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/test-case-modification.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,251] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,265] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/environments HTTP/1.1" 200 -
[2025-08-12 15:36:08,272] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-08-12 15:36:08,295] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 15:36:08,302] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/bulk_locator/backups HTTP/1.1" 200 -
[2025-08-12 15:36:08,305] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/test_cases/files HTTP/1.1" 200 -
[2025-08-12 15:36:08,309] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-08-12 15:36:08,313] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/settings HTTP/1.1" 200 -
[2025-08-12 15:36:08,316] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-08-12 15:36:08,322] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-08-12 15:36:08,327] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/environments/current HTTP/1.1" 200 -
[2025-08-12 15:36:08,341] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 15:36:08,346] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-08-12 15:36:08,357] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/environments/2/variables HTTP/1.1" 200 -
[2025-08-12 15:36:08,359] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 15:36:08,363] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 15:36:08,377] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 15:36:08,387] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 15:36:08,397] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 15:36:08,402] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/test_cases/action_types HTTP/1.1" 200 -
[2025-08-12 15:36:08,420] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-08-12 15:36:08,432] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/test_cases/locator_types HTTP/1.1" 200 -
[2025-08-12 15:36:08,460] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-08-12 15:36:08,465] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:08] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-08-12 15:36:09,528] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:09] "GET /api/devices HTTP/1.1" 200 -
[2025-08-12 15:36:11,509] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8200
[2025-08-12 15:36:11,514] INFO in appium_device_controller: Appium server is running and ready
[2025-08-12 15:36:11,514] INFO in appium_device_controller: Appium server is already running and responsive
[2025-08-12 15:36:11,515] INFO in optimized_session_manager: OptimizedSessionManager initialized
[2025-08-12 15:36:11,515] INFO in appium_device_controller: Optimized session manager initialized
[2025-08-12 15:36:11,515] INFO in appium_device_controller: Connecting to device: PJTCI7EMSSONYPU8 with options: None, platform hint: Android
[2025-08-12 15:36:11,515] INFO in appium_device_controller: Attempting AppiumDeviceManager connection for device: PJTCI7EMSSONYPU8
[2025-08-12 15:36:14,530] INFO in grid_manager: Falling back to direct connection for Android
[2025-08-12 15:36:14,532] WARNING in grid_config: Grid not available, falling back to direct connection for android
[2025-08-12 15:36:14,532] INFO in grid_manager: Session created: session_1754976974_Android for Android
[2025-08-12 15:36:14,533] WARNING in appium_device_controller: AppiumDeviceManager connection failed: 'NoneType' object has no attribute 'to_capabilities'
[2025-08-12 15:36:14,533] INFO in appium_device_controller: AppiumDeviceManager connection failed, falling back to direct connection
[2025-08-12 15:36:14,533] INFO in appium_device_controller: Connection attempt 1/3
[2025-08-12 15:36:14,533] INFO in appium_device_controller: Using provided platform hint: Android
[2025-08-12 15:36:14,533] INFO in appium_device_controller: Added Android-specific UiAutomator2 capabilities with enhanced stability settings
[2025-08-12 15:36:14,533] INFO in appium_device_controller: Desired capabilities: {'platformName': 'Android', 'deviceName': 'PJTCI7EMSSONYPU8', 'udid': 'PJTCI7EMSSONYPU8', 'newCommandTimeout': 600, 'noReset': True, 'automationName': 'UiAutomator2', 'uiautomator2ServerLaunchTimeout': 120000, 'uiautomator2ServerInstallTimeout': 120000, 'adbExecTimeout': 120000, 'skipServerInstallation': False, 'skipDeviceInitialization': False, 'ignoreHiddenApiPolicyError': True, 'disableWindowAnimation': True, 'autoGrantPermissions': True, 'dontStopAppOnReset': True, 'unicodeKeyboard': True, 'resetKeyboard': True, 'skipLogcatCapture': True, 'enforceXPath1': True, 'eventTimings': True, 'printPageSourceOnFindFailure': False, 'shouldTerminateApp': False, 'forceAppLaunch': False, 'systemPort': 8201, 'mjpegServerPort': 7811, 'clearSystemFiles': True, 'skipUnlock': True}
[2025-08-12 15:36:14,533] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'Android', 'appium:deviceName': 'PJTCI7EMSSONYPU8', 'appium:udid': 'PJTCI7EMSSONYPU8', 'appium:newCommandTimeout': 600, 'appium:noReset': True, 'appium:automationName': 'UiAutomator2', 'appium:uiautomator2ServerLaunchTimeout': 120000, 'appium:uiautomator2ServerInstallTimeout': 120000, 'appium:adbExecTimeout': 120000, 'appium:skipServerInstallation': False, 'appium:skipDeviceInitialization': False, 'appium:ignoreHiddenApiPolicyError': True, 'appium:disableWindowAnimation': True, 'appium:autoGrantPermissions': True, 'appium:dontStopAppOnReset': True, 'appium:unicodeKeyboard': True, 'appium:resetKeyboard': True, 'appium:skipLogcatCapture': True, 'appium:enforceXPath1': True, 'appium:eventTimings': True, 'appium:printPageSourceOnFindFailure': False, 'appium:shouldTerminateApp': False, 'appium:forceAppLaunch': False, 'appium:systemPort': 8201, 'appium:mjpegServerPort': 7811, 'appium:clearSystemFiles': True, 'appium:skipUnlock': True}
[2025-08-12 15:36:14,533] INFO in appium_device_controller: Connection attempt 1/3
[2025-08-12 15:36:14,535] WARNING in grid_config: Grid not available, falling back to direct connection for android
[2025-08-12 15:36:14,535] INFO in appium_device_controller: Using connection URL: http://127.0.0.1:4724/wd/hub
[2025-08-12 15:36:18,435] WARNING in appium_device_controller: Healenium services unhealthy, using original driver
[2025-08-12 15:36:18,435] INFO in appium_device_controller: Connection verified with capabilities: Android
[2025-08-12 15:36:18,435] INFO in appium_device_controller: Initializing platform helpers for Android
[2025-08-12 15:36:18,435] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-08-12 15:36:18,459] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-08-12 15:36:18,459] INFO in appium_device_controller: Device dimensions: (1080, 2400)
[2025-08-12 15:36:18,465] INFO in appium_device_controller: Initialized ImageMatcher for Android device: PJTCI7EMSSONYPU8 with Appium driver
[2025-08-12 15:36:18,465] INFO in appium_device_controller: Initializing Android-specific helpers
[2025-08-12 15:36:18,465] INFO in appium_device_controller: Android version: 12.0
[2025-08-12 15:36:18,466] INFO in appium_device_controller: Setting up UiAutomator2 support
[2025-08-12 15:36:18,533] INFO in appium_device_controller: ADB shell access confirmed
[2025-08-12 15:36:18,533] INFO in appium_device_controller: Attempting to initialize UIAutomator2 helper for device: PJTCI7EMSSONYPU8
[2025-08-12 15:36:18,536] DEBUG in appium_device_controller: UIAutomator2Helper class imported successfully
[2025-08-12 15:36:18,635] INFO in uiautomator2_helper: ADB connection confirmed for UIAutomator2 helper (device: PJTCI7EMSSONYPU8)
[2025-08-12 15:36:18,636] INFO in appium_device_controller: UIAutomator2 helper initialized successfully for device: PJTCI7EMSSONYPU8
[2025-08-12 15:36:18,636] INFO in appium_device_controller: UIAutomator2 helper is ready and ADB connection confirmed
[2025-08-12 15:36:18,636] INFO in appium_device_controller: Platform helpers initialization completed
[2025-08-12 15:36:18,636] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-08-12 15:36:18,636] INFO in appium_device_controller: Connection monitoring started
[2025-08-12 15:36:18,637] INFO in action_factory: Registered basic actions: tap, wait
[2025-08-12 15:36:18,638] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-08-12 15:36:18,638] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,639] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-08-12 15:36:18,640] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,640] INFO in action_factory: Registered action handler for 'multiStep'
[2025-08-12 15:36:18,641] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,641] INFO in action_factory: Registered action handler for 'swipe'
[2025-08-12 15:36:18,642] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,642] INFO in action_factory: Registered action handler for 'getParam'
[2025-08-12 15:36:18,643] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,643] INFO in action_factory: Registered action handler for 'wait'
[2025-08-12 15:36:18,643] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,643] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-08-12 15:36:18,644] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,644] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-08-12 15:36:18,645] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,645] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-08-12 15:36:18,647] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,647] INFO in action_factory: Registered action handler for 'text'
[2025-08-12 15:36:18,648] INFO in action_factory: Special case: Registering tap_if_text_exists_action.py as 'tapIfTextExists'
[2025-08-12 15:36:18,649] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,649] INFO in action_factory: Registered action handler for 'tapIfTextExists'
[2025-08-12 15:36:18,650] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,650] INFO in action_factory: Registered action handler for 'waitTill'
[2025-08-12 15:36:18,651] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,652] INFO in action_factory: Registered action handler for 'hookAction'
[2025-08-12 15:36:18,653] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,653] INFO in action_factory: Registered action handler for 'inputText'
[2025-08-12 15:36:18,654] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
[2025-08-12 15:36:18,655] INFO in global_values_db: Global values database initialized successfully
[2025-08-12 15:36:18,655] INFO in global_values_db: Using global values from config.py
[2025-08-12 15:36:18,655] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-08-12 15:36:18,657] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,657] INFO in action_factory: Registered action handler for 'setParam'
[2025-08-12 15:36:18,658] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-08-12 15:36:18,658] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,658] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-08-12 15:36:18,659] INFO in action_factory: Special case: Registering android_functions_action.py as 'androidFunctions'
[2025-08-12 15:36:18,659] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,659] INFO in action_factory: Registered action handler for 'androidFunctions'
[2025-08-12 15:36:18,660] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,660] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-08-12 15:36:18,661] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,661] INFO in action_factory: Registered action handler for 'clickImage'
[2025-08-12 15:36:18,662] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,662] INFO in action_factory: Registered action handler for 'tap'
[2025-08-12 15:36:18,663] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,663] INFO in action_factory: Registered action handler for 'clearText'
[2025-08-12 15:36:18,664] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-08-12 15:36:18,664] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,664] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-08-12 15:36:18,665] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-08-12 15:36:18,665] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,665] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-08-12 15:36:18,666] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,666] INFO in action_factory: Registered action handler for 'hideKeyboard'
[2025-08-12 15:36:18,667] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,667] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-08-12 15:36:18,668] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-08-12 15:36:18,668] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,668] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-08-12 15:36:18,669] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,669] INFO in action_factory: Registered action handler for 'launchApp'
[2025-08-12 15:36:18,670] INFO in action_factory: Special case: Registering if_then_steps_action.py as 'ifThenSteps'
[2025-08-12 15:36:18,670] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,670] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-08-12 15:36:18,671] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-08-12 15:36:18,671] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,671] INFO in action_factory: Registered action handler for 'info'
[2025-08-12 15:36:18,671] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,672] INFO in action_factory: Registered action handler for 'waitElement'
[2025-08-12 15:36:18,672] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,672] INFO in action_factory: Registered action handler for 'compareValue'
[2025-08-12 15:36:18,673] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,673] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-08-12 15:36:18,674] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-08-12 15:36:18,674] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,674] INFO in action_factory: Registered action handler for 'exists'
[2025-08-12 15:36:18,675] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,675] INFO in action_factory: Registered action handler for 'clickElement'
[2025-08-12 15:36:18,675] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,676] INFO in action_factory: Registered action handler for 'randomData'
[2025-08-12 15:36:18,676] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,676] INFO in action_factory: Registered action handler for 'getValue'
[2025-08-12 15:36:18,677] INFO in action_factory: Registered action handler for 'test'
[2025-08-12 15:36:18,677] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,678] INFO in action_factory: Registered action handler for 'restartApp'
[2025-08-12 15:36:18,678] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-08-12 15:36:18,678] INFO in base_action: Enhanced element finder initialized
[2025-08-12 15:36:18,678] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-08-12 15:36:18,679] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'tapIfTextExists', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'androidFunctions', 'swipeTillVisible', 'clickImage', 'clearText', 'takeScreenshot', 'tapIfLocatorExists', 'hideKeyboard', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'tap': TapAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'wait': WaitAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'text': TextAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'tapIfTextExists': TapIfTextExistsAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'androidFunctions': AndroidFunctionsAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'clearText': ClearTextAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-08-12 15:36:18,679] INFO in action_factory: Handler for 'hideKeyboard': HideKeyboardAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'info': InfoAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'test': TestAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-08-12 15:36:18,680] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-08-12 15:36:18,680] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-12 15:36:18,680] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-12 15:36:18,830] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-12 15:36:18,831] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android/screenshots/screenshot_20250812_153618.png (save_debug=False)
[2025-08-12 15:36:18,831] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
2025-08-12 15:36:18,831 - image_matcher - INFO - Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-08-12 15:36:18,831] INFO in image_matcher: Taking ADB screenshot for device: PJTCI7EMSSONYPU8
2025-08-12 15:36:19,937 - image_matcher - INFO - Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android/debug_images/screenshot_1754976978.png
[2025-08-12 15:36:19,937] INFO in image_matcher: Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android/debug_images/screenshot_1754976978.png
[2025-08-12 15:36:19,938] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android/screenshots/screenshot_20250812_153618.png
[2025-08-12 15:36:19,938] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153618.png
[2025-08-12 15:36:19,970] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:19] "POST /api/device/connect HTTP/1.1" 200 -
[2025-08-12 15:36:20,980] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:36:20,980] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
2025-08-12 15:36:20,980 - image_matcher - INFO - Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-08-12 15:36:20,980] INFO in image_matcher: Taking ADB screenshot for device: PJTCI7EMSSONYPU8
2025-08-12 15:36:21,990 - image_matcher - INFO - Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android/debug_images/screenshot_1754976980.png
[2025-08-12 15:36:21,990] INFO in image_matcher: Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android/debug_images/screenshot_1754976980.png
[2025-08-12 15:36:21,991] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:21,991] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:21,992] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:21] "GET /screenshot?deviceId=PJTCI7EMSSONYPU8&clientSessionId=client_1754976968258_tnjz6md0u_1754966681111_xbponw75f&t=1754976980976 HTTP/1.1" 200 -
[2025-08-12 15:36:26,660] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:26] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-08-12 15:36:26,664] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:26] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-08-12 15:36:28,042] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:28] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-08-12 15:36:28,044] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:28] "GET /api/test_suites/5e651293-852e-4e7b-90a3-fe64118c7330 HTTP/1.1" 200 -
[2025-08-12 15:36:28,053] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:28] "GET /api/test_cases/load/App_Settings_AU_ANDROID_20250715215105.json HTTP/1.1" 200 -
[2025-08-12 15:36:28,059] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:28] "GET /api/test_cases/load/AU_MyAccount_Android_20250712145003.json HTTP/1.1" 200 -
[2025-08-12 15:36:28,065] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:28] "GET /api/test_cases/load/All_Sign_ins_AUANDROID_20250715202114.json HTTP/1.1" 200 -
[2025-08-12 15:36:28,071] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:28] "GET /api/test_cases/load/Postcode_Flow_AU_ANDROID_20250702070922.json HTTP/1.1" 200 -
[2025-08-12 15:36:28,076] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:28] "GET /api/test_cases/load/WishList_AUAndroid_20250715224301.json HTTP/1.1" 200 -
[2025-08-12 15:36:28,081] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:28] "GET /api/test_cases/load/Browse__PDP_AU_ANDROID_20250709040606.json HTTP/1.1" 200 -
[2025-08-12 15:36:28,087] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:28] "GET /api/test_cases/load/Delivery__CNC_AUANDROID_20250709122628.json HTTP/1.1" 200 -
[2025-08-12 15:36:28,092] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:28] "GET /api/test_cases/load/AU__Performance_Android_20250712160300.json HTTP/1.1" 200 -
[2025-08-12 15:36:38,269] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:38] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 15:36:38,274] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:38] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 15:36:38,861] INFO in optimized_screenshot_manager: OptimizedScreenshotManager initialized with report_dir: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638
[2025-08-12 15:36:38,861] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:38] "POST /api/report/initialize HTTP/1.1" 200 -
[2025-08-12 15:36:38,865] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:38] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-08-12 15:36:38,870] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:38] "POST /api/database/clear_screenshots HTTP/1.1" 200 -
[2025-08-12 15:36:38,880] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:36:38,880] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
2025-08-12 15:36:38,880 - image_matcher - INFO - Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-08-12 15:36:38,880] INFO in image_matcher: Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-08-12 15:36:38,914] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 15:36:38] "GET /api/get_execution_context HTTP/1.1" 200 -
[2025-08-12 15:36:39,001] INFO in player: Executing action: {'action_id': 'XEbZHdi0GT', 'executionTime': '103ms', 'package_id': 'au.com.kmart', 'timestamp': 1749445093180, 'type': 'terminateApp', 'test_idx': 0}
[2025-08-12 15:36:39,026] INFO in app: Using directories from config.py:
[2025-08-12 15:36:39,026] INFO in app:   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/android_data/test_cases
[2025-08-12 15:36:39,026] INFO in app:   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/android_data/reference_images
[2025-08-12 15:36:39,026] INFO in app:   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
[2025-08-12 15:36:39,043] INFO in appium_device_controller: Terminating app: au.com.kmart
[2025-08-12 15:36:39,043] INFO in appium_device_controller: Using Appium to terminate Android app: au.com.kmart
[2025-08-12 15:36:39,702] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:39,703] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:39,703] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:40,668] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:36:40,668] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153640.png (save_debug=False)
[2025-08-12 15:36:40,668] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:36:41,487] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153640.png
[2025-08-12 15:36:41,488] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:41,488] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153640.png
[2025-08-12 15:36:41,501] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:36:41,501] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:36:42,122] INFO in appium_device_controller: Launching app: au.com.kmart
[2025-08-12 15:36:42,122] INFO in appium_device_controller: Using Appium to launch Android app: au.com.kmart
[2025-08-12 15:36:42,351] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:42,352] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:42,352] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:43,428] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:36:43,428] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153643.png (save_debug=False)
[2025-08-12 15:36:43,429] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:36:43,928] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153643.png
[2025-08-12 15:36:43,930] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:43,930] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153643.png
[2025-08-12 15:36:43,940] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:36:43,940] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:36:44,466] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:44,467] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:44,467] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:44,566] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-12 15:36:47,381] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:36:47,381] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:36:48,224] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-12 15:36:48,349] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:48,350] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:48,350] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:51,660] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:36:51,660] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153651.png (save_debug=False)
[2025-08-12 15:36:51,661] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:36:52,293] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153651.png
[2025-08-12 15:36:52,294] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:52,294] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153651.png
[2025-08-12 15:36:52,307] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:36:52,307] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:36:52,938] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:52,939] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:52,939] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:52,943] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:36:52,943] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:36:53,628] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:53,630] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:53,630] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:57,280] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:36:57,280] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153657.png (save_debug=False)
[2025-08-12 15:36:57,280] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:36:58,019] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153657.png
[2025-08-12 15:36:58,020] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:58,021] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153657.png
[2025-08-12 15:36:58,035] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:36:58,035] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:36:58,748] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:58,749] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:58,749] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:58,753] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:36:58,753] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:36:58,973] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-12 15:36:59,391] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:36:59,392] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:36:59,392] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:01,092] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-12 15:37:02,176] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:37:02,176] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153702.png (save_debug=False)
[2025-08-12 15:37:02,177] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:02,870] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153702.png
[2025-08-12 15:37:02,871] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:02,871] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153702.png
[2025-08-12 15:37:02,882] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:02,882] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:03,629] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:03,630] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:03,630] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:03,634] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:03,634] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:03,977] INFO in appium_device_controller: Inputting text '<EMAIL>' directly (no locator)
[2025-08-12 15:37:03,977] INFO in appium_device_controller: Skipping keyboard management to prevent session termination
[2025-08-12 15:37:04,337] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:04,337] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:04,338] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:06,293] DEBUG in appium_device_controller: Could not hide keyboard (may not be visible): Message: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-08-12 15:37:06,293] INFO in appium_device_controller: Using UIAutomator2 helper for direct text input: '<EMAIL>'
[2025-08-12 15:37:07,587] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:37:07,587] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153707.png (save_debug=False)
[2025-08-12 15:37:07,587] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:08,345] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153707.png
[2025-08-12 15:37:08,346] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:08,346] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153707.png
[2025-08-12 15:37:08,356] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:08,357] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:08,991] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-12 15:37:09,143] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:09,144] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:09,144] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:09,149] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:09,149] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:09,235] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-12 15:37:09,858] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:09,859] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:09,859] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:10,317] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:37:10,317] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153710.png (save_debug=False)
[2025-08-12 15:37:10,318] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:10,905] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153710.png
[2025-08-12 15:37:10,906] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:10,906] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153710.png
[2025-08-12 15:37:10,918] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:10,919] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:11,659] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:11,660] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:11,660] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:11,665] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:11,665] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:11,972] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-12 15:37:12,431] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:12,432] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:12,432] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:39,100] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-12 15:37:40,279] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:37:40,279] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153740.png (save_debug=False)
[2025-08-12 15:37:40,280] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:41,025] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153740.png
[2025-08-12 15:37:41,026] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:41,026] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153740.png
[2025-08-12 15:37:41,039] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:41,039] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:41,661] INFO in appium_device_controller: Inputting text 'Wonderbaby@6' directly (no locator)
[2025-08-12 15:37:41,662] INFO in appium_device_controller: Skipping keyboard management to prevent session termination
[2025-08-12 15:37:41,769] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:41,769] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:41,770] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:41,773] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:41,774] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:42,492] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:42,492] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:42,492] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:43,949] DEBUG in appium_device_controller: Could not hide keyboard (may not be visible): Message: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-08-12 15:37:43,950] INFO in appium_device_controller: Using UIAutomator2 helper for direct text input: 'Wonderbaby@6'
[2025-08-12 15:37:45,166] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:37:45,166] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153745.png (save_debug=False)
[2025-08-12 15:37:45,166] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:45,868] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153745.png
[2025-08-12 15:37:45,869] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:45,869] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153745.png
[2025-08-12 15:37:45,882] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:45,882] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:46,515] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-12 15:37:46,638] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:46,639] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:46,639] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:46,643] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:46,644] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:46,768] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-12 15:37:47,291] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:47,292] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:47,292] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:47,857] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:37:47,858] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153747.png (save_debug=False)
[2025-08-12 15:37:47,858] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:48,671] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153747.png
[2025-08-12 15:37:48,671] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:48,672] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153747.png
[2025-08-12 15:37:48,684] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:48,684] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:49,184] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:49,184] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:49,331] INFO in appium_device_controller: Terminating app: com.android.settings
[2025-08-12 15:37:49,332] INFO in appium_device_controller: Using Appium to terminate Android app: com.android.settings
[2025-08-12 15:37:49,670] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to native Appium
[2025-08-12 15:37:49,671] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 15:37:49,848] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to native Appium
[2025-08-12 15:37:49,848] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 15:37:50,703] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:37:50,703] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153750.png (save_debug=False)
[2025-08-12 15:37:50,704] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:51,204] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:51,205] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:51,563] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153750.png
[2025-08-12 15:37:51,565] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:51,565] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153750.png
[2025-08-12 15:37:51,577] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:51,578] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:51,825] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:51,825] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:52,192] INFO in appium_device_controller: Launching app: com.android.settings
[2025-08-12 15:37:52,192] INFO in appium_device_controller: Using Appium to launch Android app: com.android.settings
[2025-08-12 15:37:52,389] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:52,390] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:52,390] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:52,394] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:52,394] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:53,379] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:37:53,380] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153753.png (save_debug=False)
[2025-08-12 15:37:53,380] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:53,392] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:53,393] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:53,393] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:53,922] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153753.png
[2025-08-12 15:37:53,923] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:53,923] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153753.png
[2025-08-12 15:37:53,935] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:53,935] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:54,448] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:54,449] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:54,449] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:54,453] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:54,453] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:54,562] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-12 15:37:54,716] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-12 15:37:55,135] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:55,136] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:55,136] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:55,785] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:37:55,785] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153755.png (save_debug=False)
[2025-08-12 15:37:55,785] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:56,772] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153755.png
[2025-08-12 15:37:56,773] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:56,773] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153755.png
[2025-08-12 15:37:56,785] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:56,786] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:57,587] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:57,588] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:57,588] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:57,592] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:37:57,592] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:37:58,571] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:37:58,571] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:37:58,571] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:03,491] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:38:03,492] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153803.png (save_debug=False)
[2025-08-12 15:38:03,492] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:04,193] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153803.png
[2025-08-12 15:38:04,194] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:04,194] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153803.png
[2025-08-12 15:38:04,203] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:04,203] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:04,824] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-12 15:38:04,999] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:04,999] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:04,999] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:05,003] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:05,003] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:05,744] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:05,745] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:05,745] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:06,714] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-12 15:38:08,330] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:38:08,331] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153808.png (save_debug=False)
[2025-08-12 15:38:08,331] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:09,439] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153808.png
[2025-08-12 15:38:09,440] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:09,440] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153808.png
[2025-08-12 15:38:09,539] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:09,539] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:10,299] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:10,299] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:10,299] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:10,303] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:10,304] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:11,086] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:11,086] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:11,087] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:16,074] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:38:16,075] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153816.png (save_debug=False)
[2025-08-12 15:38:16,075] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:16,822] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153816.png
[2025-08-12 15:38:16,823] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:16,823] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153816.png
[2025-08-12 15:38:16,835] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:16,835] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:17,479] INFO in appium_device_controller: Terminating app: au.com.kmart
[2025-08-12 15:38:17,480] INFO in appium_device_controller: Using Appium to terminate Android app: au.com.kmart
[2025-08-12 15:38:17,603] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:17,603] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:17,604] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:17,608] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:17,608] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:18,268] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:18,269] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:18,269] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:18,913] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:38:18,914] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153818.png (save_debug=False)
[2025-08-12 15:38:18,914] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:19,671] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153818.png
[2025-08-12 15:38:19,671] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:19,671] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153818.png
[2025-08-12 15:38:19,681] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:19,682] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:20,295] INFO in appium_device_controller: Launching app: au.com.kmart
[2025-08-12 15:38:20,295] INFO in appium_device_controller: Using Appium to launch Android app: au.com.kmart
[2025-08-12 15:38:20,452] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:20,453] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:20,454] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:20,458] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:20,459] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:21,181] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:21,182] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:21,182] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:21,613] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:38:21,614] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153821.png (save_debug=False)
[2025-08-12 15:38:21,614] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:22,079] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153821.png
[2025-08-12 15:38:22,080] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:22,080] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153821.png
[2025-08-12 15:38:22,090] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:22,090] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:22,576] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:22,577] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:22,577] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:22,582] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:22,582] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:22,729] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-12 15:38:23,257] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:23,258] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:23,258] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:36,704] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-12 15:38:37,711] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:38:37,711] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153837.png (save_debug=False)
[2025-08-12 15:38:37,711] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:38,486] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153837.png
[2025-08-12 15:38:38,487] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:38,487] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153837.png
[2025-08-12 15:38:40,638] INFO in appium_device_controller: Terminating app: au.com.kmart
[2025-08-12 15:38:40,638] INFO in appium_device_controller: Using Appium to terminate Android app: au.com.kmart
[2025-08-12 15:38:42,091] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:38:42,092] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153842.png (save_debug=False)
[2025-08-12 15:38:42,092] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:42,848] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153842.png
[2025-08-12 15:38:42,850] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:42,850] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153842.png
[2025-08-12 15:38:42,860] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:42,860] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:43,490] INFO in appium_device_controller: Launching app: au.com.kmart
[2025-08-12 15:38:43,490] INFO in appium_device_controller: Using Appium to launch Android app: au.com.kmart
[2025-08-12 15:38:43,576] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:43,577] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:43,577] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:43,581] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:43,581] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:44,246] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:44,247] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:44,248] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:44,804] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:38:44,805] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153844.png (save_debug=False)
[2025-08-12 15:38:44,805] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:45,284] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_153844.png
[2025-08-12 15:38:45,285] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:45,285] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_153844.png
[2025-08-12 15:38:45,296] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:45,296] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:45,821] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:45,822] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:45,822] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:45,825] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:38:45,825] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:38:45,937] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-12 15:38:46,500] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:38:46,502] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:38:46,502] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:39:38,978] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-12 15:41:38,949] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-12 15:43:01,011] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-12 15:43:01,011] INFO in appium_device_controller: Tapping on element with xpath='//android.widget.Button[@content-desc="txtHomeAccountCtaSignIn"]' (timeout=10s, interval=0.5s)
[2025-08-12 15:43:01,011] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//android.widget.Button[@content-desc="txtHomeAccountCtaSignIn"]'
[2025-08-12 15:43:38,984] WARNING in appium_device_controller: Element not clickable within timeout: xpath='//android.widget.Button[@content-desc="txtHomeAccountCtaSignIn"]'
[2025-08-12 15:43:38,984] INFO in appium_device_controller: Trying to find element even if not clickable: xpath='//android.widget.Button[@content-desc="txtHomeAccountCtaSignIn"]'
[2025-08-12 15:44:01,048] WARNING in appium_device_controller: Could not find or tap on element: Message: 
Stacktrace:
NoSuchElementError: An element could not be located on the page using the given search parameters.
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:86:11)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-uiautomator2-driver/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElement (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-uiautomator2-driver/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:75:12)
[2025-08-12 15:44:02,062] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:44:02,063] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_154402.png (save_debug=False)
[2025-08-12 15:44:02,063] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:44:02,852] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_154402.png
[2025-08-12 15:44:02,853] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:44:02,853] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_154402.png
[2025-08-12 15:44:05,002] INFO in appium_device_controller: Terminating app: au.com.kmart
[2025-08-12 15:44:05,003] INFO in appium_device_controller: Using Appium to terminate Android app: au.com.kmart
[2025-08-12 15:44:06,477] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:44:06,477] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_154406.png (save_debug=False)
[2025-08-12 15:44:06,477] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:44:07,867] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to native Appium
[2025-08-12 15:44:07,868] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 15:44:08,396] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:44:08,396] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_154406.png
[2025-08-12 15:44:08,407] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:44:08,408] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:44:09,031] INFO in appium_device_controller: Launching app: au.com.kmart
[2025-08-12 15:44:09,031] INFO in appium_device_controller: Using Appium to launch Android app: au.com.kmart
[2025-08-12 15:44:09,134] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:44:09,135] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:44:09,135] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:44:09,138] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:44:09,138] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:44:09,846] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:44:09,847] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:44:09,847] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:44:10,341] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots
[2025-08-12 15:44:10,341] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_154410.png (save_debug=False)
[2025-08-12 15:44:10,341] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:44:10,823] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/screenshot_20250812_154410.png
[2025-08-12 15:44:10,824] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:44:10,824] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_154410.png
[2025-08-12 15:44:10,834] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:44:10,834] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:44:11,321] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:44:11,321] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:44:11,322] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:44:11,326] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-12 15:44:11,326] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-12 15:44:11,455] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-12 15:44:12,013] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:44:12,013] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250812_153638/screenshots/latest.png
[2025-08-12 15:44:12,013] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-12 15:44:38,995] INFO in appium_device_controller: Health checks suspended during session refresh
