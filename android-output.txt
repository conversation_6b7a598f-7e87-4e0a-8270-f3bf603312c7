2025-08-12 18:08:21,947 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 18:08:21,948 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 18:08:21,948 - app_android.config_android - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 18:08:21,949 - app_android.config_android - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android
2025-08-12 18:08:21,949 - app_android.config_android - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_android
2025-08-12 18:08:21,950 - app_android.config_android - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-08-12 18:08:21,950 - app_android.config_android - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-12 18:08:21,951 - app_android.config_android - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android/suites
2025-08-12 18:08:21,951 - app_android.config_android - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_android
2025-08-12 18:08:21,952 - app_android.config_android - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android
2025-08-12 18:08:21,952 - app_android.config_android - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/files_to_push
2025-08-12 18:08:21,952 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-08-12 18:08:21,953 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4724, WDA: 8300) - preserving existing processes for multi-instance support
2025-08-12 18:08:21,953 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-08-12 18:08:23,006 - app_android.utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
2025-08-12 18:08:23,006 - app_android.utils.global_values_db - INFO - Global values database initialized successfully
2025-08-12 18:08:23,007 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 18:08:23,007 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 18:08:23,008 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/android_data/reports
2025-08-12 18:08:23,008 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
2025-08-12 18:08:23,008 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-12 18:08:23,009 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-12 18:08:23,009 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/suites
2025-08-12 18:08:23,009 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_ios
2025-08-12 18:08:23,010 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_ios
2025-08-12 18:08:23,010 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-08-12 18:08:23,010 - app_android.utils.global_values_db - INFO - Using global values from config.py
2025-08-12 18:08:23,010 - app_android.utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-08-12 18:08:23,012 - app_android.utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-08-12 18:08:23,014 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-08-12 18:08:23,044 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-08-12 18:08:23,395 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 18:08:23,464 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-08-12 18:08:23,464 - utils.database - INFO - Test_steps table schema updated successfully
2025-08-12 18:08:23,464 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-08-12 18:08:23,465 - utils.database - INFO - Screenshots table schema updated successfully
2025-08-12 18:08:23,465 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-08-12 18:08:23,465 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-08-12 18:08:23,465 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-08-12 18:08:23,465 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-08-12 18:08:23,465 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-08-12 18:08:23,465 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-08-12 18:08:23,465 - utils.database - INFO - Database initialized successfully
2025-08-12 18:08:23,465 - utils.database - INFO - Checking initial database state...
2025-08-12 18:08:23,466 - utils.database - INFO - Database state: 0 suites, 0 cases, 1147 steps, 0 screenshots, 1273 tracking entries
2025-08-12 18:08:23,478 - app - INFO - Using directories from config.py:
2025-08-12 18:08:23,478 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 18:08:23,478 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-12 18:08:23,479 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
[2025-08-12 18:08:23,483] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-12 18:08:23,484] INFO in database: Test_steps table schema updated successfully
[2025-08-12 18:08:23,484] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 18:08:23,485] INFO in database: Screenshots table schema updated successfully
[2025-08-12 18:08:23,485] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 18:08:23,485] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 18:08:23,485] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 18:08:23,485] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 18:08:23,485] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 18:08:23,485] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 18:08:23,485] INFO in database: Database initialized successfully
[2025-08-12 18:08:23,485] INFO in database: Checking initial database state...
[2025-08-12 18:08:23,486] INFO in database: Database state: 0 suites, 0 cases, 1147 steps, 0 screenshots, 1273 tracking entries
[2025-08-12 18:08:23,488] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-12 18:08:23,488] INFO in database: Test_steps table schema updated successfully
[2025-08-12 18:08:23,488] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 18:08:23,489] INFO in database: Screenshots table schema updated successfully
[2025-08-12 18:08:23,489] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 18:08:23,489] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 18:08:23,490] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 18:08:23,490] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 18:08:23,490] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 18:08:23,490] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 18:08:23,490] INFO in database: Database initialized successfully
[2025-08-12 18:08:23,490] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-12 18:08:23,490] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-12 18:08:23,490] INFO in database: action_type column already exists in execution_tracking table
[2025-08-12 18:08:23,491] INFO in database: action_params column already exists in execution_tracking table
[2025-08-12 18:08:23,491] INFO in database: action_id column already exists in execution_tracking table
[2025-08-12 18:08:23,491] INFO in database: Successfully updated execution_tracking table schema
[2025-08-12 18:08:23,491] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-12 18:08:23,491] INFO in database: Screenshots table schema updated successfully
[2025-08-12 18:08:23,562] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4724, WDA port: 8200
[2025-08-12 18:08:23,573] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4724): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x120729400>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-08-12 18:08:23,573] INFO in appium_device_controller: Using custom ports (Appium: 4724, WDA: 8200) - preserving existing processes for multi-instance support
[2025-08-12 18:08:23,574] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-08-12 18:08:23,574] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
