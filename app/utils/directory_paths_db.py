import os
import sqlite3
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class DirectoryPathsDB:
    """
    Database handler for storing directory paths configuration
    Stores paths for:
    - Test Cases
    - Reference Images
    - Test Suites
    - Reports
    - Files to Push
    """

    def __init__(self):
        # Get platform and instance suffixes for database isolation
        instance_suffix = os.environ.get('INSTANCE_DB_SUFFIX', '')
        platform_suffix = '_ios'
        combined_suffix = f"{platform_suffix}{instance_suffix}"
        
        if combined_suffix:
            db_filename = f"settings{combined_suffix}.db"
        else:
            db_filename = "settings_ios.db"
            
        self.db_path = os.path.join('data', db_filename)
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        # Create the table if it doesn't exist
        self._init_db()

    def _init_db(self):
        """Initialize the database with the required tables"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute('''
            CREATE TABLE IF NOT EXISTS directory_paths (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE,
                path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Create environments table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS environments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Create environment_variables table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS environment_variables (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                environment_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                type TEXT DEFAULT 'default',
                initial_value TEXT,
                current_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(environment_id) REFERENCES environments(id) ON DELETE CASCADE,
                UNIQUE(environment_id, name)
            )
            ''')

            # Create active_environment table for persistent environment state
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS active_environment (
                id INTEGER PRIMARY KEY CHECK (id = 1),
                environment_id INTEGER,
                session_id TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(environment_id) REFERENCES environments(id) ON DELETE SET NULL
            )
            ''')

            conn.commit()
            conn.close()
            logger.info("Directory paths and environments database initialized/verified")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")

    def set_active_environment(self, environment_id, session_id=None):
        """Set the active environment in the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Insert or update the active environment
            cursor.execute('''
                INSERT OR REPLACE INTO active_environment (id, environment_id, session_id, updated_at)
                VALUES (1, ?, ?, CURRENT_TIMESTAMP)
            ''', (environment_id, session_id))

            conn.commit()
            conn.close()
            logger.info(f"Set active environment to ID {environment_id} for session {session_id}")
            return True
        except Exception as e:
            logger.error(f"Error setting active environment: {str(e)}")
            return False

    def get_active_environment(self, session_id=None):
        """Get the active environment from the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get the active environment
            cursor.execute('SELECT environment_id FROM active_environment WHERE id = 1')
            result = cursor.fetchone()

            conn.close()

            if result:
                environment_id = result[0]
                logger.debug(f"Retrieved active environment ID {environment_id} from database")
                return environment_id
            else:
                logger.debug("No active environment found in database")
                return None
        except Exception as e:
            logger.error(f"Error getting active environment: {str(e)}")
            return None

    def save_path(self, name, path):
        """
        Save a directory path to the database

        Args:
            name: The name/key of the directory (e.g., 'TEST_CASES')
            path: The path to the directory

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            # Check if the path already exists
            cursor.execute("SELECT id FROM directory_paths WHERE name = ?", (name,))
            existing = cursor.fetchone()

            if existing:
                # Update existing path
                cursor.execute(
                    "UPDATE directory_paths SET path = ?, updated_at = CURRENT_TIMESTAMP WHERE name = ?",
                    (path, name)
                )
            else:
                # Insert new path
                cursor.execute(
                    "INSERT INTO directory_paths (name, path) VALUES (?, ?)",
                    (name, path)
                )

            conn.commit()
            conn.close()
            logger.info(f"Directory path saved: {name} = {path}")
            return True
        except Exception as e:
            logger.error(f"Error saving directory path: {str(e)}")
            return False

    def get_path(self, name, default=None):
        """
        Get a directory path from the database

        Args:
            name: The name/key of the directory (e.g., 'TEST_CASES')
            default: Default value to return if the path is not found

        Returns:
            str: The path, or the default value if not found
        """
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT path FROM directory_paths WHERE name = ?", (name,))
            result = cursor.fetchone()

            conn.close()

            if result:
                return result[0]
            return default
        except Exception as e:
            logger.error(f"Error getting directory path: {str(e)}")
            return default

    def get_all_paths(self):
        """
        Get all directory paths from the database

        Returns:
            dict: Dictionary of all directory paths
        """
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT name, path FROM directory_paths")
            results = cursor.fetchall()

            conn.close()

            return {name: path for name, path in results}
        except Exception as e:
            logger.error(f"Error getting all directory paths: {str(e)}")
            return {}

    def save_all_paths(self, paths_dict):
        """
        Save multiple directory paths to the database

        Args:
            paths_dict: Dictionary of directory paths {name: path}

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Attempting to save all paths to database: {paths_dict}")
            if not paths_dict:
                logger.warning("Empty paths dictionary provided to save_all_paths")
                return False

            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            for name, path in paths_dict.items():
                logger.info(f"Processing path: {name} = {path}")
                # Check if the path already exists
                cursor.execute("SELECT id FROM directory_paths WHERE name = ?", (name,))
                existing = cursor.fetchone()

                if existing:
                    # Update existing path
                    logger.info(f"Updating existing path for {name}: {path}")
                    cursor.execute(
                        "UPDATE directory_paths SET path = ?, updated_at = CURRENT_TIMESTAMP WHERE name = ?",
                        (path, name)
                    )
                else:
                    # Insert new path
                    logger.info(f"Inserting new path for {name}: {path}")
                    cursor.execute(
                        "INSERT INTO directory_paths (name, path) VALUES (?, ?)",
                        (name, path)
                    )

            conn.commit()
            conn.close()
            logger.info(f"All directory paths saved successfully: {paths_dict}")
            return True
        except Exception as e:
            logger.error(f"Error saving all directory paths: {str(e)}")
            return False

    def count_directory_contents(self, name):
        """
        Count the number of items in a directory

        Args:
            name: The name of the directory in the database

        Returns:
            dict: Dictionary with counts of different file types
        """
        try:
            directory_path = self.get_path(name)
            if not directory_path:
                return {"error": f"Directory path for {name} not found in database"}

            path = Path(directory_path)
            if not path.exists():
                return {"error": f"Directory {directory_path} does not exist"}

            if not path.is_dir():
                return {"error": f"{directory_path} is not a directory"}

            counts = {}

            # Count based on directory type
            if name == "TEST_CASES":
                json_files = list(path.glob("**/*.json"))
                counts["test_cases"] = len(json_files)

            elif name == "REFERENCE_IMAGES":
                image_files = list(path.glob("**/*.png")) + list(path.glob("**/*.jpg")) + list(path.glob("**/*.jpeg"))
                counts["images"] = len(image_files)

            elif name == "TEST_SUITES":
                json_files = list(path.glob("**/*.json"))
                counts["test_suites"] = len(json_files)

            elif name == "REPORTS":
                html_files = list(path.glob("**/*.html"))
                counts["reports"] = len(html_files)

            elif name == "FILES_TO_PUSH":
                all_files = list(path.glob("**/*.*"))
                counts["files"] = len(all_files)

            # Also count total files as a fallback
            all_files = list(path.glob("**/*.*"))
            counts["total_files"] = len(all_files)

            return counts
        except Exception as e:
            logger.error(f"Error counting directory contents: {str(e)}")
            return {"error": str(e)}

    # --- Environment Methods ---
    def create_environment(self, name):
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute("INSERT INTO environments (name) VALUES (?)", (name,))
            env_id = cursor.lastrowid
            conn.commit()
            conn.close()
            logger.info(f"Environment '{name}' created with ID {env_id}.")
            return env_id
        except sqlite3.IntegrityError:
            logger.warning(f"Environment '{name}' already exists.")
            conn.close()
            return None
        except Exception as e:
            logger.error(f"Error creating environment '{name}': {str(e)}")
            conn.close()
            return None

    def get_all_environments(self):
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT id, name, created_at, updated_at FROM environments ORDER BY name")
            environments = [{"id": row[0], "name": row[1], "created_at": row[2], "updated_at": row[3]} for row in cursor.fetchall()]
            conn.close()
            return environments
        except Exception as e:
            logger.error(f"Error getting all environments: {str(e)}")
            conn.close()
            return []

    def get_environment_by_name(self, name):
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT id, name FROM environments WHERE name = ?", (name,))
            row = cursor.fetchone()
            conn.close()
            return {"id": row[0], "name": row[1]} if row else None
        except Exception as e:
            logger.error(f"Error getting environment by name '{name}': {str(e)}")
            conn.close()
            return None
            
    def get_environment_by_id(self, env_id):
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT id, name FROM environments WHERE id = ?", (env_id,))
            row = cursor.fetchone()
            conn.close()
            return {"id": row[0], "name": row[1]} if row else None
        except Exception as e:
            logger.error(f"Error getting environment by ID {env_id}: {str(e)}")
            conn.close()
            return None

    def update_environment_name(self, env_id, new_name):
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute("UPDATE environments SET name = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?", (new_name, env_id))
            conn.commit()
            updated_rows = cursor.rowcount
            conn.close()
            if updated_rows > 0:
                logger.info(f"Environment ID {env_id} updated to name '{new_name}'.")
                return True
            logger.warning(f"Environment ID {env_id} not found for update.")
            return False
        except sqlite3.IntegrityError:
            logger.warning(f"Environment name '{new_name}' already exists.")
            conn.close()
            return False
        except Exception as e:
            logger.error(f"Error updating environment ID {env_id}: {str(e)}")
            conn.close()
            return False

    def delete_environment(self, env_id):
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            # ON DELETE CASCADE should handle variables
            cursor.execute("DELETE FROM environments WHERE id = ?", (env_id,))
            conn.commit()
            deleted_rows = cursor.rowcount
            conn.close()
            if deleted_rows > 0:
                logger.info(f"Environment ID {env_id} and its variables deleted.")
                return True
            logger.warning(f"Environment ID {env_id} not found for deletion.")
            return False
        except Exception as e:
            logger.error(f"Error deleting environment ID {env_id}: {str(e)}")
            conn.close()
            return False

    # --- Environment Variable Methods ---
    def add_environment_variable(self, env_id, name, var_type="default", initial_value="", current_value=""):
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO environment_variables (environment_id, name, type, initial_value, current_value) VALUES (?, ?, ?, ?, ?)",
                (env_id, name, var_type, initial_value, current_value)
            )
            var_id = cursor.lastrowid
            conn.commit()
            conn.close()
            logger.info(f"Variable '{name}' added to environment ID {env_id} with ID {var_id}.")
            return var_id
        except sqlite3.IntegrityError:
            logger.warning(f"Variable '{name}' already exists in environment ID {env_id}.")
            conn.close()
            return None
        except Exception as e:
            logger.error(f"Error adding variable '{name}' to environment ID {env_id}: {str(e)}")
            conn.close()
            return None

    def get_variables_for_environment(self, env_id):
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute(
                "SELECT id, name, type, initial_value, current_value, created_at, updated_at FROM environment_variables WHERE environment_id = ? ORDER BY name",
                (env_id,)
            )
            variables = [
                {"id": row[0], "name": row[1], "type": row[2], "initial_value": row[3], "current_value": row[4], "created_at": row[5], "updated_at": row[6]}
                for row in cursor.fetchall()
            ]
            conn.close()
            return variables
        except Exception as e:
            logger.error(f"Error getting variables for environment ID {env_id}: {str(e)}")
            conn.close()
            return []

    def update_environment_variable(self, var_id, name, var_type, initial_value, current_value):
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            # Check for potential unique constraint violation before updating
            cursor.execute("SELECT environment_id FROM environment_variables WHERE id = ?", (var_id,))
            result = cursor.fetchone()
            if not result:
                logger.warning(f"Variable ID {var_id} not found for update.")
                conn.close()
                return False
            env_id = result[0]

            cursor.execute(
                "SELECT id FROM environment_variables WHERE environment_id = ? AND name = ? AND id != ?",
                (env_id, name, var_id)
            )
            if cursor.fetchone():
                logger.warning(f"Another variable named '{name}' already exists in environment ID {env_id}.")
                conn.close()
                return False # Or raise a specific error

            cursor.execute(
                "UPDATE environment_variables SET name = ?, type = ?, initial_value = ?, current_value = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (name, var_type, initial_value, current_value, var_id)
            )
            conn.commit()
            updated_rows = cursor.rowcount
            conn.close()
            if updated_rows > 0:
                logger.info(f"Variable ID {var_id} updated.")
                return True
            # This case should ideally be caught by the earlier check, but as a safeguard:
            logger.warning(f"Variable ID {var_id} not found for update (or no changes made).")
            return False
        except sqlite3.IntegrityError: # Should be caught by pre-check, but good to have
             logger.warning(f"Integrity error updating variable ID {var_id} - likely duplicate name for environment.")
             conn.close()
             return False
        except Exception as e:
            logger.error(f"Error updating variable ID {var_id}: {str(e)}")
            conn.close()
            return False

    def delete_environment_variable(self, var_id):
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute("DELETE FROM environment_variables WHERE id = ?", (var_id,))
            conn.commit()
            deleted_rows = cursor.rowcount
            conn.close()
            if deleted_rows > 0:
                logger.info(f"Variable ID {var_id} deleted.")
                return True
            logger.warning(f"Variable ID {var_id} not found for deletion.")
            return False
        except Exception as e:
            logger.error(f"Error deleting variable ID {var_id}: {str(e)}")
            conn.close()
            return False

# Create singleton instance
directory_paths_db = DirectoryPathsDB()