from .base_action import BaseAction
from airtest.core.api import Template, wait
from airtest.core.error import TargetNotFoundError
import os
import time
import traceback

class TapIfImageExistsAction(BaseAction):
    """Handler for tap if image exists actions - EXACT copy of TapAction logic"""

    def __init__(self, controller=None):
        super().__init__(controller)
        self.action_type = 'tapIfImageExists'  # Identify as conditional action

    def scale_ios_coordinates(self, coordinates):
        """
        Scale coordinates for iOS devices if needed (EXACT same as TapAction)

        Args:
            coordinates: Tuple of (x, y) coordinates

        Returns:
            tuple: Scaled coordinates
        """
        try:
            x, y = coordinates

            # Get device dimensions
            if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                device_width = self.controller.device_dimensions.get('width')
                device_height = self.controller.device_dimensions.get('height')

                if device_width and device_height:
                    # Check if we need to scale (if image dimensions don't match device dimensions)
                    if hasattr(self.controller, 'airtest_device') and self.controller.airtest_device:
                        try:
                            # Get the screen resolution from Airtest device
                            airtest_resolution = self.controller.airtest_device.get_current_resolution()
                            if airtest_resolution and len(airtest_resolution) == 2:
                                airtest_width, airtest_height = airtest_resolution

                                # Only scale if dimensions are different
                                if airtest_width != device_width or airtest_height != device_height:
                                    scale_x = device_width / airtest_width
                                    scale_y = device_height / airtest_height

                                    self.logger.info(f"Scaling coordinates by factors: x={scale_x}, y={scale_y}")
                                    return (int(x * scale_x), int(y * scale_y))
                        except Exception as e:
                            self.logger.warning(f"Error getting Airtest resolution: {e}")

            # Return original coordinates if no scaling needed or if scaling failed
            return coordinates
        except Exception as e:
            self.logger.warning(f"Error in scale_ios_coordinates: {e}")
            return coordinates
    def execute(self, params):
        """
        Execute tap if image exists action - EXACT copy of TapAction image method logic
        The only difference: returns success when image is not found instead of error

        Args:
            params: Dictionary containing:
                - image_filename: Reference image to tap on
                - threshold: (Optional) Matching threshold (default: 0.7)
                - timeout: (Optional) Maximum time to wait in seconds (default: 5)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            # Always return success for "if exists" behavior
            return {"status": "success", "message": "No device controller available - step passed (if exists behavior)"}

        # Extract parameters with optimized timeout (max 30 seconds)
        image_filename = params.get('image_filename')
        threshold = float(params.get('threshold', 0.7))
        timeout = min(int(params.get('timeout', 30)), 30)  # Max 30 seconds

        if not image_filename:
            # Always return success for "if exists" behavior
            return {"status": "success", "message": "Image filename is required - step passed (if exists behavior)"}

        self.logger.info(f"Optimized tap if image exists: {image_filename} (threshold: {threshold}, timeout: {timeout}s)")

        # Simple image path resolution
        abs_path = image_filename
        if not os.path.exists(image_filename):
            # Try reference_images folder
            ref_path = os.path.join('reference_images', os.path.basename(image_filename))
            if os.path.exists(ref_path):
                abs_path = ref_path
            else:
                self.logger.info(f"Reference image not found: {image_filename} - step passed (if exists behavior)")
                return {"status": "success", "message": f"Reference image not found - step passed (if exists behavior)"}

        # Simple, fast image detection with timeout (iOS-style approach)
        try:
            template_image = Template(abs_path, threshold=threshold)

            # Use simple wait with timeout
            start_time = time.time()
            try:
                wait_result = wait(template_image, timeout=timeout)
                if wait_result:
                    self.logger.info(f"Image found: {image_filename}, attempting to tap...")
                    # Simple tap using Airtest
                    from airtest.core.api import touch
                    touch(template_image)
                    return {"status": "success", "message": f"Tapped on image: {image_filename}"}

            except TargetNotFoundError:
                # Image not found - expected behavior for "if exists"
                elapsed_time = time.time() - start_time
                self.logger.info(f"Image not found within {timeout}s - step passed (if exists behavior)")
                return {"status": "success", "message": f"Image not found within {timeout}s - step passed (if exists behavior)"}

        except Exception as e:
            self.logger.warning(f"Image detection error: {e}")
            return {"status": "success", "message": f"Image detection error - step passed (if exists behavior): {str(e)}"}


