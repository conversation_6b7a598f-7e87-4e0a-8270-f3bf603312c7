#!/usr/bin/env python3
"""
Test script for Android Element Identification Improvements
Validates the enhanced element finding capabilities and performance optimizations
"""

import sys
import os
import time
import logging
from typing import Dict, Any, List

# Add the app_android directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
app_android_dir = os.path.dirname(current_dir)
if app_android_dir not in sys.path:
    sys.path.insert(0, app_android_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ElementIdentificationTester:
    """
    Test suite for validating element identification improvements
    """
    
    def __init__(self):
        self.test_results = []
        self.performance_metrics = {}
        
    def run_all_tests(self) -> Dict[str, Any]:
        """
        Run all element identification tests
        
        Returns:
            dict: Test results and performance metrics
        """
        logger.info("Starting Element Identification Improvement Tests")
        
        tests = [
            self.test_enhanced_finder_availability,
            self.test_healenium_integration,
            self.test_performance_config,
            self.test_fallback_strategies,
            self.test_adaptive_timeouts,
            self.test_screenshot_optimization,
            self.test_session_management
        ]
        
        for test in tests:
            try:
                result = test()
                self.test_results.append(result)
                logger.info(f"✓ {result['test_name']}: {result['status']}")
            except Exception as e:
                error_result = {
                    'test_name': test.__name__,
                    'status': 'FAILED',
                    'error': str(e),
                    'timestamp': time.time()
                }
                self.test_results.append(error_result)
                logger.error(f"✗ {test.__name__}: FAILED - {e}")
        
        return self._generate_test_report()
    
    def test_enhanced_finder_availability(self) -> Dict[str, Any]:
        """Test if enhanced element finder is available and functional"""
        try:
            from app_android.utils.enhanced_element_finder import EnhancedElementFinder
            
            # Test initialization without controller (should not fail)
            finder = EnhancedElementFinder(None)
            
            # Test performance stats
            stats = finder.get_performance_stats()
            assert isinstance(stats, dict)
            assert 'search_attempts' in stats
            
            return {
                'test_name': 'Enhanced Finder Availability',
                'status': 'PASSED',
                'details': 'Enhanced element finder is available and functional',
                'timestamp': time.time()
            }
            
        except ImportError as e:
            return {
                'test_name': 'Enhanced Finder Availability',
                'status': 'FAILED',
                'error': f'Enhanced finder not available: {e}',
                'timestamp': time.time()
            }
    
    def test_healenium_integration(self) -> Dict[str, Any]:
        """Test Healenium integration improvements"""
        try:
            from app_android.utils.healenium_config import HealeniumConfig
            from app_android.utils.healenium_wrapper import create_healenium_driver
            
            # Test configuration loading
            config = HealeniumConfig()
            assert hasattr(config, 'healenium_enabled')
            assert hasattr(config, 'check_healenium_health')
            
            # Test health check method
            health_method = getattr(config, 'check_healenium_health', None)
            assert callable(health_method)
            
            return {
                'test_name': 'Healenium Integration',
                'status': 'PASSED',
                'details': 'Healenium integration is properly configured',
                'timestamp': time.time()
            }
            
        except Exception as e:
            return {
                'test_name': 'Healenium Integration',
                'status': 'FAILED',
                'error': f'Healenium integration issue: {e}',
                'timestamp': time.time()
            }
    
    def test_performance_config(self) -> Dict[str, Any]:
        """Test performance configuration improvements"""
        try:
            from app_android.config.performance_config import performance_config
            
            # Test screenshot optimization settings
            assert hasattr(performance_config, 'screenshot_frequency_limit')
            assert hasattr(performance_config, 'screenshot_skip_during_element_search')
            assert hasattr(performance_config, 'should_take_screenshot')
            
            # Test enhanced should_take_screenshot method
            should_take = performance_config.should_take_screenshot(context='element_search')
            assert isinstance(should_take, bool)
            
            # Test session management settings
            assert hasattr(performance_config, 'health_check_interval')
            assert hasattr(performance_config, 'element_search_health_check_suspension')
            
            return {
                'test_name': 'Performance Configuration',
                'status': 'PASSED',
                'details': 'Performance configuration is properly enhanced',
                'timestamp': time.time()
            }
            
        except Exception as e:
            return {
                'test_name': 'Performance Configuration',
                'status': 'FAILED',
                'error': f'Performance config issue: {e}',
                'timestamp': time.time()
            }
    
    def test_fallback_strategies(self) -> Dict[str, Any]:
        """Test fallback strategies in base action"""
        try:
            from app_android.actions.base_action import BaseAction
            
            # Create base action instance
            action = BaseAction()
            
            # Test fallback methods exist
            assert hasattr(action, '_try_fallback_strategies')
            assert hasattr(action, '_try_partial_text_fallback')
            assert hasattr(action, '_try_resource_id_variations')
            assert hasattr(action, '_try_xpath_to_uiselector_conversion')
            assert hasattr(action, '_try_accessibility_fallbacks')
            
            return {
                'test_name': 'Fallback Strategies',
                'status': 'PASSED',
                'details': 'All fallback strategy methods are available',
                'timestamp': time.time()
            }
            
        except Exception as e:
            return {
                'test_name': 'Fallback Strategies',
                'status': 'FAILED',
                'error': f'Fallback strategies issue: {e}',
                'timestamp': time.time()
            }
    
    def test_adaptive_timeouts(self) -> Dict[str, Any]:
        """Test adaptive timeout functionality"""
        try:
            from app_android.utils.enhanced_element_finder import EnhancedElementFinder
            
            finder = EnhancedElementFinder(None)
            
            # Test timeout calculation
            timeout1 = finder._calculate_adaptive_timeout('id', 'simple_id', 10, 'conditional')
            timeout2 = finder._calculate_adaptive_timeout('xpath', '//complex/xpath[contains(@attr, "value")]', 10, 'action')
            
            assert isinstance(timeout1, int)
            assert isinstance(timeout2, int)
            assert timeout1 <= timeout2  # Conditional should be faster
            
            return {
                'test_name': 'Adaptive Timeouts',
                'status': 'PASSED',
                'details': 'Adaptive timeout calculation is working correctly',
                'timestamp': time.time()
            }
            
        except Exception as e:
            return {
                'test_name': 'Adaptive Timeouts',
                'status': 'FAILED',
                'error': f'Adaptive timeout issue: {e}',
                'timestamp': time.time()
            }
    
    def test_screenshot_optimization(self) -> Dict[str, Any]:
        """Test screenshot optimization features"""
        try:
            from app_android.utils.optimized_screenshot_manager import OptimizedScreenshotManager
            
            # Test initialization
            manager = OptimizedScreenshotManager("/tmp/test_reports")
            
            # Test performance tracking attributes
            assert hasattr(manager, 'screenshot_count')
            assert hasattr(manager, 'cache_hits')
            assert hasattr(manager, 'frequency_skips')
            
            return {
                'test_name': 'Screenshot Optimization',
                'status': 'PASSED',
                'details': 'Screenshot optimization features are available',
                'timestamp': time.time()
            }
            
        except Exception as e:
            return {
                'test_name': 'Screenshot Optimization',
                'status': 'FAILED',
                'error': f'Screenshot optimization issue: {e}',
                'timestamp': time.time()
            }
    
    def test_session_management(self) -> Dict[str, Any]:
        """Test optimized session management"""
        try:
            from app_android.utils.optimized_session_manager import OptimizedSessionManager
            
            # Test initialization
            manager = OptimizedSessionManager(None)
            
            # Test session management methods
            assert hasattr(manager, 'validate_session_if_needed')
            assert hasattr(manager, 'suspend_health_checks')
            assert hasattr(manager, 'needs_session_recovery')
            
            return {
                'test_name': 'Session Management',
                'status': 'PASSED',
                'details': 'Optimized session management is available',
                'timestamp': time.time()
            }
            
        except Exception as e:
            return {
                'test_name': 'Session Management',
                'status': 'FAILED',
                'error': f'Session management issue: {e}',
                'timestamp': time.time()
            }
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        passed_tests = [r for r in self.test_results if r['status'] == 'PASSED']
        failed_tests = [r for r in self.test_results if r['status'] == 'FAILED']
        
        report = {
            'summary': {
                'total_tests': len(self.test_results),
                'passed': len(passed_tests),
                'failed': len(failed_tests),
                'success_rate': round((len(passed_tests) / len(self.test_results)) * 100, 2) if self.test_results else 0
            },
            'test_results': self.test_results,
            'recommendations': self._generate_recommendations(failed_tests),
            'timestamp': time.time()
        }
        
        return report
    
    def _generate_recommendations(self, failed_tests: List[Dict]) -> List[str]:
        """Generate recommendations based on failed tests"""
        recommendations = []
        
        for test in failed_tests:
            test_name = test['test_name']
            
            if 'Enhanced Finder' in test_name:
                recommendations.append("Install enhanced element finder dependencies")
            elif 'Healenium' in test_name:
                recommendations.append("Check Healenium service configuration and availability")
            elif 'Performance' in test_name:
                recommendations.append("Verify performance configuration module is properly installed")
            elif 'Screenshot' in test_name:
                recommendations.append("Check optimized screenshot manager dependencies")
            elif 'Session' in test_name:
                recommendations.append("Verify optimized session manager is properly configured")
        
        if not recommendations:
            recommendations.append("All tests passed - element identification improvements are working correctly")
        
        return recommendations

def main():
    """Main test execution function"""
    print("=" * 80)
    print("Android Element Identification Improvements - Test Suite")
    print("=" * 80)
    
    tester = ElementIdentificationTester()
    report = tester.run_all_tests()
    
    print("\n" + "=" * 80)
    print("TEST RESULTS SUMMARY")
    print("=" * 80)
    print(f"Total Tests: {report['summary']['total_tests']}")
    print(f"Passed: {report['summary']['passed']}")
    print(f"Failed: {report['summary']['failed']}")
    print(f"Success Rate: {report['summary']['success_rate']}%")
    
    if report['summary']['failed'] > 0:
        print("\nFAILED TESTS:")
        for test in report['test_results']:
            if test['status'] == 'FAILED':
                print(f"  ✗ {test['test_name']}: {test.get('error', 'Unknown error')}")
    
    print("\nRECOMMENDATIONS:")
    for rec in report['recommendations']:
        print(f"  • {rec}")
    
    print("\n" + "=" * 80)
    
    return report['summary']['success_rate'] == 100.0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
