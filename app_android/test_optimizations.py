#!/usr/bin/env python3
"""
Test script to verify the integration of optimized session and screenshot managers
with the AppiumDeviceController according to ANDROID_PERFORMANCE_RECOMMENDATIONS.md Step 3.

This script demonstrates:
1. Optimized session management with intelligent health checks
2. Optimized screenshot functionality with caching and compression
3. Performance metrics collection and reporting
4. Fallback behavior when optimizations are unavailable
"""

import sys
import os
import time
import json
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

try:
    from app_android.utils.appium_device_controller import AppiumDeviceController
    from app_android.utils.screenshot_manager import ScreenshotManager
    from app_android.utils.optimized_session_manager import OptimizedSessionManager, performance_config
    from app_android.utils.optimized_screenshot_manager import OptimizedScreenshotManager
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure you're running this script from the MobileApp-AutoTest directory")
    sys.exit(1)

def test_optimization_availability():
    """
    Test if optimizations are available and properly configured
    """
    print("=== Testing Optimization Availability ===")
    
    # Test performance config
    try:
        print(f"Performance config loaded: {hasattr(performance_config, 'session_validation_frequency')}")
        print(f"Session validation frequency: {getattr(performance_config, 'session_validation_frequency', 'Not set')}")
        print(f"Health check frequency: {getattr(performance_config, 'health_check_frequency', 'Not set')}")
        print(f"Screenshot cache size: {getattr(performance_config, 'screenshot_cache_size', 'Not set')}")
    except Exception as e:
        print(f"Performance config error: {e}")
    
    # Test OptimizedSessionManager
    try:
        session_manager = OptimizedSessionManager(None)
        print(f"OptimizedSessionManager created successfully")
        print(f"Session manager methods: {[m for m in dir(session_manager) if not m.startswith('_')]}")
    except Exception as e:
        print(f"OptimizedSessionManager error: {e}")
    
    # Test OptimizedScreenshotManager
    try:
        import tempfile
        temp_dir = tempfile.mkdtemp()
        screenshot_manager = OptimizedScreenshotManager(report_dir=temp_dir)
        print(f"OptimizedScreenshotManager created successfully")
        print(f"Screenshot manager methods: {[m for m in dir(screenshot_manager) if not m.startswith('_')]}")
    except Exception as e:
        print(f"OptimizedScreenshotManager error: {e}")
    
    print()

def test_screenshot_manager_integration():
    """
    Test the ScreenshotManager integration with optimizations
    """
    print("=== Testing ScreenshotManager Integration ===")
    
    try:
        # Create a test screenshot manager
        screenshot_manager = ScreenshotManager()
        
        # Initialize with a test directory
        test_dir = Path("/tmp/test_screenshots")
        test_dir.mkdir(exist_ok=True)
        
        screenshot_manager.initialize(str(test_dir))
        
        print(f"ScreenshotManager initialized")
        print(f"Report directory: {screenshot_manager.report_dir}")
        print(f"Screenshots directory: {screenshot_manager.screenshots_dir}")
        print(f"Optimization available: {hasattr(screenshot_manager, 'optimized_manager')}")
        
        if hasattr(screenshot_manager, 'optimized_manager'):
            print(f"Optimized manager active: {screenshot_manager.optimized_manager is not None}")
        
        # Test performance stats
        try:
            stats = screenshot_manager.get_performance_stats()
            print(f"Performance stats available: {stats is not None}")
            if stats:
                print(f"Stats keys: {list(stats.keys())}")
        except Exception as e:
            print(f"Performance stats error: {e}")
            
    except Exception as e:
        print(f"ScreenshotManager integration error: {e}")
    
    print()

def test_device_controller_integration():
    """
    Test the AppiumDeviceController integration with optimizations
    (without actually connecting to a device)
    """
    print("=== Testing AppiumDeviceController Integration ===")
    
    try:
        # Create a device controller instance (without driver)
        controller = AppiumDeviceController()
        
        print(f"AppiumDeviceController created")
        print(f"Controller initialized: {controller is not None}")
        print(f"Platform: {getattr(controller, 'platform_name', 'Not set')}")
        
        # Check for optimized session manager
        has_opt_session = hasattr(controller, 'optimized_session_manager')
        print(f"Has optimized session manager: {has_opt_session}")
        
        if has_opt_session:
            opt_session_active = controller.optimized_session_manager is not None
            print(f"Optimized session manager active: {opt_session_active}")
        
        # Check for screenshot manager
        has_screenshot = hasattr(controller, 'screenshot_manager')
        print(f"Has screenshot manager: {has_screenshot}")
        
        # Test performance stats method
        try:
            stats = controller.get_performance_stats()
            print(f"Performance stats method available: {stats is not None}")
            if stats:
                print(f"Stats structure: {json.dumps(stats, indent=2, default=str)}")
        except Exception as e:
            print(f"Performance stats method error: {e}")
            
    except Exception as e:
        print(f"AppiumDeviceController integration error: {e}")
    
    print()

def test_performance_config():
    """
    Test performance configuration values
    """
    print("=== Testing Performance Configuration ===")
    
    config_attrs = [
        'session_validation_frequency',
        'health_check_frequency', 
        'screenshot_cache_size',
        'screenshot_compression_quality',
        'max_screenshot_age',
        'session_recovery_attempts',
        'health_check_timeout'
    ]
    
    for attr in config_attrs:
        try:
            value = getattr(performance_config, attr, 'Not configured')
            print(f"{attr}: {value}")
        except Exception as e:
            print(f"{attr}: Error - {e}")
    
    print()

def main():
    """
    Run all optimization tests
    """
    print("Android Performance Optimizations Test Suite")
    print("=" * 50)
    print()
    
    test_optimization_availability()
    test_performance_config()
    test_screenshot_manager_integration()
    test_device_controller_integration()
    
    print("=== Test Summary ===")
    print("All optimization integration tests completed.")
    print("Check the output above for any errors or missing components.")
    print()
    print("Next steps:")
    print("1. Run actual device tests to verify optimizations work in practice")
    print("2. Monitor performance metrics during test execution")
    print("3. Adjust performance_config values based on your specific needs")

if __name__ == "__main__":
    main()