#!/usr/bin/env python3
"""
Appium Device Manager CLI

A command-line interface for managing Appium Grid devices and sessions.
This tool provides comprehensive device management capabilities similar to
the AppiumDeviceManager application.
"""

import os
import sys
import json
import time
import argparse
import logging
from typing import Dict, List, Optional
from datetime import datetime
from grid_manager import GridManager, DevicePlatform, NodeStatus
from grid_config import GridConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AppiumDeviceManager:
    """Appium Device Manager CLI"""
    
    def __init__(self):
        self.grid_manager = GridManager()
        
    def print_banner(self):
        """Print application banner"""
        print("\n" + "="*60)
        print("    APPIUM DEVICE MANAGER")
        print("    Mobile Test Automation Grid Management")
        print("="*60 + "\n")
    
    def print_status_table(self, data: List[Dict], title: str):
        """Print data in a formatted table"""
        if not data:
            print(f"\n{title}: No data available\n")
            return
        
        print(f"\n{title}:")
        print("-" * len(title))
        
        # Get all unique keys for headers
        headers = set()
        for item in data:
            headers.update(item.keys())
        headers = sorted(list(headers))
        
        # Calculate column widths
        col_widths = {}
        for header in headers:
            col_widths[header] = max(
                len(str(header)),
                max(len(str(item.get(header, ''))) for item in data)
            )
        
        # Print headers
        header_row = " | ".join(str(header).ljust(col_widths[header]) for header in headers)
        print(header_row)
        print("-" * len(header_row))
        
        # Print data rows
        for item in data:
            row = " | ".join(str(item.get(header, '')).ljust(col_widths[header]) for header in headers)
            print(row)
        
        print()
    
    def cmd_start(self, args):
        """Start the Appium Grid"""
        print("Starting Appium Grid...")
        success, message = self.grid_manager.start_grid()
        
        if success:
            print(f"✅ {message}")
            if args.wait:
                print("Waiting for all nodes to be ready...")
                time.sleep(5)
                self.cmd_status(args)
        else:
            print(f"❌ {message}")
            return 1
        return 0
    
    def cmd_stop(self, args):
        """Stop the Appium Grid"""
        print("Stopping Appium Grid...")
        success, message = self.grid_manager.stop_grid()
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            return 1
        return 0
    
    def cmd_restart(self, args):
        """Restart the Appium Grid"""
        print("Restarting Appium Grid...")
        success, message = self.grid_manager.restart_grid()
        
        if success:
            print(f"✅ {message}")
            if args.wait:
                print("Waiting for all nodes to be ready...")
                time.sleep(5)
                self.cmd_status(args)
        else:
            print(f"❌ {message}")
            return 1
        return 0
    
    def cmd_status(self, args):
        """Show Grid status"""
        health = self.grid_manager.health_check()
        
        print(f"Grid Status Report - {datetime.fromtimestamp(health['timestamp']).strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        # Grid configuration
        print(f"Grid Enabled: {'✅ Yes' if health['grid_enabled'] else '❌ No'}")
        print(f"Hub Status: {'✅ Online' if health['hub_status'] == 'online' else '❌ ' + health['hub_status'].title()}")
        print(f"Active Sessions: {health['active_sessions']}")
        
        # Issues
        if health['issues']:
            print("\n⚠️  Issues:")
            for issue in health['issues']:
                print(f"   • {issue}")
        
        # Nodes
        if health['nodes']:
            self.print_status_table(health['nodes'], "Registered Nodes")
        else:
            print("\n❌ No nodes registered\n")
        
        return 0
    
    def cmd_devices(self, args):
        """List all registered devices with verification"""
        print("\n📱 Device Status & Verification:")
        print("=" * 50)
        
        try:
            # Get comprehensive device verification
            verification = self.grid_manager.verify_device_connections()
            
            if not verification['grid_available']:
                print("❌ Grid Hub is not available")
                print("\n💡 Start the Grid first: ./start-grid.sh")
                return
            
            if verification['total_devices'] == 0:
                print("❌ No devices registered with the Grid")
                print("\n💡 Troubleshooting:")
                print("   1. Make sure devices are connected and authorized")
                print("   2. Check if Appium servers are running")
                print("   3. Run device detection: python3 grid/device_detector.py")
                print("   4. Restart the Grid: ./stop-grid.sh && ./start-grid.sh")
                return
            
            # Display iOS devices
            if verification['ios_devices']:
                print("\n📱 iOS Devices:")
                for device in verification['ios_devices']:
                    status_icon = "🟢" if device['available'] else "🔴"
                    print(f"   {status_icon} {device['device_name']} (iOS {device['platform_version']})")
                    print(f"      UDID: {device.get('udid', 'N/A')}")
                    print(f"      Sessions: {device['active_sessions']}/{device['max_sessions']}")
                    print(f"      Status: {device['status']}")
            else:
                print("\n📱 iOS Devices: None registered")
            
            # Display Android devices
            if verification['android_devices']:
                print("\n🤖 Android Devices:")
                for device in verification['android_devices']:
                    status_icon = "🟢" if device['available'] else "🔴"
                    print(f"   {status_icon} {device['device_name']} (Android {device['platform_version']})")
                    print(f"      UDID: {device.get('udid', 'N/A')}")
                    print(f"      Sessions: {device['active_sessions']}/{device['max_sessions']}")
                    print(f"      Status: {device['status']}")
            else:
                print("\n🤖 Android Devices: None registered")
            
            print(f"\n📊 Total: {verification['total_devices']} device(s) registered")
            
            # Display issues and recommendations
            if verification['issues']:
                print("\n⚠️  Issues Found:")
                for issue in verification['issues']:
                    print(f"   • {issue}")
            
            if verification['recommendations']:
                print("\n💡 Recommendations:")
                for rec in verification['recommendations']:
                    print(f"   • {rec}")
            
        except Exception as e:
            print(f"❌ Error retrieving devices: {e}")
    
    def cmd_sessions(self, args):
        """List active sessions"""
        sessions = self.grid_manager.get_active_sessions()
        
        if not sessions:
            print("ℹ️  No active sessions")
            return 0
        
        session_data = []
        for session in sessions:
            duration = time.time() - session.start_time
            session_data.append({
                'Session ID': session.session_id[:12] + '...',
                'Platform': session.platform.value,
                'Device': session.device_name,
                'Duration': f"{int(duration//60)}m {int(duration%60)}s",
                'Node': session.node_id[:8] + '...' if len(session.node_id) > 8 else session.node_id
            })
        
        self.print_status_table(session_data, "Active Sessions")
        return 0
    
    def cmd_logs(self, args):
        """Show Grid logs"""
        log_files = {
            'hub': 'grid/logs/hub.log',
            'ios': 'grid/logs/ios-registration.log',
            'android': 'grid/logs/android-registration.log'
        }
        
        if args.component and args.component in log_files:
            log_file = log_files[args.component]
        else:
            print("Available log components: hub, ios, android")
            return 1
        
        if not os.path.exists(log_file):
            print(f"❌ Log file not found: {log_file}")
            return 1
        
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()
                
            # Show last N lines
            num_lines = args.lines if args.lines else 50
            recent_lines = lines[-num_lines:] if len(lines) > num_lines else lines
            
            print(f"\nLast {len(recent_lines)} lines from {log_file}:")
            print("=" * 50)
            for line in recent_lines:
                print(line.rstrip())
            
        except Exception as e:
            print(f"❌ Error reading log file: {e}")
            return 1
        
        return 0
    
    def cmd_config(self, args):
        """Show Grid configuration"""
        config_data = [
            {'Setting': 'Grid Enabled', 'Value': str(GridConfig.is_grid_enabled())},
            {'Setting': 'Hub URL', 'Value': GridConfig.GRID_HUB_URL},
            {'Setting': 'Status URL', 'Value': GridConfig.GRID_STATUS_URL},
            {'Setting': 'iOS Direct URL', 'Value': GridConfig.IOS_DIRECT_URL},
            {'Setting': 'Android Direct URL', 'Value': GridConfig.ANDROID_DIRECT_URL},
        ]
        
        self.print_status_table(config_data, "Grid Configuration")
        
        # Show node configurations
        config_files = {
            'iOS Node': 'grid/ios-node-config.json',
            'Android Node': 'grid/android-node-config.json',
            'Hub': 'grid/hub-config.json'
        }
        
        for name, config_file in config_files.items():
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r') as f:
                        config = json.load(f)
                    print(f"\n{name} Configuration:")
                    print("-" * (len(name) + 14))
                    print(json.dumps(config, indent=2))
                except Exception as e:
                    print(f"❌ Error reading {config_file}: {e}")
        
        return 0
    
    def cmd_test_connection(self, args):
        """Test connection to devices"""
        platforms = ['iOS', 'Android'] if not args.platform else [args.platform]
        
        for platform in platforms:
            print(f"\nTesting {platform} connection...")
            
            try:
                # Get connection URL with retry logic
                url = self.grid_manager.get_connection_url_with_retry(platform)
                print(f"Connection URL: {url}")
                
                # Test basic connectivity
                import requests
                if url == GridConfig.GRID_HUB_URL:
                    # Test Grid Hub
                    response = requests.get(GridConfig.GRID_STATUS_URL, timeout=10)
                    if response.status_code == 200:
                        print(f"✅ {platform} - Grid Hub accessible")
                        
                        # Check for platform-specific nodes
                        data = response.json()
                        nodes = data.get('value', {}).get('nodes', [])
                        platform_nodes = []
                        for node in nodes:
                            for slot in node.get('slots', []):
                                stereotype = slot.get('stereotype', {})
                                if stereotype.get('platformName') == platform:
                                    platform_nodes.append(node)
                                    break
                        
                        if platform_nodes:
                            print(f"✅ {platform} - {len(platform_nodes)} node(s) available")
                        else:
                            print(f"❌ {platform} - No nodes available")
                    else:
                        print(f"❌ {platform} - Grid Hub not accessible")
                else:
                    # Test direct connection
                    direct_url = url + "/status"
                    response = requests.get(direct_url, timeout=10)
                    if response.status_code == 200:
                        print(f"✅ {platform} - Direct connection successful")
                    else:
                        print(f"❌ {platform} - Direct connection failed")
                        
            except Exception as e:
                print(f"❌ {platform} - Connection test failed: {e}")
        
        return 0
    
    def cmd_monitor(self, args):
        """Monitor Grid status continuously"""
        print("Starting Grid monitor... (Press Ctrl+C to stop)")
        
        try:
            while True:
                os.system('clear' if os.name == 'posix' else 'cls')
                self.print_banner()
                self.cmd_status(args)
                
                print(f"\nRefreshing every {args.interval} seconds... (Press Ctrl+C to stop)")
                time.sleep(args.interval)
                
        except KeyboardInterrupt:
            print("\n\nMonitoring stopped.")
            return 0

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Appium Device Manager - Mobile Test Automation Grid Management",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Start command
    start_parser = subparsers.add_parser('start', help='Start the Appium Grid')
    start_parser.add_argument('--wait', action='store_true', help='Wait for nodes to be ready')
    
    # Stop command
    stop_parser = subparsers.add_parser('stop', help='Stop the Appium Grid')
    
    # Restart command
    restart_parser = subparsers.add_parser('restart', help='Restart the Appium Grid')
    restart_parser.add_argument('--wait', action='store_true', help='Wait for nodes to be ready')
    
    # Status command
    status_parser = subparsers.add_parser('status', help='Show Grid status')
    
    # Devices command
    devices_parser = subparsers.add_parser('devices', help='List available devices')
    
    # Sessions command
    sessions_parser = subparsers.add_parser('sessions', help='List active sessions')
    
    # Logs command
    logs_parser = subparsers.add_parser('logs', help='Show Grid logs')
    logs_parser.add_argument('component', choices=['hub', 'ios', 'android'], help='Log component to show')
    logs_parser.add_argument('--lines', type=int, default=50, help='Number of lines to show')
    
    # Config command
    config_parser = subparsers.add_parser('config', help='Show Grid configuration')
    
    # Test connection command
    test_parser = subparsers.add_parser('test', help='Test connection to devices')
    test_parser.add_argument('--platform', choices=['iOS', 'Android'], help='Platform to test')
    
    # Monitor command
    monitor_parser = subparsers.add_parser('monitor', help='Monitor Grid status continuously')
    monitor_parser.add_argument('--interval', type=int, default=5, help='Refresh interval in seconds')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    manager = AppiumDeviceManager()
    manager.print_banner()
    
    # Map commands to methods
    command_map = {
        'start': manager.cmd_start,
        'stop': manager.cmd_stop,
        'restart': manager.cmd_restart,
        'status': manager.cmd_status,
        'devices': manager.cmd_devices,
        'sessions': manager.cmd_sessions,
        'logs': manager.cmd_logs,
        'config': manager.cmd_config,
        'test': manager.cmd_test_connection,
        'monitor': manager.cmd_monitor
    }
    
    if args.command in command_map:
        return command_map[args.command](args)
    else:
        print(f"Unknown command: {args.command}")
        return 1

if __name__ == "__main__":
    sys.exit(main())