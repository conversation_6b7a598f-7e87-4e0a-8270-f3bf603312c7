#!/usr/bin/env python3
"""
AppiumDeviceManager Integration Module

This module provides integration between the MobileApp-AutoTest application
and the AppiumDeviceManager for enhanced session management and device handling.
"""

import os
import json
import time
import logging
import requests
import subprocess
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class SessionStatus(Enum):
    """Session status enumeration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TERMINATED = "terminated"
    ERROR = "error"

@dataclass
class DeviceManagerSession:
    """Device Manager session information"""
    session_id: str
    device_id: str
    platform: str
    port: int
    process_id: Optional[int]
    status: SessionStatus
    start_time: str
    last_activity: str
    automation_app_url: Optional[str] = None

class AppiumDeviceManagerClient:
    """Client for communicating with AppiumDeviceManager"""
    
    def __init__(self, base_url: str = "http://localhost:3001", timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
    def is_available(self) -> bool:
        """Check if AppiumDeviceManager is available"""
        try:
            response = self.session.get(f"{self.base_url}/api/devices", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.debug(f"AppiumDeviceManager not available: {e}")
            return False
    
    def get_devices(self) -> List[Dict]:
        """Get available devices from AppiumDeviceManager"""
        try:
            response = self.session.get(f"{self.base_url}/api/devices", timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get devices: {e}")
            return []
    
    def get_ios_devices(self) -> List[Dict]:
        """Get iOS devices from AppiumDeviceManager"""
        try:
            response = self.session.get(f"{self.base_url}/api/devices/ios", timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get iOS devices: {e}")
            return []
    
    def get_android_devices(self) -> List[Dict]:
        """Get Android devices from AppiumDeviceManager"""
        try:
            response = self.session.get(f"{self.base_url}/api/devices/android", timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get Android devices: {e}")
            return []
    
    def get_sessions(self) -> List[DeviceManagerSession]:
        """Get all active sessions from AppiumDeviceManager"""
        try:
            response = self.session.get(f"{self.base_url}/api/sessions", timeout=self.timeout)
            response.raise_for_status()
            sessions_data = response.json()
            
            sessions = []
            for session_data in sessions_data:
                sessions.append(DeviceManagerSession(
                    session_id=session_data['sessionId'],
                    device_id=session_data['deviceId'],
                    platform=session_data['platform'],
                    port=session_data['port'],
                    process_id=session_data.get('processId'),
                    status=SessionStatus(session_data['status']),
                    start_time=session_data['startTime'],
                    last_activity=session_data['lastActivity']
                ))
            return sessions
        except Exception as e:
            logger.error(f"Failed to get sessions: {e}")
            return []
    
    def get_session_status(self, device_id: str) -> Optional[DeviceManagerSession]:
        """Get session status for a specific device"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/sessions/status/{device_id}", 
                timeout=self.timeout
            )
            response.raise_for_status()
            session_data = response.json()
            
            return DeviceManagerSession(
                session_id=session_data['sessionId'],
                device_id=session_data['deviceId'],
                platform=session_data['platform'],
                port=session_data['port'],
                process_id=session_data.get('processId'),
                status=SessionStatus(session_data['status']),
                start_time=session_data['startTime'],
                last_activity=session_data['lastActivity']
            )
        except Exception as e:
            logger.error(f"Failed to get session status for device {device_id}: {e}")
            return None
    
    def launch_automation_app(self, device_id: str, platform: str, app_path: str = None) -> Optional[DeviceManagerSession]:
        """Launch automation app for a specific device"""
        try:
            payload = {
                'deviceId': device_id,
                'platform': platform
            }
            
            if app_path:
                payload['appPath'] = app_path
            
            response = self.session.post(
                f"{self.base_url}/api/devices/launch-app",
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get('success'):
                session_data = result.get('session', {})
                return DeviceManagerSession(
                    session_id=session_data['sessionId'],
                    device_id=session_data['deviceId'],
                    platform=session_data['platform'],
                    port=session_data['port'],
                    process_id=session_data.get('processId'),
                    status=SessionStatus(session_data['status']),
                    start_time=session_data['startTime'],
                    last_activity=session_data['lastActivity'],
                    automation_app_url=result.get('automationAppUrl')
                )
            else:
                logger.error(f"Failed to launch app: {result.get('message')}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to launch automation app for device {device_id}: {e}")
            return None
    
    def terminate_session(self, device_id: str) -> bool:
        """Terminate session for a specific device"""
        try:
            payload = {'deviceId': device_id}
            response = self.session.post(
                f"{self.base_url}/api/sessions/terminate",
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()
            result = response.json()
            return result.get('success', False)
        except Exception as e:
            logger.error(f"Failed to terminate session for device {device_id}: {e}")
            return False
    
    def cleanup_sessions(self) -> bool:
        """Cleanup all inactive sessions"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/sessions/cleanup",
                timeout=self.timeout
            )
            response.raise_for_status()
            result = response.json()
            return result.get('success', False)
        except Exception as e:
            logger.error(f"Failed to cleanup sessions: {e}")
            return False

class IntegratedSessionManager:
    """Integrated session manager that combines Grid and DeviceManager"""
    
    def __init__(self, device_manager_url: str = "http://localhost:3001"):
        self.device_manager = AppiumDeviceManagerClient(device_manager_url)
        
        # Import grid manager if available
        try:
            from grid_manager import get_grid_manager
            self.grid_manager = get_grid_manager()
            self.grid_available = True
        except ImportError:
            self.grid_manager = None
            self.grid_available = False
            logger.warning("Grid manager not available")
    
    def is_device_manager_available(self) -> bool:
        """Check if AppiumDeviceManager is available"""
        return self.device_manager.is_available()
    
    def get_available_devices(self, platform: str = None) -> List[Dict]:
        """Get available devices from both Grid and DeviceManager"""
        devices = []
        
        # Get devices from AppiumDeviceManager if available
        if self.is_device_manager_available():
            if platform:
                if platform.lower() == 'ios':
                    devices.extend(self.device_manager.get_ios_devices())
                elif platform.lower() == 'android':
                    devices.extend(self.device_manager.get_android_devices())
            else:
                devices.extend(self.device_manager.get_devices())
        
        # Get devices from Grid if available
        if self.grid_available and self.grid_manager:
            try:
                grid_devices = self.grid_manager.get_device_info()
                for device in grid_devices:
                    if not platform or device.platform.value.lower() == platform.lower():
                        devices.append({
                            'deviceId': device.device_name,
                            'platform': device.platform.value,
                            'platformVersion': device.platform_version,
                            'status': device.status.value,
                            'source': 'grid'
                        })
            except Exception as e:
                logger.error(f"Failed to get devices from grid: {e}")
        
        return devices
    
    def create_session(self, device_id: str, platform: str, capabilities: Dict = None) -> Optional[str]:
        """Create a session using the best available method"""
        
        # Try AppiumDeviceManager first if available
        if self.is_device_manager_available():
            session = self.device_manager.launch_automation_app(device_id, platform)
            if session:
                logger.info(f"Session created via DeviceManager: {session.session_id}")
                return session.session_id
        
        # Fallback to Grid if available
        if self.grid_available and self.grid_manager and capabilities:
            try:
                session_id = self.grid_manager.create_session(platform, capabilities)
                if session_id:
                    logger.info(f"Session created via Grid: {session_id}")
                    return session_id
            except Exception as e:
                logger.error(f"Failed to create session via Grid: {e}")
        
        logger.error(f"Failed to create session for device {device_id}")
        return None
    
    def terminate_session(self, session_id: str, device_id: str = None) -> bool:
        """Terminate a session"""
        success = False
        
        # Try DeviceManager if device_id is provided
        if device_id and self.is_device_manager_available():
            success = self.device_manager.terminate_session(device_id)
            if success:
                logger.info(f"Session terminated via DeviceManager for device {device_id}")
        
        # Try Grid if available
        if not success and self.grid_available and self.grid_manager:
            try:
                success = self.grid_manager.end_session(session_id)
                if success:
                    logger.info(f"Session terminated via Grid: {session_id}")
            except Exception as e:
                logger.error(f"Failed to terminate session via Grid: {e}")
        
        return success
    
    def get_session_info(self, device_id: str = None, session_id: str = None) -> Optional[Dict]:
        """Get session information"""
        
        # Try DeviceManager if device_id is provided
        if device_id and self.is_device_manager_available():
            session = self.device_manager.get_session_status(device_id)
            if session:
                return {
                    'session_id': session.session_id,
                    'device_id': session.device_id,
                    'platform': session.platform,
                    'port': session.port,
                    'status': session.status.value,
                    'start_time': session.start_time,
                    'source': 'device_manager'
                }
        
        # Try Grid if available
        if self.grid_available and self.grid_manager:
            try:
                sessions = self.grid_manager.get_active_sessions()
                for session in sessions:
                    if session.session_id == session_id:
                        return {
                            'session_id': session.session_id,
                            'platform': session.platform.value,
                            'device_name': session.device_name,
                            'start_time': session.start_time,
                            'source': 'grid'
                        }
            except Exception as e:
                logger.error(f"Failed to get session info from Grid: {e}")
        
        return None
    
    def cleanup_all_sessions(self) -> bool:
        """Cleanup all sessions from both systems"""
        success = True
        
        # Cleanup DeviceManager sessions
        if self.is_device_manager_available():
            try:
                dm_success = self.device_manager.cleanup_sessions()
                if dm_success:
                    logger.info("DeviceManager sessions cleaned up")
                else:
                    success = False
            except Exception as e:
                logger.error(f"Failed to cleanup DeviceManager sessions: {e}")
                success = False
        
        # Cleanup Grid sessions
        if self.grid_available and self.grid_manager:
            try:
                sessions = self.grid_manager.get_active_sessions()
                for session in sessions:
                    self.grid_manager.end_session(session.session_id)
                logger.info("Grid sessions cleaned up")
            except Exception as e:
                logger.error(f"Failed to cleanup Grid sessions: {e}")
                success = False
        
        return success

# Global instance
_integrated_session_manager = None

def get_integrated_session_manager() -> IntegratedSessionManager:
    """Get the global integrated session manager instance"""
    global _integrated_session_manager
    if _integrated_session_manager is None:
        _integrated_session_manager = IntegratedSessionManager()
    return _integrated_session_manager

if __name__ == "__main__":
    # Test the integration
    manager = get_integrated_session_manager()
    
    print("AppiumDeviceManager Integration Test")
    print("====================================")
    
    # Check availability
    print(f"DeviceManager available: {manager.is_device_manager_available()}")
    print(f"Grid available: {manager.grid_available}")
    
    # Get devices
    devices = manager.get_available_devices()
    print(f"\nAvailable devices: {len(devices)}")
    for device in devices:
        print(f"  - {device.get('deviceId', 'Unknown')} ({device.get('platform', 'Unknown')})")
    
    # Get sessions
    if manager.is_device_manager_available():
        sessions = manager.device_manager.get_sessions()
        print(f"\nActive sessions: {len(sessions)}")
        for session in sessions:
            print(f"  - {session.session_id} ({session.platform})")