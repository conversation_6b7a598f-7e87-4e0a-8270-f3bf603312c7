# Comprehensive Environment Variable Resolution Test Plan

## Overview
This test plan validates the environment variable resolution fix for iOS restartApp actions and ensures robust operation across all execution contexts.

## Test Categories

### 1. Basic Environment Variable Resolution Tests

#### Test 1.1: Direct Environment Variable Resolution
**Objective**: Verify basic environment variable resolution works
**Steps**:
1. Set AU-PROD-IP14 as active environment
2. Execute restartApp action with `package_id: "env[appid]"`
3. Verify logs show resolution: `env[appid]` → `au.com.kmart`
4. Verify RestartAppAction receives resolved value

**Expected Result**: Environment variable resolves correctly

#### Test 1.2: Multiple Environment Variables
**Objective**: Test multiple variables in single action
**Steps**:
1. Create action with multiple env vars: `{"package_id": "env[appid]", "text": "env[uname]"}`
2. Execute action
3. Verify all variables resolve correctly

**Expected Result**: All environment variables resolve independently

#### Test 1.3: Non-existent Environment Variable
**Objective**: Test handling of undefined variables
**Steps**:
1. Execute action with `package_id: "env[nonexistent]"`
2. Verify original placeholder is preserved
3. Verify no errors thrown

**Expected Result**: Original placeholder kept, no crashes

### 2. Test Suite Execution Context Tests

#### Test 2.1: Single Test Case Execution
**Objective**: Verify environment resolution in single test execution
**Steps**:
1. Create test case with restartApp action using env vars
2. Execute single test case
3. Monitor environment ID throughout execution
4. Verify resolution works correctly

**Expected Result**: Environment ID persists, resolution works

#### Test 2.2: Multi-Test Suite Execution
**Objective**: Verify environment persistence across multiple tests
**Steps**:
1. Create test suite with 3+ test cases using env vars
2. Execute entire suite
3. Monitor environment ID between test cases
4. Verify resolution works in all test cases

**Expected Result**: Environment ID maintained across all tests

#### Test 2.3: Test Case with Multi-Step Actions
**Objective**: Verify environment resolution in multi-step actions
**Steps**:
1. Create test case with multiStep action containing env vars
2. Execute test case
3. Verify environment resolution works for all sub-steps

**Expected Result**: Environment resolution works for all sub-steps

### 3. Error Handling and Recovery Tests

#### Test 3.1: Test Case Failure and Retry
**Objective**: Verify environment persistence during retries
**Steps**:
1. Create test case that fails on first attempt
2. Configure max retries = 2
3. Ensure retry uses env vars correctly
4. Monitor environment ID during retries

**Expected Result**: Environment ID preserved during retries

#### Test 3.2: Runtime Exception During Execution
**Objective**: Test environment persistence during exceptions
**Steps**:
1. Create test case with action that throws exception
2. Include restartApp with env vars after exception
3. Verify environment resolution still works

**Expected Result**: Environment resolution unaffected by exceptions

#### Test 3.3: Session Timeout Simulation
**Objective**: Test database fallback when session is lost
**Steps**:
1. Start test execution
2. Simulate session loss (clear Flask session)
3. Verify database fallback retrieves environment ID
4. Verify resolution continues working

**Expected Result**: Database fallback works seamlessly

### 4. Concurrency and Threading Tests

#### Test 4.1: Concurrent Test Execution
**Objective**: Verify environment resolution with multiple concurrent tests
**Steps**:
1. Start multiple test cases simultaneously
2. Each using different environment variables
3. Verify no cross-contamination
4. Verify each gets correct environment context

**Expected Result**: No environment ID conflicts between tests

#### Test 4.2: Background Thread Execution
**Objective**: Test environment resolution in background threads
**Steps**:
1. Execute test case in background thread
2. Verify environment ID accessible in thread context
3. Verify resolution works correctly

**Expected Result**: Environment resolution works in background threads

### 5. Database Persistence Tests

#### Test 5.1: Database-Based Environment Storage
**Objective**: Verify database storage and retrieval of active environment
**Steps**:
1. Set active environment via database
2. Execute test without Flask session
3. Verify database fallback works
4. Verify correct environment ID retrieved

**Expected Result**: Database fallback provides correct environment ID

#### Test 5.2: Environment Switching During Execution
**Objective**: Test environment changes during test execution
**Steps**:
1. Start test execution with Environment A
2. Switch to Environment B mid-execution
3. Verify subsequent actions use Environment B
4. Verify no cached environment ID issues

**Expected Result**: Environment switch takes effect immediately

### 6. Edge Cases and Stress Tests

#### Test 6.1: Rapid Environment Switching
**Objective**: Test rapid environment changes
**Steps**:
1. Switch environments rapidly (every 100ms)
2. Execute actions during switches
3. Verify no race conditions
4. Verify correct environment always used

**Expected Result**: No race conditions, correct environment used

#### Test 6.2: Large Test Suite Execution
**Objective**: Test environment persistence in long-running suites
**Steps**:
1. Create test suite with 50+ test cases
2. Include env vars in various actions
3. Execute entire suite
4. Monitor environment ID throughout

**Expected Result**: Environment ID stable throughout execution

#### Test 6.3: Memory and Performance Impact
**Objective**: Verify fix doesn't impact performance
**Steps**:
1. Execute test suite before and after fix
2. Measure execution time and memory usage
3. Compare performance metrics

**Expected Result**: Minimal performance impact

### 7. Cross-Platform Isolation Tests

#### Test 7.1: iOS and Android Simultaneous Execution
**Objective**: Verify iOS fix doesn't affect Android
**Steps**:
1. Start iOS test execution with env vars
2. Simultaneously start Android test execution
3. Verify both platforms work independently
4. Verify no cross-platform interference

**Expected Result**: Both platforms work independently

#### Test 7.2: Android ActionFactory Compatibility
**Objective**: Verify Android ActionFactory unaffected
**Steps**:
1. Execute Android test cases
2. Verify Android ActionFactory works normally
3. Verify no signature conflicts

**Expected Result**: Android functionality unchanged

## Test Execution Instructions

### Prerequisites
1. AU-PROD-IP14 environment configured with appid = "au.com.kmart"
2. Test device connected and configured
3. Test cases created with environment variables

### Execution Order
1. Run Basic Tests (1.1-1.3) first
2. Run Suite Execution Tests (2.1-2.3)
3. Run Error Handling Tests (3.1-3.3)
4. Run Concurrency Tests (4.1-4.2)
5. Run Database Tests (5.1-5.2)
6. Run Edge Cases (6.1-6.3)
7. Run Cross-Platform Tests (7.1-7.2)

### Success Criteria
- All environment variables resolve correctly
- No regression in existing functionality
- Environment ID persists across all execution contexts
- Database fallback works when session unavailable
- No cross-platform interference
- Performance impact < 5%

### Failure Investigation
If any test fails:
1. Check logs for environment ID retrieval
2. Verify database active_environment table
3. Check Flask session state
4. Verify environment variable configuration
5. Check for threading or concurrency issues

## Automated Test Implementation

Create automated test scripts for each category to enable regression testing and continuous validation of the environment variable resolution fix.
