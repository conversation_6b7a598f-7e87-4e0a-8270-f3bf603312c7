import os
from pathlib import Path
import importlib.util
import sys
import logging

logger = logging.getLogger(__name__)

# Base directory
BASE_DIR = Path(__file__).resolve().parent

# Platform-specific suffixes for isolation
PLATFORM_SUFFIX = "_ios"
INSTANCE_SUFFIX = os.environ.get('INSTANCE_DB_SUFFIX', '')
DATABASE_SUFFIX = f"{PLATFORM_SUFFIX}{INSTANCE_SUFFIX}"

# Database-backed directory paths
# Check if the database module is available
try:
    # First, make sure app module is in the path
    app_dir = BASE_DIR / 'app'
    if str(app_dir) not in sys.path:
        sys.path.insert(0, str(app_dir))

    # Import the directory paths database
    from utils.directory_paths_db import DirectoryPathsDB
    directory_paths_db = DirectoryPathsDB()
    
    # Define default directories with platform suffix
    DEFAULT_DIRECTORIES = {
        'TEST_CASES': BASE_DIR / 'test_cases_ios',
        'REPORTS': BASE_DIR / 'reports_ios',
        'SCREENSHOTS': BASE_DIR / 'screenshots_ios',
        'REFERENCE_IMAGES': BASE_DIR / '/Users/<USER>/Documents/automation-tool/android_data/reference_images',
        'TEST_SUITES': BASE_DIR / 'test_suites_ios',
        'RESULTS': BASE_DIR / 'reports_ios' / 'suites',
        'RECORDINGS': BASE_DIR / 'recordings_ios',
        'TEMP_FILES': BASE_DIR / 'temp_ios',
    }
    
    # Get paths from database or use defaults
    DIRECTORIES = {}
    for name, default_path in DEFAULT_DIRECTORIES.items():
        # Get path from database or use default
        db_path = directory_paths_db.get_path(name)
        if db_path:
            # Check if it's an absolute or relative path
            path_obj = Path(db_path)
            if path_obj.is_absolute():
                DIRECTORIES[name] = path_obj
            else:
                DIRECTORIES[name] = BASE_DIR / db_path
            logger.info(f"Using database path for {name}: {DIRECTORIES[name]}")
        else:
            DIRECTORIES[name] = default_path
            logger.info(f"Using default path for {name}: {DIRECTORIES[name]}")
            
            # Initialize the database with default value
            if 'directory_paths_db' in locals():
                try:
                    directory_paths_db.save_path(name, str(default_path.relative_to(BASE_DIR)))
                except ValueError:
                    # If it can't be made relative, store absolute path
                    directory_paths_db.save_path(name, str(default_path))
    
    # Files to push directory from database or default
    db_files_to_push = directory_paths_db.get_path('FILES_TO_PUSH')
    if db_files_to_push:
        path_obj = Path(db_files_to_push)
        if path_obj.is_absolute():
            FILES_TO_PUSH_DIR = path_obj
        else:
            FILES_TO_PUSH_DIR = BASE_DIR / db_files_to_push
        logger.info(f"Using database path for FILES_TO_PUSH: {FILES_TO_PUSH_DIR}")
    else:
        FILES_TO_PUSH_DIR = BASE_DIR / '/Users/<USER>/Documents/automation-tool/files_to_push'
        logger.info(f"Using default path for FILES_TO_PUSH: {FILES_TO_PUSH_DIR}")
        
        # Initialize the database with default value
        if 'directory_paths_db' in locals():
            try:
                directory_paths_db.save_path('FILES_TO_PUSH', str(FILES_TO_PUSH_DIR.relative_to(BASE_DIR)))
            except ValueError:
                # If it can't be made relative, store absolute path
                directory_paths_db.save_path('FILES_TO_PUSH', str(FILES_TO_PUSH_DIR))
except Exception as e:
    # Fall back to default directories if database access fails
    logger.warning(f"Error accessing directory paths database, using defaults: {str(e)}")
    
    # Directory configuration with defaults
    DIRECTORIES = {
        'TEST_CASES': BASE_DIR / 'test_cases',
        'REPORTS': BASE_DIR / 'reports',
        'SCREENSHOTS': BASE_DIR / 'screenshots',
        'REFERENCE_IMAGES': BASE_DIR / '/Users/<USER>/Documents/automation-tool/android_data/reference_images',
        'TEST_SUITES': BASE_DIR / 'test_suites',
        'RESULTS': BASE_DIR / 'reports' / 'suites',
        'RECORDINGS': BASE_DIR / 'recordings',
        'TEMP_FILES': BASE_DIR / 'temp',
    }
    
    # Files to push directory (for media uploads)
    FILES_TO_PUSH_DIR = BASE_DIR / '/Users/<USER>/Documents/automation-tool/files_to_push'

# Ensure all directories exist
for dir_path in DIRECTORIES.values():
    dir_path.mkdir(parents=True, exist_ok=True)

# Ensure files_to_push directory exists
FILES_TO_PUSH_DIR.mkdir(parents=True, exist_ok=True)

# iOS scaling configuration
# Coordinate scaling factors (for converting Airtest positions to iOS screen coordinates)
IOS_SCALE_FACTORS = {
    # Device UUID or model: scale_factor
    'default': 0.33,  # Default scale factor
    'iPhone Simulator': 0.33,
    'iPhone X': 0.33,
    'iPhone 11': 0.33,
    'iPhone 12': 0.33,
    'iPhone 13': 0.33,
    'iPhone 14': 0.33,
    'iPhone 15': 0.33,
    'iPad Simulator': 0.33,
    # Add more device-specific scale factors as needed
}

# Template scaling factors (for resizing templates before matching)
IOS_TEMPLATE_SCALES = {
    # Device UUID or model: template_scale
    'default': 1.0,   # Default: no scaling
    'iPhone X': 1.0,
    'iPhone 11': 1.0,
    'iPhone 12': 1.0,
    'iPhone 13': 1.0,
    'iPhone 14': 1.0,
    'iPhone 15': 1.0,
    'iPad Simulator': 1.0,
    # Add more device-specific template scales as needed
}

# Global values for test parameterization
GLOBAL_VALUES = {
    'Auto Rerun Failed': False,
    'Connection Retry Attempts': 3,
    'Connection Retry Delay': 2,
    'Max Step Execution Time': 300,
    'Test Case Delay': 15,
    'default_element_timeout': 60,
}

# Port configuration
FLASK_PORT = int(os.getenv('FLASK_PORT', 8080))
APPIUM_PORT = int(os.getenv('APPIUM_PORT', 4723))
# iOS-specific WDA port - using 8200 to avoid conflicts with Android apps
WDA_PORT = int(os.getenv('WDA_PORT', 8200))

# Appium configuration
APPIUM_CONFIG = {
    'HOST': '127.0.0.1',
    'PORT': APPIUM_PORT,
    'BASE_PATH': '/wd/hub'
}

# WebDriverAgent configuration
WDA_CONFIG = {
    'HOST': '127.0.0.1',
    'PORT': WDA_PORT,
    'BASE_PATH': ''
}

# ADB configuration
ADB_CONFIG = {
    'TIMEOUT': 10,  # seconds
    'MAX_RETRIES': 3
}

# Flask configuration
class FlaskConfig:
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'mobile-automation-secret-key')
    TESTING = False
    DEBUG = False
    PORT = FLASK_PORT
    HOST = '0.0.0.0'
    TEST_CASES_DIR = str(DIRECTORIES['TEST_CASES'])