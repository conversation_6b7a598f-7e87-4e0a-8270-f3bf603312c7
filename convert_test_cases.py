#!/usr/bin/env python3
"""
Utility to convert hardcoded package_id values to environment variables in test cases
"""

import json
import glob
import os
import sys
from datetime import datetime

def convert_test_case_file(file_path, backup_dir=None):
    """Convert a single test case file to use environment variables"""
    print(f"Processing: {file_path}")
    
    try:
        with open(file_path, 'r') as f:
            test_case = json.load(f)
        
        # Create backup if requested
        if backup_dir:
            os.makedirs(backup_dir, exist_ok=True)
            backup_path = os.path.join(backup_dir, os.path.basename(file_path))
            with open(backup_path, 'w') as f:
                json.dump(test_case, f, indent=2)
            print(f"  Backup created: {backup_path}")
        
        changes_made = 0
        
        # Process actions
        if 'actions' in test_case and isinstance(test_case['actions'], list):
            for action in test_case['actions']:
                if isinstance(action, dict):
                    # Convert package_id values
                    if 'package_id' in action:
                        old_value = action['package_id']
                        if old_value in ['au.com.kmart', 'com.kmart.android', 'nz.com.kmart']:
                            action['package_id'] = 'env[package_id]'
                            print(f"    Converted package_id: '{old_value}' -> 'env[package_id]'")
                            changes_made += 1
                    
                    # Convert package values (for backward compatibility)
                    if 'package' in action:
                        old_value = action['package']
                        if old_value in ['au.com.kmart', 'com.kmart.android', 'nz.com.kmart']:
                            action['package'] = 'env[package_id]'
                            print(f"    Converted package: '{old_value}' -> 'env[package_id]'")
                            changes_made += 1
        
        # Save the modified test case
        if changes_made > 0:
            with open(file_path, 'w') as f:
                json.dump(test_case, f, indent=2)
            print(f"  ✓ Saved {changes_made} changes to {file_path}")
        else:
            print(f"  - No changes needed for {file_path}")
        
        return changes_made
        
    except Exception as e:
        print(f"  ✗ Error processing {file_path}: {e}")
        return 0

def convert_all_test_cases():
    """Convert all test cases in both iOS and Android directories"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"tmp/backup_env_conversion_{timestamp}"
    
    total_changes = 0
    total_files = 0
    
    # Convert iOS test cases
    ios_test_cases_dir = "test_cases"
    if os.path.exists(ios_test_cases_dir):
        print(f"\nConverting iOS test cases in {ios_test_cases_dir}:")
        for file_path in glob.glob(os.path.join(ios_test_cases_dir, "*.json")):
            changes = convert_test_case_file(file_path, backup_dir)
            total_changes += changes
            total_files += 1
    
    # Convert Android test cases
    android_test_cases_dir = "test_cases_android"
    if os.path.exists(android_test_cases_dir):
        print(f"\nConverting Android test cases in {android_test_cases_dir}:")
        for file_path in glob.glob(os.path.join(android_test_cases_dir, "*.json")):
            changes = convert_test_case_file(file_path, backup_dir)
            total_changes += changes
            total_files += 1
    
    print(f"\n=== CONVERSION SUMMARY ===")
    print(f"Files processed: {total_files}")
    print(f"Total changes made: {total_changes}")
    if total_changes > 0:
        print(f"Backup directory: {backup_dir}")
    
    return total_changes

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--file":
        # Convert specific file
        if len(sys.argv) > 2:
            convert_test_case_file(sys.argv[2])
        else:
            print("Usage: python convert_test_cases.py --file <file_path>")
    else:
        # Convert all test cases
        convert_all_test_cases()
