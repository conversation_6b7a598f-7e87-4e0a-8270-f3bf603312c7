#!/usr/bin/env python3
"""
Database Separation and Cleanup Script
Separates iOS and Android environments into their proper databases and removes cross-contamination
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

# Add both app directories to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

def backup_databases():
    """Create backups of both databases before making changes"""
    print("=== CREATING DATABASE BACKUPS ===")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"tmp/database_backup_{timestamp}"
    os.makedirs(backup_dir, exist_ok=True)
    
    # Backup iOS database
    ios_db_path = "data/settings_ios.db"
    if os.path.exists(ios_db_path):
        ios_backup = os.path.join(backup_dir, "settings_ios_backup.db")
        import shutil
        shutil.copy2(ios_db_path, ios_backup)
        print(f"✓ iOS database backed up to: {ios_backup}")
    
    # Backup Android database
    android_db_path = "data/settings_android.db"
    if os.path.exists(android_db_path):
        android_backup = os.path.join(backup_dir, "settings_android_backup.db")
        import shutil
        shutil.copy2(android_db_path, android_backup)
        print(f"✓ Android database backed up to: {android_backup}")
    
    print(f"Backup directory: {backup_dir}\n")
    return backup_dir

def analyze_current_state():
    """Analyze the current state of both databases"""
    print("=== ANALYZING CURRENT DATABASE STATE ===")
    
    # Analyze iOS database
    print("iOS Database (settings_ios.db):")
    ios_db_path = "data/settings_ios.db"
    if os.path.exists(ios_db_path):
        conn = sqlite3.connect(ios_db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM environments ORDER BY id")
        ios_environments = cursor.fetchall()
        
        for env_id, env_name in ios_environments:
            cursor.execute("SELECT COUNT(*) FROM environment_variables WHERE environment_id = ?", (env_id,))
            var_count = cursor.fetchone()[0]
            
            # Check if it's iOS or Android environment
            cursor.execute("SELECT name, current_value FROM environment_variables WHERE environment_id = ? AND name = 'package_id'", (env_id,))
            package_result = cursor.fetchone()
            
            env_type = "UNKNOWN"
            if package_result:
                package_value = package_result[1]
                if 'au.com.kmart' in package_value or 'nz.com.kmart' in package_value:
                    env_type = "iOS"
                elif 'com.kmart.android' in package_value:
                    env_type = "ANDROID"
            
            print(f"  ID {env_id}: {env_name} ({var_count} vars) - {env_type}")
        
        conn.close()
    else:
        print("  Database not found")
    
    # Analyze Android database
    print("\nAndroid Database (settings_android.db):")
    android_db_path = "data/settings_android.db"
    if os.path.exists(android_db_path):
        conn = sqlite3.connect(android_db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM environments ORDER BY id")
        android_environments = cursor.fetchall()
        
        for env_id, env_name in android_environments:
            cursor.execute("SELECT COUNT(*) FROM environment_variables WHERE environment_id = ?", (env_id,))
            var_count = cursor.fetchone()[0]
            print(f"  ID {env_id}: {env_name} ({var_count} vars)")
        
        conn.close()
    else:
        print("  Database not found")
    
    print()

def clean_ios_database():
    """Remove Android environments from iOS database and keep only the 3 original iOS environments"""
    print("=== CLEANING iOS DATABASE ===")
    
    ios_db_path = "data/settings_ios.db"
    if not os.path.exists(ios_db_path):
        print("iOS database not found!")
        return False
    
    conn = sqlite3.connect(ios_db_path)
    cursor = conn.cursor()
    
    # Get all environments
    cursor.execute("SELECT id, name FROM environments ORDER BY id")
    all_environments = cursor.fetchall()
    
    ios_environments_to_keep = []
    android_environments_to_remove = []
    
    for env_id, env_name in all_environments:
        # Check package_id to determine if it's iOS or Android
        cursor.execute("SELECT current_value FROM environment_variables WHERE environment_id = ? AND name = 'package_id'", (env_id,))
        package_result = cursor.fetchone()
        
        if package_result:
            package_value = package_result[0]
            if 'au.com.kmart' in package_value or 'nz.com.kmart' in package_value:
                ios_environments_to_keep.append((env_id, env_name))
            elif 'com.kmart.android' in package_value:
                android_environments_to_remove.append((env_id, env_name))
        else:
            # Check by name pattern
            if 'ANDROID' in env_name.upper():
                android_environments_to_remove.append((env_id, env_name))
            else:
                ios_environments_to_keep.append((env_id, env_name))
    
    print(f"iOS environments to keep: {len(ios_environments_to_keep)}")
    for env_id, env_name in ios_environments_to_keep:
        print(f"  ✓ ID {env_id}: {env_name}")
    
    print(f"\nAndroid environments to remove: {len(android_environments_to_remove)}")
    for env_id, env_name in android_environments_to_remove:
        print(f"  ✗ ID {env_id}: {env_name}")
    
    # Remove Android environments from iOS database
    for env_id, env_name in android_environments_to_remove:
        print(f"\nRemoving Android environment ID {env_id}: {env_name}")
        
        # Delete environment variables first (due to foreign key constraint)
        cursor.execute("DELETE FROM environment_variables WHERE environment_id = ?", (env_id,))
        deleted_vars = cursor.rowcount
        print(f"  Deleted {deleted_vars} variables")
        
        # Delete environment
        cursor.execute("DELETE FROM environments WHERE id = ?", (env_id,))
        deleted_envs = cursor.rowcount
        print(f"  Deleted environment: {deleted_envs}")
    
    # Set active environment to the first iOS environment
    if ios_environments_to_keep:
        first_ios_env_id = ios_environments_to_keep[0][0]
        cursor.execute("INSERT OR REPLACE INTO active_environment (id, environment_id, updated_at) VALUES (1, ?, CURRENT_TIMESTAMP)", (first_ios_env_id,))
        print(f"\n✓ Set active environment to ID {first_ios_env_id}: {ios_environments_to_keep[0][1]}")
    
    conn.commit()
    conn.close()
    
    print("✓ iOS database cleanup completed")
    return True

def ensure_android_environments():
    """Ensure Android database has proper Android environments"""
    print("=== ENSURING ANDROID ENVIRONMENTS ===")
    
    android_db_path = "data/settings_android.db"
    
    # Import Android database class
    from app_android.utils.directory_paths_db import directory_paths_db as android_db
    
    # Get current environments
    environments = android_db.get_all_environments()
    print(f"Current Android environments: {len(environments)}")
    
    # Define the required Android environments
    required_android_envs = [
        ("AU-PROD-ANDROID", "com.kmart.android"),
        ("AU-PROD-ANDROID-SE", "com.kmart.android"),
        ("NZ-PROD-ANDROID", "com.kmart.android")
    ]
    
    for env_name, package_id in required_android_envs:
        # Check if environment exists
        existing_env = android_db.get_environment_by_name(env_name)
        if not existing_env:
            print(f"Creating Android environment: {env_name}")
            env_id = android_db.create_environment(env_name)
            if env_id:
                # Add package_id variable
                android_db.add_environment_variable(
                    env_id, 
                    "package_id", 
                    "string", 
                    package_id, 
                    package_id
                )
                print(f"  ✓ Created with package_id: {package_id}")
            else:
                print(f"  ✗ Failed to create environment {env_name}")
        else:
            print(f"Android environment {env_name} already exists (ID: {existing_env['id']})")
            # Verify it has package_id variable
            variables = android_db.get_variables_for_environment(existing_env['id'])
            package_var = next((v for v in variables if v['name'] == 'package_id'), None)
            if not package_var:
                print(f"  Adding missing package_id variable")
                android_db.add_environment_variable(
                    existing_env['id'], 
                    "package_id", 
                    "string", 
                    package_id, 
                    package_id
                )
            else:
                print(f"  ✓ Has package_id: {package_var['current_value']}")
    
    print("✓ Android environments verified")

def verify_separation():
    """Verify that the databases are properly separated"""
    print("=== VERIFYING DATABASE SEPARATION ===")
    
    # Check iOS database
    print("iOS Database Final State:")
    ios_db_path = "data/settings_ios.db"
    if os.path.exists(ios_db_path):
        conn = sqlite3.connect(ios_db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM environments ORDER BY id")
        ios_environments = cursor.fetchall()
        
        ios_only = True
        for env_id, env_name in ios_environments:
            cursor.execute("SELECT current_value FROM environment_variables WHERE environment_id = ? AND name = 'package_id'", (env_id,))
            package_result = cursor.fetchone()
            
            env_type = "iOS"
            if package_result and 'com.kmart.android' in package_result[0]:
                env_type = "ANDROID (ERROR!)"
                ios_only = False
            
            print(f"  ID {env_id}: {env_name} - {env_type}")
        
        # Check active environment
        cursor.execute("SELECT environment_id FROM active_environment WHERE id = 1")
        active_result = cursor.fetchone()
        if active_result:
            print(f"  Active Environment: ID {active_result[0]}")
        
        conn.close()
        
        if ios_only:
            print("  ✓ iOS database contains only iOS environments")
        else:
            print("  ✗ iOS database still contains Android environments!")
    
    # Check Android database
    print("\nAndroid Database Final State:")
    android_db_path = "data/settings_android.db"
    if os.path.exists(android_db_path):
        conn = sqlite3.connect(android_db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM environments ORDER BY id")
        android_environments = cursor.fetchall()
        
        for env_id, env_name in android_environments:
            cursor.execute("SELECT current_value FROM environment_variables WHERE environment_id = ? AND name = 'package_id'", (env_id,))
            package_result = cursor.fetchone()
            
            package_value = package_result[0] if package_result else "None"
            print(f"  ID {env_id}: {env_name} - package_id: {package_value}")
        
        conn.close()
        print("  ✓ Android database verified")
    
    print()

def main():
    """Main function to perform database separation and cleanup"""
    print("DATABASE SEPARATION AND CLEANUP\n")
    print("This script will:")
    print("1. Create backups of both databases")
    print("2. Remove Android environments from iOS database")
    print("3. Ensure proper Android environments exist")
    print("4. Verify the separation is complete")
    print()
    
    # Create backups
    backup_dir = backup_databases()
    
    # Analyze current state
    analyze_current_state()
    
    # Clean iOS database
    ios_success = clean_ios_database()
    
    # Ensure Android environments
    ensure_android_environments()
    
    # Verify separation
    verify_separation()
    
    print("=== SUMMARY ===")
    if ios_success:
        print("✅ Database separation completed successfully!")
        print("✅ iOS database now contains only iOS environments")
        print("✅ Android database contains proper Android environments")
        print("✅ Cross-contamination removed")
        print(f"✅ Backups available in: {backup_dir}")
        print("\nEnvironment variable resolution should now work correctly for each platform!")
    else:
        print("❌ Database separation encountered issues")
        print("Check the error messages above and restore from backup if needed")

if __name__ == "__main__":
    main()
