#!/usr/bin/env python3
"""
Debug script to examine the current state of environment variables in the Android database
"""

import sys
import os
import sqlite3

# Add the app_android directory to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

from utils.directory_paths_db import directory_paths_db

def examine_android_database():
    """Examine the current state of environments and variables in the Android database"""
    
    print("=== ANDROID ENVIRONMENT DATABASE INVESTIGATION ===\n")
    
    # 1. Check all environments
    print("1. ALL ENVIRONMENTS:")
    environments = directory_paths_db.get_all_environments()
    if environments:
        for env in environments:
            print(f"   ID: {env['id']}, Name: '{env['name']}', Created: {env['created_at']}")
    else:
        print("   No environments found!")
    print()
    
    # 2. Check active environment
    print("2. ACTIVE ENVIRONMENT:")
    try:
        active_env_id = directory_paths_db.get_active_environment()
        if active_env_id:
            active_env = directory_paths_db.get_environment_by_id(active_env_id)
            print(f"   Active Environment ID: {active_env_id}")
            if active_env:
                print(f"   Active Environment Name: '{active_env['name']}'")
            else:
                print(f"   WARNING: Active environment ID {active_env_id} not found in database!")
        else:
            print("   No active environment set")
    except AttributeError:
        print("   Active environment functionality not available in Android database")
    print()
    
    # 3. Check variables for each environment
    print("3. ENVIRONMENT VARIABLES:")
    for env in environments:
        env_id = env['id']
        env_name = env['name']
        print(f"   Environment '{env_name}' (ID: {env_id}):")
        
        variables = directory_paths_db.get_variables_for_environment(env_id)
        if variables:
            for var in variables:
                print(f"     - {var['name']}: '{var['current_value']}' (type: {var['type']})")
        else:
            print("     No variables defined")
        print()
    
    # 4. Test specific environment ID 2 (from the logs)
    print("4. SPECIFIC CHECK FOR ENVIRONMENT ID 2:")
    env_2 = directory_paths_db.get_environment_by_id(2)
    if env_2:
        print(f"   Environment ID 2 exists: '{env_2['name']}'")
        variables_2 = directory_paths_db.get_variables_for_environment(2)
        if variables_2:
            print(f"   Variables in environment ID 2:")
            for var in variables_2:
                print(f"     - {var['name']}: '{var['current_value']}'")
        else:
            print("   No variables found in environment ID 2")
    else:
        print("   Environment ID 2 does not exist!")
    print()
    
    # 5. Check for package_id related variables
    print("5. SEARCH FOR PACKAGE_ID RELATED VARIABLES:")
    found_package_vars = False
    for env in environments:
        env_id = env['id']
        env_name = env['name']
        variables = directory_paths_db.get_variables_for_environment(env_id)
        
        package_vars = [var for var in variables if 'package' in var['name'].lower()]
        if package_vars:
            found_package_vars = True
            print(f"   In environment '{env_name}' (ID: {env_id}):")
            for var in package_vars:
                print(f"     - {var['name']}: '{var['current_value']}'")
    
    if not found_package_vars:
        print("   No package-related variables found in any environment")
    print()
    
    # 6. Raw database inspection
    print("6. RAW DATABASE INSPECTION:")
    try:
        # Get the database path
        db_path = directory_paths_db.db_path
        print(f"   Database path: {db_path}")
        
        if os.path.exists(db_path):
            print(f"   Database file exists, size: {os.path.getsize(db_path)} bytes")
            
            # Connect directly to examine tables
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check table structure
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"   Tables in database: {[table[0] for table in tables]}")
            
            # Count records in each table
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   Records in {table_name}: {count}")
            
            conn.close()
        else:
            print(f"   Database file does not exist!")
    except Exception as e:
        print(f"   Error inspecting database: {e}")

if __name__ == "__main__":
    examine_android_database()
