#!/usr/bin/env python3
"""
Debug script to investigate environment variable resolution issues
"""

import sys
import os

# Add the app directory to Python path
sys.path.append('app')

def test_environment_resolution():
    """Test the environment variable resolution system"""
    print("=== Environment Variable Resolution Debug ===")
    
    try:
        # Import the required modules
        from utils.environment_resolver import resolve_text_with_env_variables, get_active_environment_id
        from utils.directory_paths_db import directory_paths_db
        
        # Test 1: Check active environment
        print("\n1. Checking Active Environment:")
        active_env_id = get_active_environment_id()
        print(f"   Active Environment ID: {active_env_id}")
        
        # Test 2: Check environment variables for active environment
        print(f"\n2. Variables for Environment ID {active_env_id}:")
        if active_env_id:
            variables = directory_paths_db.get_variables_for_environment(active_env_id)
            print(f"   Found {len(variables)} variables:")
            for var in variables:
                print(f"     {var['name']}: '{var['current_value']}'")
        
        # Test 3: Test resolution with different formats
        print(f"\n3. Testing Resolution with Environment ID {active_env_id}:")
        
        test_cases = [
            "env[package_id]",           # Standard format
            "package_id",                # Direct variable name
            "env[appid]",               # Another variable
            "com.kmart.android",        # Hardcoded value (should not change)
            "env[nonexistent]",         # Non-existent variable
        ]
        
        for test_input in test_cases:
            try:
                result = resolve_text_with_env_variables(test_input, active_env_id)
                changed = "✓ RESOLVED" if result != test_input else "✗ NO CHANGE"
                print(f"     '{test_input}' -> '{result}' [{changed}]")
            except Exception as e:
                print(f"     '{test_input}' -> ERROR: {e}")
        
        # Test 4: Test the action factory resolution
        print(f"\n4. Testing ActionFactory Resolution:")
        try:
            from actions.action_factory import ActionFactory
            
            # Create action factory instance
            action_factory = ActionFactory()
            
            # Test parameters that should be resolved
            test_params = {
                'package_id': 'env[package_id]',
                'app_id': 'env[appid]',
                'hardcoded': 'com.kmart.android'
            }
            
            print(f"   Original params: {test_params}")
            
            # Apply environment variable resolution
            resolved_params = test_params.copy()
            
            # Manually call the resolution logic from action factory
            current_env_id = get_active_environment_id()
            if current_env_id is not None:
                print(f"   Applying resolution for env ID {current_env_id}")
                substitution_count = 0
                for key, value in resolved_params.items():
                    if isinstance(value, str):
                        original_value = value
                        try:
                            resolved_value = resolve_text_with_env_variables(value, current_env_id)
                            if resolved_value != original_value:
                                resolved_params[key] = resolved_value
                                substitution_count += 1
                                print(f"     ✓ Resolved '{key}': '{original_value}' -> '{resolved_value}'")
                            else:
                                print(f"     - No change for '{key}': '{original_value}'")
                        except Exception as e:
                            print(f"     ✗ Error resolving '{key}': {e}")
                
                print(f"   Total substitutions: {substitution_count}")
                print(f"   Final params: {resolved_params}")
            else:
                print("   No active environment found!")
                
        except Exception as e:
            print(f"   Error testing ActionFactory: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"Error in environment resolution test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_environment_resolution()
