#!/usr/bin/env python3
"""
Debug script to check what methods are available in the iOS DirectoryPathsDB class
"""

import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def debug_ios_db_methods():
    """Debug the iOS DirectoryPathsDB class methods"""
    print("=== DEBUGGING iOS DirectoryPathsDB METHODS ===")
    
    try:
        from utils.directory_paths_db import DirectoryPathsDB, directory_paths_db
        
        print("1. DirectoryPathsDB class methods:")
        methods = [method for method in dir(DirectoryPathsDB) if not method.startswith('_')]
        for method in sorted(methods):
            print(f"   - {method}")
        
        print("\n2. directory_paths_db instance methods:")
        instance_methods = [method for method in dir(directory_paths_db) if not method.startswith('_')]
        for method in sorted(instance_methods):
            print(f"   - {method}")
        
        print("\n3. Testing specific methods:")
        
        # Test if get_active_environment exists
        if hasattr(directory_paths_db, 'get_active_environment'):
            print("   ✓ get_active_environment method exists")
            try:
                result = directory_paths_db.get_active_environment()
                print(f"   ✓ get_active_environment() returned: {result}")
            except Exception as e:
                print(f"   ✗ get_active_environment() error: {e}")
        else:
            print("   ✗ get_active_environment method does NOT exist")
        
        # Test if set_active_environment exists
        if hasattr(directory_paths_db, 'set_active_environment'):
            print("   ✓ set_active_environment method exists")
        else:
            print("   ✗ set_active_environment method does NOT exist")
        
        # Check database file
        print(f"\n4. Database file: {directory_paths_db.db_path}")
        if os.path.exists(directory_paths_db.db_path):
            print(f"   ✓ Database file exists, size: {os.path.getsize(directory_paths_db.db_path)} bytes")
        else:
            print("   ✗ Database file does not exist")
        
        # Check database tables
        import sqlite3
        try:
            conn = sqlite3.connect(directory_paths_db.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"   Tables: {tables}")
            
            if 'active_environment' in tables:
                cursor.execute("SELECT * FROM active_environment")
                active_env_data = cursor.fetchall()
                print(f"   active_environment table data: {active_env_data}")
            
            conn.close()
        except Exception as e:
            print(f"   Database query error: {e}")
        
    except Exception as e:
        print(f"Error importing or testing DirectoryPathsDB: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_ios_db_methods()
