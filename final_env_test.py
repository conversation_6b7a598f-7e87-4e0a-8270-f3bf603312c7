#!/usr/bin/env python3
"""
Final test to verify environment variable resolution with actual ActionFactory execution
"""

import sys
import os

# Add both app directories to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

def test_ios_action_factory_resolution():
    """Test iOS ActionFactory environment variable resolution"""
    print("=== TESTING iOS ActionFactory RESOLUTION ===")
    
    try:
        from actions.action_factory import ActionFactory
        
        # Create ActionFactory without device controller for testing
        action_factory = ActionFactory(None)
        
        # Test parameters with environment variable placeholder
        test_params = {'package_id': 'env[package_id]'}
        print(f"Input parameters: {test_params}")
        
        # Call apply_environment_variables directly to test resolution
        try:
            resolved_params = test_params.copy()
            action_factory.apply_environment_variables(resolved_params)
            print(f"Resolved parameters: {resolved_params}")
            
            # Check if resolution occurred
            if resolved_params['package_id'] != 'env[package_id]':
                print(f"✓ Environment variable resolved: 'env[package_id]' -> '{resolved_params['package_id']}'")
                return True
            else:
                print("✗ Environment variable was not resolved")
                return False
                
        except Exception as e:
            print(f"✗ Error during resolution: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Error creating iOS ActionFactory: {e}")
        return False

def test_android_action_factory_resolution():
    """Test Android ActionFactory environment variable resolution"""
    print("\n=== TESTING ANDROID ActionFactory RESOLUTION ===")
    
    try:
        from app_android.actions.action_factory import ActionFactory
        
        # Create ActionFactory without device controller for testing
        action_factory = ActionFactory(None)
        
        # Test parameters with environment variable placeholder
        test_params = {'package_id': 'env[package_id]'}
        print(f"Input parameters: {test_params}")
        
        # Call apply_environment_variables directly to test resolution
        try:
            resolved_params = test_params.copy()
            action_factory.apply_environment_variables(resolved_params)
            print(f"Resolved parameters: {resolved_params}")
            
            # Check if resolution occurred
            if resolved_params['package_id'] != 'env[package_id]':
                print(f"✓ Environment variable resolved: 'env[package_id]' -> '{resolved_params['package_id']}'")
                return True
            else:
                print("✗ Environment variable was not resolved")
                return False
                
        except Exception as e:
            print(f"✗ Error during resolution: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Error creating Android ActionFactory: {e}")
        return False

def main():
    """Run final verification tests"""
    print("FINAL ENVIRONMENT VARIABLE RESOLUTION TEST\n")
    
    ios_success = test_ios_action_factory_resolution()
    android_success = test_android_action_factory_resolution()
    
    print("\n=== FINAL TEST RESULTS ===")
    print(f"iOS Environment Resolution: {'✓ WORKING' if ios_success else '✗ FAILED'}")
    print(f"Android Environment Resolution: {'✓ WORKING' if android_success else '✗ FAILED'}")
    
    if ios_success and android_success:
        print("\n🎉 SUCCESS! Environment variable resolution is working correctly!")
        print("\nThe original issue has been resolved:")
        print("- Environment databases are properly configured")
        print("- Active environments are set correctly")
        print("- Environment variable resolution is working")
        print("- The system should now show successful substitutions instead of '0 substitutions made'")
    else:
        print("\n⚠️ Some issues remain. Check the error messages above.")

if __name__ == "__main__":
    main()
