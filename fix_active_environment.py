#!/usr/bin/env python3
"""
Fix the active environment settings for both iOS and Android
"""

import sys
import os

# Add both app directories to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

def fix_ios_active_environment():
    """Set the iOS active environment to a proper iOS environment"""
    print("=== FIXING iOS ACTIVE ENVIRONMENT ===")
    
    from utils.directory_paths_db import directory_paths_db as ios_db
    
    # Get all environments
    environments = ios_db.get_all_environments()
    
    # Find iOS environments (those with iOS-specific variables)
    ios_environments = []
    for env in environments:
        variables = ios_db.get_variables_for_environment(env['id'])
        # Check if it has iOS-specific variables like 'appid' or if package_id contains 'au.com.kmart'
        has_ios_vars = any(var['name'] in ['appid', 'tfappid'] for var in variables)
        has_ios_package = any(var['name'] == 'package_id' and 'au.com.kmart' in var['current_value'] for var in variables)
        
        if has_ios_vars or has_ios_package:
            ios_environments.append(env)
    
    print(f"Found {len(ios_environments)} iOS environments:")
    for env in ios_environments:
        print(f"  - ID {env['id']}: {env['name']}")
    
    if ios_environments:
        # Set the first iOS environment as active
        target_env = ios_environments[0]
        print(f"Setting active environment to ID {target_env['id']}: {target_env['name']}")
        success = ios_db.set_active_environment(target_env['id'])
        if success:
            print("✓ iOS active environment updated successfully")
        else:
            print("✗ Failed to update iOS active environment")
    else:
        print("⚠ No iOS environments found")

def fix_android_active_environment():
    """Set the Android active environment to a proper Android environment"""
    print("\n=== FIXING ANDROID ACTIVE ENVIRONMENT ===")
    
    from app_android.utils.directory_paths_db import directory_paths_db as android_db
    
    # Get all environments
    environments = android_db.get_all_environments()
    
    # Find Android environments (those with Android package IDs)
    android_environments = []
    for env in environments:
        variables = android_db.get_variables_for_environment(env['id'])
        # Check if it has Android package_id
        has_android_package = any(var['name'] == 'package_id' and 'com.kmart.android' in var['current_value'] for var in variables)
        
        if has_android_package or 'ANDROID' in env['name'].upper():
            android_environments.append(env)
    
    print(f"Found {len(android_environments)} Android environments:")
    for env in android_environments:
        print(f"  - ID {env['id']}: {env['name']}")
    
    if android_environments:
        # Set the first Android environment as active
        target_env = android_environments[0]
        print(f"Setting active environment to ID {target_env['id']}: {target_env['name']}")
        try:
            success = android_db.set_active_environment(target_env['id'])
            if success:
                print("✓ Android active environment updated successfully")
            else:
                print("✗ Failed to update Android active environment")
        except AttributeError:
            print("⚠ Android database doesn't support active environment setting")
    else:
        print("⚠ No Android environments found")

def create_final_test_script():
    """Create a final test script to verify environment variable resolution with actual values"""
    print("\n=== CREATING FINAL TEST SCRIPT ===")
    
    test_script = '''#!/usr/bin/env python3
"""
Final test to verify environment variable resolution with actual ActionFactory execution
"""

import sys
import os

# Add both app directories to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

def test_ios_action_factory_resolution():
    """Test iOS ActionFactory environment variable resolution"""
    print("=== TESTING iOS ActionFactory RESOLUTION ===")
    
    try:
        from actions.action_factory import ActionFactory
        
        # Create ActionFactory without device controller for testing
        action_factory = ActionFactory(None)
        
        # Test parameters with environment variable placeholder
        test_params = {'package_id': 'env[package_id]'}
        print(f"Input parameters: {test_params}")
        
        # Call apply_environment_variables directly to test resolution
        try:
            resolved_params = test_params.copy()
            action_factory.apply_environment_variables(resolved_params)
            print(f"Resolved parameters: {resolved_params}")
            
            # Check if resolution occurred
            if resolved_params['package_id'] != 'env[package_id]':
                print(f"✓ Environment variable resolved: 'env[package_id]' -> '{resolved_params['package_id']}'")
                return True
            else:
                print("✗ Environment variable was not resolved")
                return False
                
        except Exception as e:
            print(f"✗ Error during resolution: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Error creating iOS ActionFactory: {e}")
        return False

def test_android_action_factory_resolution():
    """Test Android ActionFactory environment variable resolution"""
    print("\\n=== TESTING ANDROID ActionFactory RESOLUTION ===")
    
    try:
        from app_android.actions.action_factory import ActionFactory
        
        # Create ActionFactory without device controller for testing
        action_factory = ActionFactory(None)
        
        # Test parameters with environment variable placeholder
        test_params = {'package_id': 'env[package_id]'}
        print(f"Input parameters: {test_params}")
        
        # Call apply_environment_variables directly to test resolution
        try:
            resolved_params = test_params.copy()
            action_factory.apply_environment_variables(resolved_params)
            print(f"Resolved parameters: {resolved_params}")
            
            # Check if resolution occurred
            if resolved_params['package_id'] != 'env[package_id]':
                print(f"✓ Environment variable resolved: 'env[package_id]' -> '{resolved_params['package_id']}'")
                return True
            else:
                print("✗ Environment variable was not resolved")
                return False
                
        except Exception as e:
            print(f"✗ Error during resolution: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Error creating Android ActionFactory: {e}")
        return False

def main():
    """Run final verification tests"""
    print("FINAL ENVIRONMENT VARIABLE RESOLUTION TEST\\n")
    
    ios_success = test_ios_action_factory_resolution()
    android_success = test_android_action_factory_resolution()
    
    print("\\n=== FINAL TEST RESULTS ===")
    print(f"iOS Environment Resolution: {'✓ WORKING' if ios_success else '✗ FAILED'}")
    print(f"Android Environment Resolution: {'✓ WORKING' if android_success else '✗ FAILED'}")
    
    if ios_success and android_success:
        print("\\n🎉 SUCCESS! Environment variable resolution is working correctly!")
        print("\\nThe original issue has been resolved:")
        print("- Environment databases are properly configured")
        print("- Active environments are set correctly")
        print("- Environment variable resolution is working")
        print("- The system should now show successful substitutions instead of '0 substitutions made'")
    else:
        print("\\n⚠️ Some issues remain. Check the error messages above.")

if __name__ == "__main__":
    main()
'''
    
    with open('final_env_test.py', 'w') as f:
        f.write(test_script)
    
    print("Created final_env_test.py")

def main():
    """Main function to fix active environments"""
    print("FIXING ACTIVE ENVIRONMENT SETTINGS\n")
    
    fix_ios_active_environment()
    fix_android_active_environment()
    create_final_test_script()
    
    print("\n=== SUMMARY ===")
    print("✓ Active environments have been configured")
    print("✓ Final test script created")
    print("\nRun: python3 final_env_test.py")
    print("This will test the complete environment variable resolution system")

if __name__ == "__main__":
    main()
