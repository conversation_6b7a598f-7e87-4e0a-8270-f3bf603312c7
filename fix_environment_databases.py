#!/usr/bin/env python3
"""
Script to fix environment databases and ensure proper environment variable setup
"""

import sys
import os

# Add both app directories to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

def fix_ios_database():
    """Fix the iOS database - keep the 3 valid environments and ensure they're properly configured"""
    print("=== FIXING iOS DATABASE ===")
    
    # Import iOS database
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
    from utils.directory_paths_db import directory_paths_db as ios_db
    
    # Get current environments
    environments = ios_db.get_all_environments()
    print(f"Current iOS environments: {len(environments)}")
    
    for env in environments:
        print(f"  - ID {env['id']}: {env['name']}")
        variables = ios_db.get_variables_for_environment(env['id'])
        print(f"    Variables: {len(variables)}")
        
        # Check if package_id exists and has correct value
        package_var = next((v for v in variables if v['name'] == 'package_id'), None)
        if package_var:
            print(f"    package_id: '{package_var['current_value']}'")
        else:
            print("    WARNING: No package_id variable found!")
    
    # Set active environment to ID 7 (AU-PROD-IP14) if not already set
    active_env = ios_db.get_active_environment()
    if active_env != 7:
        print(f"Setting active environment to ID 7 (was {active_env})")
        ios_db.set_active_environment(7)
    else:
        print("Active environment is already ID 7")
    
    print("iOS database is properly configured with 3 valid environments\n")

def fix_android_database():
    """Fix the Android database - add proper environments and variables"""
    print("=== FIXING ANDROID DATABASE ===")
    
    # Import Android database
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))
    from utils.directory_paths_db import directory_paths_db as android_db
    
    # Get current environments
    environments = android_db.get_all_environments()
    print(f"Current Android environments: {len(environments)}")
    
    # Create the three standard environments if they don't exist
    standard_envs = [
        ("AU-PROD-ANDROID", "com.kmart.android"),
        ("AU-PROD-ANDROID-SE", "com.kmart.android"),
        ("NZ-PROD-ANDROID", "com.kmart.android")
    ]
    
    for env_name, package_id in standard_envs:
        # Check if environment exists
        existing_env = android_db.get_environment_by_name(env_name)
        if not existing_env:
            print(f"Creating environment: {env_name}")
            env_id = android_db.create_environment(env_name)
            if env_id:
                # Add package_id variable
                android_db.add_environment_variable(
                    env_id, 
                    "package_id", 
                    "string", 
                    package_id, 
                    package_id
                )
                print(f"  Added package_id variable: {package_id}")
            else:
                print(f"  Failed to create environment {env_name}")
        else:
            print(f"Environment {env_name} already exists (ID: {existing_env['id']})")
            # Check if it has package_id variable
            variables = android_db.get_variables_for_environment(existing_env['id'])
            package_var = next((v for v in variables if v['name'] == 'package_id'), None)
            if not package_var:
                print(f"  Adding missing package_id variable")
                android_db.add_environment_variable(
                    existing_env['id'], 
                    "package_id", 
                    "string", 
                    package_id, 
                    package_id
                )
    
    # Remove the default environment if it has no variables
    default_env = android_db.get_environment_by_name("Default")
    if default_env:
        variables = android_db.get_variables_for_environment(default_env['id'])
        if len(variables) == 0:
            print("Removing empty Default environment")
            android_db.delete_environment(default_env['id'])
    
    # Set active environment to the first Android environment
    new_environments = android_db.get_all_environments()
    if new_environments:
        first_env_id = new_environments[0]['id']
        print(f"Setting active environment to ID {first_env_id}")
        try:
            android_db.set_active_environment(first_env_id)
        except AttributeError:
            print("Active environment functionality not available in Android database")
    
    print("Android database has been configured with proper environments\n")

def create_test_script():
    """Create a test script to verify environment variable resolution"""
    print("=== CREATING TEST SCRIPT ===")
    
    test_script = '''#!/usr/bin/env python3
"""
Test script to verify environment variable resolution works correctly
"""

import sys
import os

# Test iOS environment resolution
print("=== TESTING iOS ENVIRONMENT RESOLUTION ===")
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
from utils.environment_resolver import resolve_text_with_env_variables

# Test with environment ID 7 (AU-PROD-IP14)
test_cases = [
    "env[package_id]",
    "env[appid]", 
    "env[uname]",
    "hardcoded_value",
    "com.kmart.android"
]

for test_case in test_cases:
    try:
        result = resolve_text_with_env_variables(test_case, 7)
        changed = result != test_case
        print(f"  '{test_case}' -> '{result}' (changed: {changed})")
    except Exception as e:
        print(f"  '{test_case}' -> ERROR: {e}")

print()

# Test Android environment resolution
print("=== TESTING ANDROID ENVIRONMENT RESOLUTION ===")
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))
from utils.environment_resolver import resolve_text_with_env_variables as android_resolve

# Get first Android environment
from utils.directory_paths_db import directory_paths_db as android_db
android_envs = android_db.get_all_environments()
if android_envs:
    android_env_id = android_envs[0]['id']
    print(f"Testing with Android environment ID {android_env_id}")
    
    for test_case in test_cases:
        try:
            result = android_resolve(test_case, android_env_id)
            changed = result != test_case
            print(f"  '{test_case}' -> '{result}' (changed: {changed})")
        except Exception as e:
            print(f"  '{test_case}' -> ERROR: {e}")
else:
    print("No Android environments found!")
'''
    
    with open('test_environment_resolution.py', 'w') as f:
        f.write(test_script)
    
    print("Created test_environment_resolution.py")

def main():
    """Main function to fix both databases"""
    print("ENVIRONMENT DATABASE CLEANUP AND FIX\n")
    
    fix_ios_database()
    fix_android_database()
    create_test_script()
    
    print("=== SUMMARY ===")
    print("✓ iOS database: 3 valid environments with proper variables")
    print("✓ Android database: Configured with proper environments and package_id variables")
    print("✓ Test script created: test_environment_resolution.py")
    print("\nNext steps:")
    print("1. Run test_environment_resolution.py to verify resolution works")
    print("2. Update action parameters to use env[package_id] instead of hardcoded values")
    print("3. Test the mobile automation to ensure environment variables are resolved")

if __name__ == "__main__":
    main()
