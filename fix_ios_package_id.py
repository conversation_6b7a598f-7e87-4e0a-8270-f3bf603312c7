#!/usr/bin/env python3
import sqlite3
import os
import sys

# Add the app directory to the path to import the database utility
sys.path.append('/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app')
from utils.directory_paths_db import DirectoryPathsDB

def fix_ios_package_id():
    """Add the missing package_id variable to iOS environments"""
    try:
        directory_paths_db = DirectoryPathsDB()
        
        # Get all environments
        environments = directory_paths_db.get_all_environments()
        print(f"Found {len(environments)} environments:")
        for env in environments:
            print(f"  ID: {env['id']}, Name: {env['name']}")
        
        if not environments:
            print("No environments found. Creating a default environment...")
            env_id = directory_paths_db.create_environment("Default")
            if env_id:
                print(f"Created default environment with ID: {env_id}")
                environments = [{'id': env_id, 'name': 'Default'}]
            else:
                print("Failed to create default environment")
                return
        
        # Add package_id variable to each environment
        ios_bundle_id = "au.com.kmart"  # Correct iOS bundle ID
        
        for env in environments:
            env_id = env['id']
            env_name = env['name']
            
            print(f"\nProcessing environment '{env_name}' (ID: {env_id})...")
            
            # Check if package_id already exists
            variables = directory_paths_db.get_variables_for_environment(env_id)
            package_id_exists = any(var['name'] == 'package_id' for var in variables)
            
            if package_id_exists:
                print(f"  package_id already exists in environment '{env_name}'")
                # Update the existing package_id
                for var in variables:
                    if var['name'] == 'package_id':
                        print(f"  Current package_id value: '{var['current_value']}'")
                        if var['current_value'] != ios_bundle_id:
                            print(f"  Updating package_id to: '{ios_bundle_id}'")
                            success = directory_paths_db.update_environment_variable(
                                var['id'], 'package_id', 'default', ios_bundle_id, ios_bundle_id
                            )
                            if success:
                                print(f"  ✓ Successfully updated package_id")
                            else:
                                print(f"  ✗ Failed to update package_id")
                        else:
                            print(f"  ✓ package_id already has correct value")
            else:
                print(f"  Adding package_id variable with value: '{ios_bundle_id}'")
                var_id = directory_paths_db.add_environment_variable(
                    env_id, 'package_id', 'default', ios_bundle_id, ios_bundle_id
                )
                if var_id:
                    print(f"  ✓ Successfully added package_id variable with ID: {var_id}")
                else:
                    print(f"  ✗ Failed to add package_id variable")
        
        print("\n=== VERIFICATION ===")
        # Verify the changes
        for env in environments:
            env_id = env['id']
            env_name = env['name']
            variables = directory_paths_db.get_variables_for_environment(env_id)
            package_id_var = next((var for var in variables if var['name'] == 'package_id'), None)
            if package_id_var:
                print(f"Environment '{env_name}': package_id = '{package_id_var['current_value']}'")
            else:
                print(f"Environment '{env_name}': package_id NOT FOUND")
                
    except Exception as e:
        print(f"Error fixing iOS package_id: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_ios_package_id()