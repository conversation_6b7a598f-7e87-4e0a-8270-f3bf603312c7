#!/usr/bin/env python3
"""
Test Case Environment Variable Cleanup Tool

This tool scans test case files and replaces hardcoded values with environment variable placeholders
to ensure consistent environment variable usage across all test cases.
"""

import json
import os
import sys
import re
from pathlib import Path

# Mapping of hardcoded values to environment variable placeholders
HARDCODED_TO_ENV_VAR_MAPPING = {
    # Package IDs
    'au.com.kmart': 'env[appid]',
    'nz.com.kmart': 'env[appid]',
    'com.kmart.android': 'env[package_id]',
    
    # Common hardcoded values that should use environment variables
    'Wonderbaby@5': 'env[pwd]',
    '<EMAIL>': 'env[uname]',
    '<EMAIL>': 'env[uname1]',
    '<EMAIL>': 'env[uname]',
    'Uno card': 'env[uno]',
    '305 238 Flinders': 'env[deliver-address]',
    '6 Quay Street': 'env[deliver-address]',
    'com.apple.TestFlight': 'env[tfappid]',
}

def scan_test_case_file(file_path):
    """Scan a test case file for hardcoded values that should be environment variables"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        data = json.loads(content)
        issues_found = []
        
        # Check each action in the test case
        if 'actions' in data:
            for i, action in enumerate(data['actions']):
                for key, value in action.items():
                    if isinstance(value, str) and value in HARDCODED_TO_ENV_VAR_MAPPING:
                        issues_found.append({
                            'action_index': i,
                            'action_id': action.get('action_id', 'unknown'),
                            'action_type': action.get('type', 'unknown'),
                            'parameter': key,
                            'hardcoded_value': value,
                            'suggested_env_var': HARDCODED_TO_ENV_VAR_MAPPING[value]
                        })
        
        return issues_found
        
    except Exception as e:
        print(f"Error scanning {file_path}: {e}")
        return []

def fix_test_case_file(file_path, dry_run=True):
    """Fix hardcoded values in a test case file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        data = json.loads(content)
        changes_made = []
        
        # Fix each action in the test case
        if 'actions' in data:
            for i, action in enumerate(data['actions']):
                for key, value in action.items():
                    if isinstance(value, str) and value in HARDCODED_TO_ENV_VAR_MAPPING:
                        old_value = value
                        new_value = HARDCODED_TO_ENV_VAR_MAPPING[value]
                        
                        if not dry_run:
                            action[key] = new_value
                            
                        changes_made.append({
                            'action_index': i,
                            'action_id': action.get('action_id', 'unknown'),
                            'action_type': action.get('type', 'unknown'),
                            'parameter': key,
                            'old_value': old_value,
                            'new_value': new_value
                        })
        
        # Save the file if not dry run
        if not dry_run and changes_made:
            # Create backup
            backup_path = f"{file_path}.backup_{int(time.time())}"
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  Created backup: {backup_path}")
            
            # Save updated file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"  Updated file: {file_path}")
        
        return changes_made
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return []

def scan_all_test_cases(test_cases_dir):
    """Scan all test case files in the directory"""
    test_cases_dir = Path(test_cases_dir)
    
    if not test_cases_dir.exists():
        print(f"Test cases directory not found: {test_cases_dir}")
        return
    
    print(f"Scanning test cases in: {test_cases_dir}")
    print("=" * 60)
    
    total_files = 0
    total_issues = 0
    files_with_issues = 0
    
    for json_file in test_cases_dir.glob("*.json"):
        if json_file.name.endswith('.bak'):
            continue
            
        total_files += 1
        issues = scan_test_case_file(json_file)
        
        if issues:
            files_with_issues += 1
            total_issues += len(issues)
            print(f"\n📁 {json_file.name}")
            print(f"   Found {len(issues)} hardcoded values:")
            
            for issue in issues:
                print(f"   • Action {issue['action_index']} ({issue['action_type']}): "
                      f"{issue['parameter']} = '{issue['hardcoded_value']}' "
                      f"→ '{issue['suggested_env_var']}'")
    
    print("\n" + "=" * 60)
    print(f"SUMMARY:")
    print(f"  Total files scanned: {total_files}")
    print(f"  Files with hardcoded values: {files_with_issues}")
    print(f"  Total hardcoded values found: {total_issues}")
    
    return total_files, files_with_issues, total_issues

def fix_all_test_cases(test_cases_dir, dry_run=True):
    """Fix all test case files in the directory"""
    import time
    
    test_cases_dir = Path(test_cases_dir)
    
    if not test_cases_dir.exists():
        print(f"Test cases directory not found: {test_cases_dir}")
        return
    
    mode = "DRY RUN" if dry_run else "FIXING"
    print(f"{mode}: Fixing test cases in: {test_cases_dir}")
    print("=" * 60)
    
    total_files = 0
    total_changes = 0
    files_changed = 0
    
    for json_file in test_cases_dir.glob("*.json"):
        if json_file.name.endswith('.bak'):
            continue
            
        total_files += 1
        changes = fix_test_case_file(json_file, dry_run=dry_run)
        
        if changes:
            files_changed += 1
            total_changes += len(changes)
            print(f"\n📁 {json_file.name}")
            print(f"   {'Would make' if dry_run else 'Made'} {len(changes)} changes:")
            
            for change in changes:
                print(f"   • Action {change['action_index']} ({change['action_type']}): "
                      f"{change['parameter']} = '{change['old_value']}' "
                      f"→ '{change['new_value']}'")
    
    print("\n" + "=" * 60)
    print(f"SUMMARY:")
    print(f"  Total files processed: {total_files}")
    print(f"  Files {'that would be' if dry_run else ''} changed: {files_changed}")
    print(f"  Total changes {'that would be' if dry_run else ''} made: {total_changes}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python3 fix_test_case_env_vars.py scan <test_cases_dir>")
        print("  python3 fix_test_case_env_vars.py fix <test_cases_dir> [--dry-run]")
        sys.exit(1)
    
    command = sys.argv[1]
    test_cases_dir = sys.argv[2] if len(sys.argv) > 2 else "ios_data/test_cases"
    
    if command == "scan":
        scan_all_test_cases(test_cases_dir)
    elif command == "fix":
        dry_run = "--dry-run" in sys.argv or len(sys.argv) < 4
        fix_all_test_cases(test_cases_dir, dry_run=dry_run)
    else:
        print(f"Unknown command: {command}")
        print("Use 'scan' or 'fix'")
