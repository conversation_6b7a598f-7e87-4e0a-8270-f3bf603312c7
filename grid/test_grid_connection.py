#!/usr/bin/env python3
"""
Grid Connection Test Script
This script tests the complete Appium Grid setup and device connections.
"""

import sys
import os
import time
import requests
import json
from typing import Dict
from selenium import webdriver
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from appium import webdriver as appium_webdriver
from appium.options.ios import XCUITestOptions
from appium.options.android import UiAutomator2Options

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from grid_config import GridConfig
from grid_manager import GridManager

class GridConnectionTester:
    def __init__(self):
        self.config = GridConfig()
        self.grid_manager = GridManager()
        self.test_results = {
            'grid_hub': False,
            'ios_appium': False,
            'android_appium': False,
            'ios_node_registration': False,
            'android_node_registration': False,
            'ios_session_creation': False,
            'android_session_creation': False,
            'device_detection': False
        }
    
    def test_grid_hub(self) -> bool:
        """Test Grid Hub connectivity"""
        print("🔍 Testing Grid Hub connection...")
        try:
            response = requests.get(f"{self.config.GRID_HUB_URL}/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('value', {}).get('ready'):
                    print("✅ Grid Hub is ready and accessible")
                    return True
                else:
                    print("❌ Grid Hub is not ready")
                    return False
            else:
                print(f"❌ Grid Hub returned HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Grid Hub connection failed: {e}")
            return False
    
    def test_appium_server(self, url: str, platform: str) -> bool:
        """Test Appium server connectivity"""
        print(f"🔍 Testing {platform} Appium server ({url})...")
        try:
            response = requests.get(f"{url}/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('value', {}).get('ready'):
                    print(f"✅ {platform} Appium server is ready")
                    return True
                else:
                    print(f"❌ {platform} Appium server is not ready")
                    return False
            else:
                print(f"❌ {platform} Appium server returned HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ {platform} Appium server connection failed: {e}")
            return False
    
    def test_node_registration(self, platform: str) -> bool:
        """Test if nodes are registered with the Grid"""
        print(f"🔍 Testing {platform} node registration...")
        try:
            response = requests.get(f"{self.config.GRID_HUB_URL}/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                nodes = data.get('value', {}).get('nodes', [])
                
                platform_nodes = []
                for node in nodes:
                    slots = node.get('slots', [])
                    for slot in slots:
                        stereotype = slot.get('stereotype', {})
                        if stereotype.get('platformName', '').lower() == platform.lower():
                            platform_nodes.append(node)
                            break
                
                if platform_nodes:
                    print(f"✅ Found {len(platform_nodes)} {platform} node(s) registered")
                    return True
                else:
                    print(f"❌ No {platform} nodes registered with Grid")
                    return False
            else:
                print(f"❌ Failed to check node registration: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Node registration check failed: {e}")
            return False
    
    def test_session_creation(self, platform: str) -> bool:
        """Test creating a session through the Grid"""
        print(f"🔍 Testing {platform} session creation...")
        driver = None
        try:
            if platform.lower() == 'ios':
                options = XCUITestOptions()
                options.platform_name = "iOS"
                options.device_name = "iPhone"
                options.automation_name = "XCUITest"
                options.no_reset = True
                
                driver = appium_webdriver.Remote(
                    command_executor=f"{self.config.GRID_HUB_URL}/wd/hub",
                    options=options
                )
            elif platform.lower() == 'android':
                options = UiAutomator2Options()
                options.platform_name = "Android"
                options.device_name = "Android Device"
                options.automation_name = "UiAutomator2"
                options.no_reset = True
                
                driver = appium_webdriver.Remote(
                    command_executor=f"{self.config.GRID_HUB_URL}/wd/hub",
                    options=options
                )
            
            if driver:
                # Simple test - get page source
                source = driver.page_source
                if source:
                    print(f"✅ {platform} session created successfully")
                    return True
                else:
                    print(f"❌ {platform} session created but no page source")
                    return False
            else:
                print(f"❌ Failed to create {platform} session")
                return False
                
        except Exception as e:
            print(f"❌ {platform} session creation failed: {e}")
            return False
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass
    
    def test_device_detection(self) -> bool:
        """Test device detection functionality"""
        print("🔍 Testing device detection...")
        try:
            verification = self.grid_manager.verify_device_connections()
            
            if verification['grid_available']:
                total_devices = verification['total_devices']
                if total_devices > 0:
                    print(f"✅ Device detection successful - found {total_devices} device(s)")
                    print(f"   iOS devices: {len(verification['ios_devices'])}")
                    print(f"   Android devices: {len(verification['android_devices'])}")
                    return True
                else:
                    print("⚠️  Device detection working but no devices found")
                    return False
            else:
                print("❌ Grid not available for device detection")
                return False
        except Exception as e:
            print(f"❌ Device detection failed: {e}")
            return False
    
    def run_comprehensive_test(self) -> Dict[str, bool]:
        """Run all tests and return results"""
        print("🚀 Starting Comprehensive Grid Connection Test")
        print("=" * 60)
        
        # Test Grid Hub
        self.test_results['grid_hub'] = self.test_grid_hub()
        time.sleep(1)
        
        # Test Appium servers
        self.test_results['ios_appium'] = self.test_appium_server(
            "http://127.0.0.1:4723", "iOS"
        )
        time.sleep(1)
        
        self.test_results['android_appium'] = self.test_appium_server(
            "http://127.0.0.1:4724", "Android"
        )
        time.sleep(1)
        
        # Test node registration
        if self.test_results['grid_hub']:
            self.test_results['ios_node_registration'] = self.test_node_registration("iOS")
            time.sleep(1)
            
            self.test_results['android_node_registration'] = self.test_node_registration("Android")
            time.sleep(1)
        
        # Test device detection
        self.test_results['device_detection'] = self.test_device_detection()
        time.sleep(1)
        
        # Test session creation (only if nodes are registered)
        if self.test_results['ios_node_registration']:
            self.test_results['ios_session_creation'] = self.test_session_creation("iOS")
            time.sleep(2)
        
        if self.test_results['android_node_registration']:
            self.test_results['android_session_creation'] = self.test_session_creation("Android")
            time.sleep(2)
        
        return self.test_results
    
    def print_summary(self, results: Dict[str, bool]):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 Test Summary")
        print("=" * 60)
        
        passed = sum(1 for v in results.values() if v)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            formatted_name = test_name.replace('_', ' ').title()
            print(f"{formatted_name:<25} {status}")
        
        print(f"\n📈 Overall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Your Appium Grid is working correctly.")
        else:
            print("\n💡 Troubleshooting recommendations:")
            
            if not results['grid_hub']:
                print("   • Start the Grid Hub: java -jar selenium-server-*.jar hub")
            
            if not results['ios_appium']:
                print("   • Start iOS Appium server: appium server --port 4723")
            
            if not results['android_appium']:
                print("   • Start Android Appium server: appium server --port 4724")
            
            if not results['ios_node_registration'] or not results['android_node_registration']:
                print("   • Check node configuration files and restart Grid")
                print("   • Run device detection: python3 grid/device_detector.py")
            
            if not results['device_detection']:
                print("   • Connect devices and ensure they're authorized for debugging")
                print("   • For iOS: Check Xcode and iOS developer settings")
                print("   • For Android: Enable USB debugging and authorize computer")

def main():
    tester = GridConnectionTester()
    results = tester.run_comprehensive_test()
    tester.print_summary(results)
    
    # Return appropriate exit code
    return 0 if all(results.values()) else 1

if __name__ == '__main__':
    sys.exit(main())