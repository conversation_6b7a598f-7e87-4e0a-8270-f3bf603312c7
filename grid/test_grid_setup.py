#!/usr/bin/env python3
"""
Appium Grid Setup Test Script

This script tests the Appium Grid configuration to ensure:
1. Grid Hub is accessible
2. iOS and Android nodes are registered
3. Device connections route through the Grid
4. Session isolation works correctly
5. No port conflicts exist
"""

import sys
import os
import time
import requests
import subprocess
import json
from typing import Dict, List, Optional

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from grid_config import GridConfig
except ImportError:
    print("Error: grid_config module not found. Make sure it's in the project root.")
    sys.exit(1)

class GridTester:
    def __init__(self):
        self.grid_config = GridConfig()
        self.test_results = []
        
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if passed else "FAIL"
        result = f"[{status}] {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'message': message
        })
        
    def test_grid_hub_status(self) -> bool:
        """Test if Grid Hub is accessible"""
        try:
            response = requests.get(f"{GridConfig.GRID_HUB_URL}/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                ready = data.get('value', {}).get('ready', False)
                self.log_test("Grid Hub Status", ready, f"Hub ready: {ready}")
                return ready
            else:
                self.log_test("Grid Hub Status", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Grid Hub Status", False, f"Connection failed: {e}")
            return False
            
    def test_grid_nodes(self) -> bool:
        """Test if nodes are registered with the Grid"""
        try:
            response = requests.get(f"{GridConfig.GRID_HUB_URL}/status", timeout=5)
            if response.status_code != 200:
                self.log_test("Grid Nodes Registration", False, "Hub not accessible")
                return False
                
            data = response.json()
            nodes = data.get('value', {}).get('nodes', [])
            
            ios_node_found = False
            android_node_found = False
            
            for node in nodes:
                node_id = node.get('id', '')
                slots = node.get('slots', [])
                
                for slot in slots:
                    capabilities = slot.get('stereotype', {})
                    platform = capabilities.get('platformName', '').lower()
                    
                    if platform == 'ios':
                        ios_node_found = True
                    elif platform == 'android':
                        android_node_found = True
                        
            self.log_test("iOS Node Registration", ios_node_found, 
                         "iOS node found" if ios_node_found else "iOS node not found")
            self.log_test("Android Node Registration", android_node_found,
                         "Android node found" if android_node_found else "Android node not found")
                         
            return ios_node_found and android_node_found
            
        except Exception as e:
            self.log_test("Grid Nodes Registration", False, f"Error: {e}")
            return False
            
    def test_port_availability(self) -> bool:
        """Test that required ports are available or in use by correct services"""
        ports_to_check = {
            4444: "Grid Hub",
            4723: "iOS Appium Node", 
            4724: "Android Appium Node",
            8080: "iOS App",
            8081: "Android App"
        }
        
        all_ports_ok = True
        
        for port, service in ports_to_check.items():
            try:
                # Check if port is in use
                result = subprocess.run(
                    ["lsof", "-i", f":{port}"],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    # Port is in use - this is expected for Grid services
                    if port in [4444, 4723, 4724]:
                        self.log_test(f"Port {port} ({service})", True, "Port in use (expected)")
                    else:
                        # App ports - check if they're available for apps
                        self.log_test(f"Port {port} ({service})", True, "Port available for app")
                else:
                    # Port is free
                    if port in [4444, 4723, 4724]:
                        self.log_test(f"Port {port} ({service})", False, "Grid service not running")
                        all_ports_ok = False
                    else:
                        self.log_test(f"Port {port} ({service})", True, "Port free for app")
                        
            except Exception as e:
                self.log_test(f"Port {port} ({service})", False, f"Error checking port: {e}")
                all_ports_ok = False
                
        return all_ports_ok
        
    def test_grid_config_module(self) -> bool:
        """Test the GridConfig module functionality"""
        try:
            # Test iOS connection URL
            ios_url = self.grid_config.get_connection_url('ios')
            expected_ios = GridConfig.GRID_HUB_URL if self.grid_config.is_grid_available() else GridConfig.IOS_DIRECT_URL
            ios_correct = ios_url == expected_ios
            self.log_test("iOS Connection URL", ios_correct, f"URL: {ios_url}")
            
            # Test Android connection URL
            android_url = self.grid_config.get_connection_url('android')
            expected_android = GridConfig.GRID_HUB_URL if self.grid_config.is_grid_available() else GridConfig.ANDROID_DIRECT_URL
            android_correct = android_url == expected_android
            self.log_test("Android Connection URL", android_correct, f"URL: {android_url}")
            
            # Test grid availability check
            grid_available = self.grid_config.is_grid_available()
            self.log_test("Grid Availability Check", True, f"Grid available: {grid_available}")
            
            return ios_correct and android_correct
            
        except Exception as e:
            self.log_test("GridConfig Module", False, f"Error: {e}")
            return False
            
    def test_environment_variables(self) -> bool:
        """Test environment variable configuration"""
        grid_enabled = os.getenv('APPIUM_GRID_ENABLED', 'false').lower() == 'true'
        self.log_test("Environment Variable", True, f"APPIUM_GRID_ENABLED: {grid_enabled}")
        return True
        
    def run_all_tests(self) -> bool:
        """Run all tests and return overall result"""
        print("\n" + "="*60)
        print("APPIUM GRID SETUP TEST")
        print("="*60)
        
        tests = [
            self.test_environment_variables,
            self.test_grid_config_module,
            self.test_port_availability,
            self.test_grid_hub_status,
            self.test_grid_nodes
        ]
        
        all_passed = True
        for test in tests:
            try:
                result = test()
                if not result:
                    all_passed = False
            except Exception as e:
                print(f"Test failed with exception: {e}")
                all_passed = False
            print()  # Add spacing between test groups
            
        # Print summary
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        
        passed_count = sum(1 for result in self.test_results if result['passed'])
        total_count = len(self.test_results)
        
        print(f"Tests passed: {passed_count}/{total_count}")
        
        if not all_passed:
            print("\nFailed tests:")
            for result in self.test_results:
                if not result['passed']:
                    print(f"  - {result['test']}: {result['message']}")
                    
        print(f"\nOverall result: {'PASS' if all_passed else 'FAIL'}")
        
        if all_passed:
            print("\n✅ Appium Grid setup is working correctly!")
            print("\nNext steps:")
            print("1. Start your iOS app: python run.py")
            print("2. Start your Android app: python run_android.py")
            print("3. Both apps will automatically route through the Grid")
        else:
            print("\n❌ Appium Grid setup has issues that need to be resolved.")
            print("\nTroubleshooting:")
            print("1. Make sure the Grid is started: ./grid/start-grid.sh")
            print("2. Check Grid console: http://localhost:4444")
            print("3. Verify Appium nodes are running")
            
        return all_passed

def main():
    """Main function"""
    tester = GridTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()