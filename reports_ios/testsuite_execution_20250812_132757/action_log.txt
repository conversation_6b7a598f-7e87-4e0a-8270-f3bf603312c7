Action Log - 2025-08-12 14:52:13
================================================================================

[[14:52:12]] [INFO] Generating execution report...
[[14:52:12]] [WARNING] 2 tests failed.
[[14:52:12]] [SUCCESS] Screenshot refreshed
[[14:52:12]] [INFO] Refreshing screenshot...
[[14:52:12]] [SUCCESS] Screenshot refreshed successfully
[[14:52:12]] [SUCCESS] Screenshot refreshed
[[14:52:12]] [INFO] Refreshing screenshot...
[[14:52:09]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:52:09]] [SUCCESS] Screenshot refreshed successfully
[[14:52:09]] [SUCCESS] Screenshot refreshed
[[14:52:09]] [INFO] Refreshing screenshot...
[[14:51:56]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:51:56]] [SUCCESS] Screenshot refreshed successfully
[[14:51:56]] [SUCCESS] Screenshot refreshed
[[14:51:56]] [INFO] Refreshing screenshot...
[[14:51:52]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:51:52]] [SUCCESS] Screenshot refreshed successfully
[[14:51:52]] [SUCCESS] Screenshot refreshed
[[14:51:52]] [INFO] Refreshing screenshot...
[[14:51:48]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:51:48]] [SUCCESS] Screenshot refreshed successfully
[[14:51:48]] [SUCCESS] Screenshot refreshed
[[14:51:48]] [INFO] Refreshing screenshot...
[[14:51:41]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:51:41]] [SUCCESS] Screenshot refreshed successfully
[[14:51:41]] [SUCCESS] Screenshot refreshed
[[14:51:41]] [INFO] Refreshing screenshot...
[[14:51:35]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:51:35]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:51:35]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:51:35]] [INFO] vKo6Ox3YrP=running
[[14:51:35]] [INFO] Executing action 643/643: cleanupSteps action
[[14:51:35]] [INFO] Skipping remaining steps in failed test case (moving from action 641 to 642), but preserving cleanup steps
[[14:51:35]] [INFO] 2p13JoJbbA=fail
[[14:51:35]] [ERROR] Action 641 failed: Element not found or not tappable: xpath='//XCUIElementTypeButton[contains(@name,"Remove")]'
[[14:51:22]] [INFO] 2p13JoJbbA=running
[[14:51:22]] [INFO] Executing action 641/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:51:22]] [SUCCESS] Screenshot refreshed successfully
[[14:51:22]] [SUCCESS] Screenshot refreshed
[[14:51:22]] [INFO] Refreshing screenshot...
[[14:51:22]] [INFO] 2p13JoJbbA=pass
[[14:51:17]] [INFO] 2p13JoJbbA=running
[[14:51:17]] [INFO] Executing action 640/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:51:17]] [SUCCESS] Screenshot refreshed successfully
[[14:51:17]] [SUCCESS] Screenshot refreshed
[[14:51:17]] [INFO] Refreshing screenshot...
[[14:51:17]] [INFO] nyBidG0kHp=pass
[[14:51:05]] [INFO] nyBidG0kHp=running
[[14:51:05]] [INFO] Executing action 639/643: Swipe from (50%, 50%) to (50%, 20%)
[[14:51:05]] [SUCCESS] Screenshot refreshed successfully
[[14:51:04]] [SUCCESS] Screenshot refreshed
[[14:51:04]] [INFO] Refreshing screenshot...
[[14:51:04]] [INFO] w7I4F66YKQ=pass
[[14:50:59]] [INFO] w7I4F66YKQ=running
[[14:50:59]] [INFO] Executing action 638/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:50:59]] [SUCCESS] Screenshot refreshed successfully
[[14:50:59]] [SUCCESS] Screenshot refreshed
[[14:50:59]] [INFO] Refreshing screenshot...
[[14:50:59]] [INFO] F4NGh9HrLw=pass
[[14:50:54]] [INFO] F4NGh9HrLw=running
[[14:50:54]] [INFO] Executing action 637/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:50:53]] [SUCCESS] Screenshot refreshed successfully
[[14:50:53]] [SUCCESS] Screenshot refreshed
[[14:50:53]] [INFO] Refreshing screenshot...
[[14:50:53]] [INFO] VtMfqK1V9t=pass
[[14:50:33]] [INFO] VtMfqK1V9t=running
[[14:50:33]] [INFO] Executing action 636/643: Tap on element with accessibility_id: Add to bag
[[14:50:33]] [SUCCESS] Screenshot refreshed successfully
[[14:50:33]] [SUCCESS] Screenshot refreshed
[[14:50:33]] [INFO] Refreshing screenshot...
[[14:50:33]] [INFO] NOnuFzXy63=pass
[[14:50:28]] [INFO] NOnuFzXy63=running
[[14:50:28]] [INFO] Executing action 635/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:50:28]] [SUCCESS] Screenshot refreshed successfully
[[14:50:28]] [SUCCESS] Screenshot refreshed
[[14:50:28]] [INFO] Refreshing screenshot...
[[14:50:28]] [INFO] kz9lnCdwoH=pass
[[14:50:23]] [INFO] kz9lnCdwoH=running
[[14:50:23]] [INFO] Executing action 634/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[14:50:23]] [SUCCESS] Screenshot refreshed successfully
[[14:50:23]] [SUCCESS] Screenshot refreshed
[[14:50:23]] [INFO] Refreshing screenshot...
[[14:50:23]] [INFO] kz9lnCdwoH=pass
[[14:50:17]] [INFO] kz9lnCdwoH=running
[[14:50:17]] [INFO] Executing action 633/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:50:17]] [SUCCESS] Screenshot refreshed successfully
[[14:50:16]] [SUCCESS] Screenshot refreshed
[[14:50:16]] [INFO] Refreshing screenshot...
[[14:50:16]] [INFO] qIF9CVPc56=pass
[[14:50:12]] [INFO] qIF9CVPc56=running
[[14:50:12]] [INFO] Executing action 632/643: iOS Function: text - Text: "mat"
[[14:50:12]] [SUCCESS] Screenshot refreshed successfully
[[14:50:12]] [SUCCESS] Screenshot refreshed
[[14:50:12]] [INFO] Refreshing screenshot...
[[14:50:12]] [INFO] yEga5MkcRe=pass
[[14:50:08]] [INFO] yEga5MkcRe=running
[[14:50:08]] [INFO] Executing action 631/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:50:08]] [SUCCESS] Screenshot refreshed successfully
[[14:50:08]] [SUCCESS] Screenshot refreshed
[[14:50:08]] [INFO] Refreshing screenshot...
[[14:50:08]] [INFO] F4NGh9HrLw=pass
[[14:50:03]] [INFO] F4NGh9HrLw=running
[[14:50:03]] [INFO] Executing action 630/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:50:03]] [SUCCESS] Screenshot refreshed successfully
[[14:50:03]] [SUCCESS] Screenshot refreshed
[[14:50:03]] [INFO] Refreshing screenshot...
[[14:50:03]] [INFO] kz9lnCdwoH=pass
[[14:49:58]] [INFO] kz9lnCdwoH=running
[[14:49:58]] [INFO] Executing action 629/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[14:49:58]] [SUCCESS] Screenshot refreshed successfully
[[14:49:58]] [SUCCESS] Screenshot refreshed
[[14:49:58]] [INFO] Refreshing screenshot...
[[14:49:58]] [INFO] kz9lnCdwoH=pass
[[14:49:51]] [INFO] kz9lnCdwoH=running
[[14:49:51]] [INFO] Executing action 628/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:49:51]] [SUCCESS] Screenshot refreshed successfully
[[14:49:51]] [SUCCESS] Screenshot refreshed
[[14:49:51]] [INFO] Refreshing screenshot...
[[14:49:51]] [INFO] JRheDTvpJf=pass
[[14:49:47]] [INFO] JRheDTvpJf=running
[[14:49:47]] [INFO] Executing action 627/643: iOS Function: text - Text: "Kid toy"
[[14:49:47]] [SUCCESS] Screenshot refreshed successfully
[[14:49:46]] [SUCCESS] Screenshot refreshed
[[14:49:46]] [INFO] Refreshing screenshot...
[[14:49:46]] [INFO] yEga5MkcRe=pass
[[14:49:43]] [INFO] yEga5MkcRe=running
[[14:49:43]] [INFO] Executing action 626/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:49:42]] [SUCCESS] Screenshot refreshed successfully
[[14:49:42]] [SUCCESS] Screenshot refreshed
[[14:49:42]] [INFO] Refreshing screenshot...
[[14:49:42]] [INFO] F4NGh9HrLw=pass
[[14:49:37]] [INFO] F4NGh9HrLw=running
[[14:49:37]] [INFO] Executing action 625/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:49:37]] [SUCCESS] Screenshot refreshed successfully
[[14:49:37]] [SUCCESS] Screenshot refreshed
[[14:49:37]] [INFO] Refreshing screenshot...
[[14:49:37]] [INFO] XPEr3w6Zof=pass
[[14:49:33]] [INFO] XPEr3w6Zof=running
[[14:49:33]] [INFO] Executing action 624/643: Restart app: env[appid]
[[14:49:33]] [SUCCESS] Screenshot refreshed successfully
[[14:49:33]] [SUCCESS] Screenshot refreshed
[[14:49:33]] [INFO] Refreshing screenshot...
[[14:49:33]] [INFO] PiQRBWBe3E=pass
[[14:49:29]] [INFO] PiQRBWBe3E=running
[[14:49:29]] [INFO] Executing action 623/643: Tap on image: env[device-back-img]
[[14:49:28]] [SUCCESS] Screenshot refreshed successfully
[[14:49:28]] [SUCCESS] Screenshot refreshed
[[14:49:28]] [INFO] Refreshing screenshot...
[[14:49:28]] [INFO] GWoppouz1l=pass
[[14:49:25]] [INFO] GWoppouz1l=running
[[14:49:25]] [INFO] Executing action 622/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[14:49:25]] [SUCCESS] Screenshot refreshed successfully
[[14:49:25]] [SUCCESS] Screenshot refreshed
[[14:49:25]] [INFO] Refreshing screenshot...
[[14:49:25]] [INFO] B6GDXWAmWp=pass
[[14:49:20]] [INFO] B6GDXWAmWp=running
[[14:49:20]] [INFO] Executing action 621/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[14:49:20]] [SUCCESS] Screenshot refreshed successfully
[[14:49:20]] [SUCCESS] Screenshot refreshed
[[14:49:20]] [INFO] Refreshing screenshot...
[[14:49:20]] [INFO] mtYqeDttRc=pass
[[14:49:16]] [INFO] mtYqeDttRc=running
[[14:49:16]] [INFO] Executing action 620/643: Tap on image: env[paypal-close-img]
[[14:49:15]] [SUCCESS] Screenshot refreshed successfully
[[14:49:15]] [SUCCESS] Screenshot refreshed
[[14:49:15]] [INFO] Refreshing screenshot...
[[14:49:15]] [INFO] q6cKxgMAIn=pass
[[14:49:08]] [INFO] q6cKxgMAIn=running
[[14:49:08]] [INFO] Executing action 619/643: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[14:49:08]] [SUCCESS] Screenshot refreshed successfully
[[14:49:08]] [SUCCESS] Screenshot refreshed
[[14:49:08]] [INFO] Refreshing screenshot...
[[14:49:08]] [INFO] KRQDBv2D3A=pass
[[14:49:04]] [INFO] KRQDBv2D3A=running
[[14:49:04]] [INFO] Executing action 618/643: Tap on image: env[device-back-img]
[[14:49:03]] [SUCCESS] Screenshot refreshed successfully
[[14:49:03]] [SUCCESS] Screenshot refreshed
[[14:49:03]] [INFO] Refreshing screenshot...
[[14:49:03]] [INFO] P4b2BITpCf=pass
[[14:48:59]] [INFO] P4b2BITpCf=running
[[14:48:59]] [INFO] Executing action 617/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[14:48:58]] [SUCCESS] Screenshot refreshed successfully
[[14:48:58]] [SUCCESS] Screenshot refreshed
[[14:48:58]] [INFO] Refreshing screenshot...
[[14:48:58]] [INFO] inrxgdWzXr=pass
[[14:48:51]] [INFO] inrxgdWzXr=running
[[14:48:51]] [INFO] Executing action 616/643: Tap on element with accessibility_id: Learn more about Zip
[[14:48:51]] [SUCCESS] Screenshot refreshed successfully
[[14:48:51]] [SUCCESS] Screenshot refreshed
[[14:48:51]] [INFO] Refreshing screenshot...
[[14:48:51]] [INFO] Et3kvnFdxh=pass
[[14:48:47]] [INFO] Et3kvnFdxh=running
[[14:48:47]] [INFO] Executing action 615/643: Tap on image: env[device-back-img]
[[14:48:47]] [INFO] Skipping disabled action 614/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[14:48:47]] [SUCCESS] Screenshot refreshed successfully
[[14:48:47]] [SUCCESS] Screenshot refreshed
[[14:48:47]] [INFO] Refreshing screenshot...
[[14:48:47]] [INFO] pk2DLZFBmx=pass
[[14:48:40]] [INFO] pk2DLZFBmx=running
[[14:48:40]] [INFO] Executing action 613/643: Tap on element with accessibility_id: Learn more about AfterPay
[[14:48:40]] [SUCCESS] Screenshot refreshed successfully
[[14:48:40]] [SUCCESS] Screenshot refreshed
[[14:48:40]] [INFO] Refreshing screenshot...
[[14:48:40]] [INFO] ShJSdXvmVL=pass
[[14:48:32]] [INFO] ShJSdXvmVL=running
[[14:48:32]] [INFO] Executing action 612/643: Swipe up till element accessibility_id: "Learn more about AfterPay" is visible
[[14:48:31]] [SUCCESS] Screenshot refreshed successfully
[[14:48:31]] [SUCCESS] Screenshot refreshed
[[14:48:31]] [INFO] Refreshing screenshot...
[[14:48:31]] [INFO] eShagNJmzI=pass
[[14:48:26]] [INFO] eShagNJmzI=running
[[14:48:26]] [INFO] Executing action 611/643: Wait for 3 ms
[[14:48:26]] [SUCCESS] Screenshot refreshed successfully
[[14:48:26]] [SUCCESS] Screenshot refreshed
[[14:48:26]] [INFO] Refreshing screenshot...
[[14:48:26]] [INFO] sHQtYzpI4s=pass
[[14:48:22]] [INFO] sHQtYzpI4s=running
[[14:48:22]] [INFO] Executing action 610/643: Tap on image: env[closebtnimage]
[[14:48:21]] [SUCCESS] Screenshot refreshed successfully
[[14:48:21]] [SUCCESS] Screenshot refreshed
[[14:48:21]] [INFO] Refreshing screenshot...
[[14:48:21]] [INFO] 83tV9A4NOn=pass
[[14:48:18]] [INFO] 83tV9A4NOn=running
[[14:48:18]] [INFO] Executing action 609/643: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[14:48:17]] [SUCCESS] Screenshot refreshed successfully
[[14:48:17]] [SUCCESS] Screenshot refreshed
[[14:48:17]] [INFO] Refreshing screenshot...
[[14:48:17]] [INFO] dCqKBG3e7u=pass
[[14:48:12]] [INFO] dCqKBG3e7u=running
[[14:48:12]] [INFO] Executing action 608/643: Tap on image: env[product-share-img]
[[14:48:12]] [SUCCESS] Screenshot refreshed successfully
[[14:48:12]] [SUCCESS] Screenshot refreshed
[[14:48:12]] [INFO] Refreshing screenshot...
[[14:48:12]] [INFO] kAQ1yIIw3h=pass
[[14:48:08]] [INFO] kAQ1yIIw3h=running
[[14:48:08]] [INFO] Executing action 607/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)
[[14:48:07]] [SUCCESS] Screenshot refreshed successfully
[[14:48:07]] [SUCCESS] Screenshot refreshed
[[14:48:07]] [INFO] Refreshing screenshot...
[[14:48:07]] [INFO] OmKfD9iBjD=pass
[[14:48:04]] [INFO] OmKfD9iBjD=running
[[14:48:04]] [INFO] Executing action 606/643: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:48:03]] [SUCCESS] Screenshot refreshed successfully
[[14:48:03]] [SUCCESS] Screenshot refreshed
[[14:48:03]] [INFO] Refreshing screenshot...
[[14:48:03]] [INFO] dMl1PH9Dlc=pass
[[14:47:52]] [INFO] dMl1PH9Dlc=running
[[14:47:52]] [INFO] Executing action 605/643: Wait for 10 ms
[[14:47:51]] [SUCCESS] Screenshot refreshed successfully
[[14:47:51]] [SUCCESS] Screenshot refreshed
[[14:47:51]] [INFO] Refreshing screenshot...
[[14:47:51]] [INFO] eHLWiRoqqS=pass
[[14:47:46]] [INFO] eHLWiRoqqS=running
[[14:47:46]] [INFO] Executing action 604/643: Swipe from (50%, 70%) to (50%, 30%)
[[14:47:46]] [SUCCESS] Screenshot refreshed successfully
[[14:47:46]] [SUCCESS] Screenshot refreshed
[[14:47:46]] [INFO] Refreshing screenshot...
[[14:47:46]] [INFO] huUnpMMjVR=pass
[[14:47:42]] [INFO] huUnpMMjVR=running
[[14:47:42]] [INFO] Executing action 603/643: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[14:47:42]] [SUCCESS] Screenshot refreshed successfully
[[14:47:41]] [SUCCESS] Screenshot refreshed
[[14:47:41]] [INFO] Refreshing screenshot...
[[14:47:41]] [INFO] XmAxcBtFI0=pass
[[14:47:38]] [INFO] XmAxcBtFI0=running
[[14:47:38]] [INFO] Executing action 602/643: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[14:47:38]] [SUCCESS] Screenshot refreshed successfully
[[14:47:38]] [SUCCESS] Screenshot refreshed
[[14:47:38]] [INFO] Refreshing screenshot...
[[14:47:38]] [INFO] ktAufkDJnF=pass
[[14:47:34]] [INFO] ktAufkDJnF=running
[[14:47:34]] [INFO] Executing action 601/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show (")]
[[14:47:34]] [SUCCESS] Screenshot refreshed successfully
[[14:47:33]] [SUCCESS] Screenshot refreshed
[[14:47:33]] [INFO] Refreshing screenshot...
[[14:47:33]] [INFO] dMl1PH9Dlc=pass
[[14:47:27]] [INFO] dMl1PH9Dlc=running
[[14:47:27]] [INFO] Executing action 600/643: Wait for 5 ms
[[14:47:27]] [SUCCESS] Screenshot refreshed successfully
[[14:47:27]] [SUCCESS] Screenshot refreshed
[[14:47:27]] [INFO] Refreshing screenshot...
[[14:47:27]] [INFO] a50JhCx0ir=pass
[[14:47:23]] [INFO] a50JhCx0ir=running
[[14:47:23]] [INFO] Executing action 599/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[14:47:23]] [SUCCESS] Screenshot refreshed successfully
[[14:47:22]] [SUCCESS] Screenshot refreshed
[[14:47:22]] [INFO] Refreshing screenshot...
[[14:47:22]] [INFO] Y1O1clhMSJ=pass
[[14:47:18]] [INFO] Y1O1clhMSJ=running
[[14:47:18]] [INFO] Executing action 598/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[14:47:18]] [SUCCESS] Screenshot refreshed successfully
[[14:47:18]] [SUCCESS] Screenshot refreshed
[[14:47:18]] [INFO] Refreshing screenshot...
[[14:47:18]] [INFO] lYPskZt0Ya=pass
[[14:47:14]] [INFO] lYPskZt0Ya=running
[[14:47:14]] [INFO] Executing action 597/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:47:14]] [SUCCESS] Screenshot refreshed successfully
[[14:47:14]] [SUCCESS] Screenshot refreshed
[[14:47:14]] [INFO] Refreshing screenshot...
[[14:47:14]] [INFO] xUbWFa8Ok2=pass
[[14:47:10]] [INFO] xUbWFa8Ok2=running
[[14:47:10]] [INFO] Executing action 596/643: Tap on Text: "Latest"
[[14:47:09]] [SUCCESS] Screenshot refreshed successfully
[[14:47:09]] [SUCCESS] Screenshot refreshed
[[14:47:09]] [INFO] Refreshing screenshot...
[[14:47:09]] [INFO] RbNtEW6N9T=pass
[[14:47:05]] [INFO] RbNtEW6N9T=running
[[14:47:05]] [INFO] Executing action 595/643: Tap on Text: "Toys"
[[14:47:05]] [SUCCESS] Screenshot refreshed successfully
[[14:47:05]] [SUCCESS] Screenshot refreshed
[[14:47:05]] [INFO] Refreshing screenshot...
[[14:47:05]] [INFO] ltDXyWvtEz=pass
[[14:47:01]] [INFO] ltDXyWvtEz=running
[[14:47:01]] [INFO] Executing action 594/643: Tap on image: env[device-back-img]
[[14:47:01]] [SUCCESS] Screenshot refreshed successfully
[[14:47:01]] [SUCCESS] Screenshot refreshed
[[14:47:01]] [INFO] Refreshing screenshot...
[[14:47:01]] [INFO] QPKR6jUF9O=pass
[[14:46:58]] [INFO] QPKR6jUF9O=running
[[14:46:58]] [INFO] Executing action 593/643: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[14:46:57]] [SUCCESS] Screenshot refreshed successfully
[[14:46:57]] [SUCCESS] Screenshot refreshed
[[14:46:57]] [INFO] Refreshing screenshot...
[[14:46:57]] [INFO] vfwUVEyq6X=pass
[[14:46:54]] [INFO] vfwUVEyq6X=running
[[14:46:54]] [INFO] Executing action 592/643: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[14:46:54]] [SUCCESS] Screenshot refreshed successfully
[[14:46:54]] [SUCCESS] Screenshot refreshed
[[14:46:54]] [INFO] Refreshing screenshot...
[[14:46:54]] [INFO] Xr6F8gdd8q=pass
[[14:46:50]] [INFO] Xr6F8gdd8q=running
[[14:46:50]] [INFO] Executing action 591/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:46:50]] [SUCCESS] Screenshot refreshed successfully
[[14:46:50]] [SUCCESS] Screenshot refreshed
[[14:46:50]] [INFO] Refreshing screenshot...
[[14:46:50]] [INFO] Xr6F8gdd8q=pass
[[14:46:47]] [INFO] Xr6F8gdd8q=running
[[14:46:47]] [INFO] Executing action 590/643: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:46:46]] [SUCCESS] Screenshot refreshed successfully
[[14:46:46]] [SUCCESS] Screenshot refreshed
[[14:46:46]] [INFO] Refreshing screenshot...
[[14:46:46]] [INFO] F4NGh9HrLw=pass
[[14:46:41]] [INFO] F4NGh9HrLw=running
[[14:46:41]] [INFO] Executing action 589/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:46:41]] [SUCCESS] Screenshot refreshed successfully
[[14:46:41]] [SUCCESS] Screenshot refreshed
[[14:46:41]] [INFO] Refreshing screenshot...
[[14:46:41]] [INFO] H9fy9qcFbZ=pass
[[14:46:37]] [INFO] H9fy9qcFbZ=running
[[14:46:37]] [INFO] Executing action 588/643: Restart app: env[appid]
[[14:46:35]] [INFO] === RETRYING TEST CASE: Browse__PDP_20250510095542.json (Attempt 3 of 3) ===
[[14:46:35]] [INFO] 2p13JoJbbA=fail
[[14:46:35]] [ERROR] Action 641 failed: Element not found or not tappable: xpath='//XCUIElementTypeButton[contains(@name,"Remove")]'
[[14:46:22]] [INFO] 2p13JoJbbA=running
[[14:46:22]] [INFO] Executing action 641/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:46:22]] [SUCCESS] Screenshot refreshed successfully
[[14:46:22]] [SUCCESS] Screenshot refreshed
[[14:46:22]] [INFO] Refreshing screenshot...
[[14:46:22]] [INFO] 2p13JoJbbA=pass
[[14:46:17]] [INFO] 2p13JoJbbA=running
[[14:46:17]] [INFO] Executing action 640/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:46:17]] [SUCCESS] Screenshot refreshed successfully
[[14:46:17]] [SUCCESS] Screenshot refreshed
[[14:46:17]] [INFO] Refreshing screenshot...
[[14:46:17]] [INFO] nyBidG0kHp=pass
[[14:46:04]] [INFO] nyBidG0kHp=running
[[14:46:04]] [INFO] Executing action 639/643: Swipe from (50%, 50%) to (50%, 20%)
[[14:46:04]] [SUCCESS] Screenshot refreshed successfully
[[14:46:03]] [SUCCESS] Screenshot refreshed
[[14:46:03]] [INFO] Refreshing screenshot...
[[14:46:03]] [INFO] w7I4F66YKQ=pass
[[14:45:59]] [INFO] w7I4F66YKQ=running
[[14:45:59]] [INFO] Executing action 638/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:45:58]] [SUCCESS] Screenshot refreshed successfully
[[14:45:58]] [SUCCESS] Screenshot refreshed
[[14:45:58]] [INFO] Refreshing screenshot...
[[14:45:58]] [INFO] F4NGh9HrLw=pass
[[14:45:53]] [INFO] F4NGh9HrLw=running
[[14:45:53]] [INFO] Executing action 637/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:45:53]] [SUCCESS] Screenshot refreshed successfully
[[14:45:53]] [SUCCESS] Screenshot refreshed
[[14:45:53]] [INFO] Refreshing screenshot...
[[14:45:53]] [INFO] VtMfqK1V9t=pass
[[14:45:33]] [INFO] VtMfqK1V9t=running
[[14:45:33]] [INFO] Executing action 636/643: Tap on element with accessibility_id: Add to bag
[[14:45:33]] [SUCCESS] Screenshot refreshed successfully
[[14:45:33]] [SUCCESS] Screenshot refreshed
[[14:45:33]] [INFO] Refreshing screenshot...
[[14:45:33]] [INFO] NOnuFzXy63=pass
[[14:45:28]] [INFO] NOnuFzXy63=running
[[14:45:28]] [INFO] Executing action 635/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:45:28]] [SUCCESS] Screenshot refreshed successfully
[[14:45:28]] [SUCCESS] Screenshot refreshed
[[14:45:28]] [INFO] Refreshing screenshot...
[[14:45:28]] [INFO] kz9lnCdwoH=pass
[[14:45:23]] [INFO] kz9lnCdwoH=running
[[14:45:23]] [INFO] Executing action 634/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[14:45:23]] [SUCCESS] Screenshot refreshed successfully
[[14:45:23]] [SUCCESS] Screenshot refreshed
[[14:45:23]] [INFO] Refreshing screenshot...
[[14:45:23]] [INFO] kz9lnCdwoH=pass
[[14:45:17]] [INFO] kz9lnCdwoH=running
[[14:45:17]] [INFO] Executing action 633/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:45:17]] [SUCCESS] Screenshot refreshed successfully
[[14:45:17]] [SUCCESS] Screenshot refreshed
[[14:45:17]] [INFO] Refreshing screenshot...
[[14:45:17]] [INFO] qIF9CVPc56=pass
[[14:45:13]] [INFO] qIF9CVPc56=running
[[14:45:13]] [INFO] Executing action 632/643: iOS Function: text - Text: "mat"
[[14:45:13]] [SUCCESS] Screenshot refreshed successfully
[[14:45:12]] [SUCCESS] Screenshot refreshed
[[14:45:12]] [INFO] Refreshing screenshot...
[[14:45:12]] [INFO] yEga5MkcRe=pass
[[14:45:09]] [INFO] yEga5MkcRe=running
[[14:45:09]] [INFO] Executing action 631/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:45:08]] [SUCCESS] Screenshot refreshed successfully
[[14:45:08]] [SUCCESS] Screenshot refreshed
[[14:45:08]] [INFO] Refreshing screenshot...
[[14:45:08]] [INFO] F4NGh9HrLw=pass
[[14:45:04]] [INFO] F4NGh9HrLw=running
[[14:45:04]] [INFO] Executing action 630/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:45:03]] [SUCCESS] Screenshot refreshed successfully
[[14:45:03]] [SUCCESS] Screenshot refreshed
[[14:45:03]] [INFO] Refreshing screenshot...
[[14:45:03]] [INFO] kz9lnCdwoH=pass
[[14:44:59]] [INFO] kz9lnCdwoH=running
[[14:44:59]] [INFO] Executing action 629/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[14:44:58]] [SUCCESS] Screenshot refreshed successfully
[[14:44:58]] [SUCCESS] Screenshot refreshed
[[14:44:58]] [INFO] Refreshing screenshot...
[[14:44:58]] [INFO] kz9lnCdwoH=pass
[[14:44:52]] [INFO] kz9lnCdwoH=running
[[14:44:52]] [INFO] Executing action 628/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:44:52]] [SUCCESS] Screenshot refreshed successfully
[[14:44:52]] [SUCCESS] Screenshot refreshed
[[14:44:52]] [INFO] Refreshing screenshot...
[[14:44:52]] [INFO] JRheDTvpJf=pass
[[14:44:48]] [INFO] JRheDTvpJf=running
[[14:44:48]] [INFO] Executing action 627/643: iOS Function: text - Text: "Kid toy"
[[14:44:47]] [SUCCESS] Screenshot refreshed successfully
[[14:44:47]] [SUCCESS] Screenshot refreshed
[[14:44:47]] [INFO] Refreshing screenshot...
[[14:44:47]] [INFO] yEga5MkcRe=pass
[[14:44:44]] [INFO] yEga5MkcRe=running
[[14:44:44]] [INFO] Executing action 626/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:44:43]] [SUCCESS] Screenshot refreshed successfully
[[14:44:43]] [SUCCESS] Screenshot refreshed
[[14:44:43]] [INFO] Refreshing screenshot...
[[14:44:43]] [INFO] F4NGh9HrLw=pass
[[14:44:38]] [INFO] F4NGh9HrLw=running
[[14:44:38]] [INFO] Executing action 625/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:44:38]] [SUCCESS] Screenshot refreshed successfully
[[14:44:37]] [SUCCESS] Screenshot refreshed
[[14:44:37]] [INFO] Refreshing screenshot...
[[14:44:37]] [INFO] XPEr3w6Zof=pass
[[14:44:34]] [INFO] XPEr3w6Zof=running
[[14:44:34]] [INFO] Executing action 624/643: Restart app: env[appid]
[[14:44:33]] [SUCCESS] Screenshot refreshed successfully
[[14:44:33]] [SUCCESS] Screenshot refreshed
[[14:44:33]] [INFO] Refreshing screenshot...
[[14:44:33]] [INFO] PiQRBWBe3E=pass
[[14:44:29]] [INFO] PiQRBWBe3E=running
[[14:44:29]] [INFO] Executing action 623/643: Tap on image: env[device-back-img]
[[14:44:29]] [SUCCESS] Screenshot refreshed successfully
[[14:44:29]] [SUCCESS] Screenshot refreshed
[[14:44:29]] [INFO] Refreshing screenshot...
[[14:44:29]] [INFO] GWoppouz1l=pass
[[14:44:26]] [INFO] GWoppouz1l=running
[[14:44:26]] [INFO] Executing action 622/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[14:44:26]] [SUCCESS] Screenshot refreshed successfully
[[14:44:25]] [SUCCESS] Screenshot refreshed
[[14:44:25]] [INFO] Refreshing screenshot...
[[14:44:25]] [INFO] B6GDXWAmWp=pass
[[14:44:21]] [INFO] B6GDXWAmWp=running
[[14:44:21]] [INFO] Executing action 621/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[14:44:21]] [SUCCESS] Screenshot refreshed successfully
[[14:44:21]] [SUCCESS] Screenshot refreshed
[[14:44:21]] [INFO] Refreshing screenshot...
[[14:44:21]] [INFO] mtYqeDttRc=pass
[[14:44:17]] [INFO] mtYqeDttRc=running
[[14:44:17]] [INFO] Executing action 620/643: Tap on image: env[paypal-close-img]
[[14:44:16]] [SUCCESS] Screenshot refreshed successfully
[[14:44:16]] [SUCCESS] Screenshot refreshed
[[14:44:16]] [INFO] Refreshing screenshot...
[[14:44:16]] [INFO] q6cKxgMAIn=pass
[[14:44:09]] [INFO] q6cKxgMAIn=running
[[14:44:09]] [INFO] Executing action 619/643: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[14:44:09]] [SUCCESS] Screenshot refreshed successfully
[[14:44:09]] [SUCCESS] Screenshot refreshed
[[14:44:09]] [INFO] Refreshing screenshot...
[[14:44:09]] [INFO] KRQDBv2D3A=pass
[[14:44:05]] [INFO] KRQDBv2D3A=running
[[14:44:05]] [INFO] Executing action 618/643: Tap on image: env[device-back-img]
[[14:44:05]] [SUCCESS] Screenshot refreshed successfully
[[14:44:05]] [SUCCESS] Screenshot refreshed
[[14:44:05]] [INFO] Refreshing screenshot...
[[14:44:05]] [INFO] P4b2BITpCf=pass
[[14:43:59]] [INFO] P4b2BITpCf=running
[[14:43:59]] [INFO] Executing action 617/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[14:43:59]] [SUCCESS] Screenshot refreshed successfully
[[14:43:59]] [SUCCESS] Screenshot refreshed
[[14:43:59]] [INFO] Refreshing screenshot...
[[14:43:59]] [INFO] inrxgdWzXr=pass
[[14:43:52]] [INFO] inrxgdWzXr=running
[[14:43:52]] [INFO] Executing action 616/643: Tap on element with accessibility_id: Learn more about Zip
[[14:43:52]] [SUCCESS] Screenshot refreshed successfully
[[14:43:51]] [SUCCESS] Screenshot refreshed
[[14:43:51]] [INFO] Refreshing screenshot...
[[14:43:51]] [INFO] Et3kvnFdxh=pass
[[14:43:48]] [INFO] Et3kvnFdxh=running
[[14:43:48]] [INFO] Executing action 615/643: Tap on image: env[device-back-img]
[[14:43:48]] [INFO] Skipping disabled action 614/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[14:43:47]] [SUCCESS] Screenshot refreshed successfully
[[14:43:47]] [SUCCESS] Screenshot refreshed
[[14:43:47]] [INFO] Refreshing screenshot...
[[14:43:47]] [INFO] pk2DLZFBmx=pass
[[14:43:41]] [INFO] pk2DLZFBmx=running
[[14:43:41]] [INFO] Executing action 613/643: Tap on element with accessibility_id: Learn more about AfterPay
[[14:43:41]] [SUCCESS] Screenshot refreshed successfully
[[14:43:40]] [SUCCESS] Screenshot refreshed
[[14:43:40]] [INFO] Refreshing screenshot...
[[14:43:40]] [INFO] ShJSdXvmVL=pass
[[14:43:32]] [INFO] ShJSdXvmVL=running
[[14:43:32]] [INFO] Executing action 612/643: Swipe up till element accessibility_id: "Learn more about AfterPay" is visible
[[14:43:32]] [SUCCESS] Screenshot refreshed successfully
[[14:43:31]] [SUCCESS] Screenshot refreshed
[[14:43:31]] [INFO] Refreshing screenshot...
[[14:43:31]] [INFO] eShagNJmzI=pass
[[14:43:27]] [INFO] eShagNJmzI=running
[[14:43:27]] [INFO] Executing action 611/643: Wait for 3 ms
[[14:43:27]] [SUCCESS] Screenshot refreshed successfully
[[14:43:26]] [SUCCESS] Screenshot refreshed
[[14:43:26]] [INFO] Refreshing screenshot...
[[14:43:26]] [INFO] sHQtYzpI4s=pass
[[14:43:22]] [INFO] sHQtYzpI4s=running
[[14:43:22]] [INFO] Executing action 610/643: Tap on image: env[closebtnimage]
[[14:43:22]] [SUCCESS] Screenshot refreshed successfully
[[14:43:21]] [SUCCESS] Screenshot refreshed
[[14:43:21]] [INFO] Refreshing screenshot...
[[14:43:21]] [INFO] 83tV9A4NOn=pass
[[14:43:18]] [INFO] 83tV9A4NOn=running
[[14:43:18]] [INFO] Executing action 609/643: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[14:43:18]] [SUCCESS] Screenshot refreshed successfully
[[14:43:18]] [SUCCESS] Screenshot refreshed
[[14:43:18]] [INFO] Refreshing screenshot...
[[14:43:18]] [INFO] dCqKBG3e7u=pass
[[14:43:13]] [INFO] dCqKBG3e7u=running
[[14:43:13]] [INFO] Executing action 608/643: Tap on image: env[product-share-img]
[[14:43:13]] [SUCCESS] Screenshot refreshed successfully
[[14:43:13]] [SUCCESS] Screenshot refreshed
[[14:43:13]] [INFO] Refreshing screenshot...
[[14:43:13]] [INFO] kAQ1yIIw3h=pass
[[14:43:09]] [INFO] kAQ1yIIw3h=running
[[14:43:09]] [INFO] Executing action 607/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)
[[14:43:08]] [SUCCESS] Screenshot refreshed successfully
[[14:43:08]] [SUCCESS] Screenshot refreshed
[[14:43:08]] [INFO] Refreshing screenshot...
[[14:43:08]] [INFO] OmKfD9iBjD=pass
[[14:43:04]] [INFO] OmKfD9iBjD=running
[[14:43:04]] [INFO] Executing action 606/643: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:43:04]] [SUCCESS] Screenshot refreshed successfully
[[14:43:04]] [SUCCESS] Screenshot refreshed
[[14:43:04]] [INFO] Refreshing screenshot...
[[14:43:04]] [INFO] dMl1PH9Dlc=pass
[[14:42:52]] [INFO] dMl1PH9Dlc=running
[[14:42:52]] [INFO] Executing action 605/643: Wait for 10 ms
[[14:42:52]] [SUCCESS] Screenshot refreshed successfully
[[14:42:52]] [SUCCESS] Screenshot refreshed
[[14:42:52]] [INFO] Refreshing screenshot...
[[14:42:52]] [INFO] eHLWiRoqqS=pass
[[14:42:47]] [INFO] eHLWiRoqqS=running
[[14:42:47]] [INFO] Executing action 604/643: Swipe from (50%, 70%) to (50%, 30%)
[[14:42:47]] [SUCCESS] Screenshot refreshed successfully
[[14:42:47]] [SUCCESS] Screenshot refreshed
[[14:42:47]] [INFO] Refreshing screenshot...
[[14:42:47]] [INFO] huUnpMMjVR=pass
[[14:42:42]] [INFO] huUnpMMjVR=running
[[14:42:42]] [INFO] Executing action 603/643: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[14:42:42]] [SUCCESS] Screenshot refreshed successfully
[[14:42:42]] [SUCCESS] Screenshot refreshed
[[14:42:42]] [INFO] Refreshing screenshot...
[[14:42:42]] [INFO] XmAxcBtFI0=pass
[[14:42:39]] [INFO] XmAxcBtFI0=running
[[14:42:39]] [INFO] Executing action 602/643: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[14:42:39]] [SUCCESS] Screenshot refreshed successfully
[[14:42:38]] [SUCCESS] Screenshot refreshed
[[14:42:38]] [INFO] Refreshing screenshot...
[[14:42:38]] [INFO] ktAufkDJnF=pass
[[14:42:35]] [INFO] ktAufkDJnF=running
[[14:42:35]] [INFO] Executing action 601/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show (")]
[[14:42:34]] [SUCCESS] Screenshot refreshed successfully
[[14:42:34]] [SUCCESS] Screenshot refreshed
[[14:42:34]] [INFO] Refreshing screenshot...
[[14:42:34]] [INFO] dMl1PH9Dlc=pass
[[14:42:28]] [INFO] dMl1PH9Dlc=running
[[14:42:28]] [INFO] Executing action 600/643: Wait for 5 ms
[[14:42:27]] [SUCCESS] Screenshot refreshed successfully
[[14:42:27]] [SUCCESS] Screenshot refreshed
[[14:42:27]] [INFO] Refreshing screenshot...
[[14:42:27]] [INFO] a50JhCx0ir=pass
[[14:42:24]] [INFO] a50JhCx0ir=running
[[14:42:24]] [INFO] Executing action 599/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[14:42:23]] [SUCCESS] Screenshot refreshed successfully
[[14:42:23]] [SUCCESS] Screenshot refreshed
[[14:42:23]] [INFO] Refreshing screenshot...
[[14:42:23]] [INFO] Y1O1clhMSJ=pass
[[14:42:19]] [INFO] Y1O1clhMSJ=running
[[14:42:19]] [INFO] Executing action 598/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[14:42:19]] [SUCCESS] Screenshot refreshed successfully
[[14:42:19]] [SUCCESS] Screenshot refreshed
[[14:42:19]] [INFO] Refreshing screenshot...
[[14:42:19]] [INFO] lYPskZt0Ya=pass
[[14:42:15]] [INFO] lYPskZt0Ya=running
[[14:42:15]] [INFO] Executing action 597/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:42:15]] [SUCCESS] Screenshot refreshed successfully
[[14:42:15]] [SUCCESS] Screenshot refreshed
[[14:42:15]] [INFO] Refreshing screenshot...
[[14:42:15]] [INFO] xUbWFa8Ok2=pass
[[14:42:10]] [INFO] xUbWFa8Ok2=running
[[14:42:10]] [INFO] Executing action 596/643: Tap on Text: "Latest"
[[14:42:10]] [SUCCESS] Screenshot refreshed successfully
[[14:42:10]] [SUCCESS] Screenshot refreshed
[[14:42:10]] [INFO] Refreshing screenshot...
[[14:42:10]] [INFO] RbNtEW6N9T=pass
[[14:42:06]] [INFO] RbNtEW6N9T=running
[[14:42:06]] [INFO] Executing action 595/643: Tap on Text: "Toys"
[[14:42:06]] [SUCCESS] Screenshot refreshed successfully
[[14:42:06]] [SUCCESS] Screenshot refreshed
[[14:42:06]] [INFO] Refreshing screenshot...
[[14:42:06]] [INFO] ltDXyWvtEz=pass
[[14:42:02]] [INFO] ltDXyWvtEz=running
[[14:42:02]] [INFO] Executing action 594/643: Tap on image: env[device-back-img]
[[14:42:01]] [SUCCESS] Screenshot refreshed successfully
[[14:42:01]] [SUCCESS] Screenshot refreshed
[[14:42:01]] [INFO] Refreshing screenshot...
[[14:42:01]] [INFO] QPKR6jUF9O=pass
[[14:41:58]] [INFO] QPKR6jUF9O=running
[[14:41:58]] [INFO] Executing action 593/643: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[14:41:58]] [SUCCESS] Screenshot refreshed successfully
[[14:41:58]] [SUCCESS] Screenshot refreshed
[[14:41:58]] [INFO] Refreshing screenshot...
[[14:41:58]] [INFO] vfwUVEyq6X=pass
[[14:41:55]] [INFO] vfwUVEyq6X=running
[[14:41:55]] [INFO] Executing action 592/643: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[14:41:55]] [SUCCESS] Screenshot refreshed successfully
[[14:41:55]] [SUCCESS] Screenshot refreshed
[[14:41:55]] [INFO] Refreshing screenshot...
[[14:41:55]] [INFO] Xr6F8gdd8q=pass
[[14:41:51]] [INFO] Xr6F8gdd8q=running
[[14:41:51]] [INFO] Executing action 591/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:41:51]] [SUCCESS] Screenshot refreshed successfully
[[14:41:50]] [SUCCESS] Screenshot refreshed
[[14:41:50]] [INFO] Refreshing screenshot...
[[14:41:50]] [INFO] Xr6F8gdd8q=pass
[[14:41:47]] [INFO] Xr6F8gdd8q=running
[[14:41:47]] [INFO] Executing action 590/643: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:41:47]] [SUCCESS] Screenshot refreshed successfully
[[14:41:47]] [SUCCESS] Screenshot refreshed
[[14:41:47]] [INFO] Refreshing screenshot...
[[14:41:47]] [INFO] F4NGh9HrLw=pass
[[14:41:42]] [INFO] F4NGh9HrLw=running
[[14:41:42]] [INFO] Executing action 589/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:41:41]] [SUCCESS] Screenshot refreshed successfully
[[14:41:41]] [SUCCESS] Screenshot refreshed
[[14:41:41]] [INFO] Refreshing screenshot...
[[14:41:41]] [INFO] H9fy9qcFbZ=pass
[[14:41:38]] [INFO] H9fy9qcFbZ=running
[[14:41:38]] [INFO] Executing action 588/643: Restart app: env[appid]
[[14:41:36]] [INFO] === RETRYING TEST CASE: Browse__PDP_20250510095542.json (Attempt 2 of 3) ===
[[14:41:36]] [INFO] 2p13JoJbbA=fail
[[14:41:36]] [ERROR] Action 641 failed: Element not found or not tappable: xpath='//XCUIElementTypeButton[contains(@name,"Remove")]'
[[14:41:21]] [INFO] 2p13JoJbbA=running
[[14:41:21]] [INFO] Executing action 641/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:41:21]] [SUCCESS] Screenshot refreshed successfully
[[14:41:20]] [SUCCESS] Screenshot refreshed
[[14:41:20]] [INFO] Refreshing screenshot...
[[14:41:20]] [INFO] 2p13JoJbbA=pass
[[14:41:16]] [INFO] 2p13JoJbbA=running
[[14:41:16]] [INFO] Executing action 640/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:41:15]] [SUCCESS] Screenshot refreshed successfully
[[14:41:15]] [SUCCESS] Screenshot refreshed
[[14:41:15]] [INFO] Refreshing screenshot...
[[14:41:15]] [INFO] nyBidG0kHp=pass
[[14:41:01]] [INFO] nyBidG0kHp=running
[[14:41:01]] [INFO] Executing action 639/643: Swipe from (50%, 50%) to (50%, 20%)
[[14:41:01]] [SUCCESS] Screenshot refreshed successfully
[[14:41:01]] [SUCCESS] Screenshot refreshed
[[14:41:01]] [INFO] Refreshing screenshot...
[[14:41:01]] [INFO] w7I4F66YKQ=pass
[[14:40:56]] [INFO] w7I4F66YKQ=running
[[14:40:56]] [INFO] Executing action 638/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:40:56]] [SUCCESS] Screenshot refreshed successfully
[[14:40:55]] [SUCCESS] Screenshot refreshed
[[14:40:55]] [INFO] Refreshing screenshot...
[[14:40:55]] [INFO] F4NGh9HrLw=pass
[[14:40:51]] [INFO] F4NGh9HrLw=running
[[14:40:51]] [INFO] Executing action 637/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:40:50]] [SUCCESS] Screenshot refreshed successfully
[[14:40:50]] [SUCCESS] Screenshot refreshed
[[14:40:50]] [INFO] Refreshing screenshot...
[[14:40:50]] [INFO] VtMfqK1V9t=pass
[[14:40:30]] [INFO] VtMfqK1V9t=running
[[14:40:30]] [INFO] Executing action 636/643: Tap on element with accessibility_id: Add to bag
[[14:40:30]] [SUCCESS] Screenshot refreshed successfully
[[14:40:30]] [SUCCESS] Screenshot refreshed
[[14:40:30]] [INFO] Refreshing screenshot...
[[14:40:30]] [INFO] NOnuFzXy63=pass
[[14:40:26]] [INFO] NOnuFzXy63=running
[[14:40:26]] [INFO] Executing action 635/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:40:25]] [SUCCESS] Screenshot refreshed successfully
[[14:40:25]] [SUCCESS] Screenshot refreshed
[[14:40:25]] [INFO] Refreshing screenshot...
[[14:40:25]] [INFO] kz9lnCdwoH=pass
[[14:40:21]] [INFO] kz9lnCdwoH=running
[[14:40:21]] [INFO] Executing action 634/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[14:40:21]] [SUCCESS] Screenshot refreshed successfully
[[14:40:20]] [SUCCESS] Screenshot refreshed
[[14:40:20]] [INFO] Refreshing screenshot...
[[14:40:20]] [INFO] kz9lnCdwoH=pass
[[14:40:17]] [INFO] kz9lnCdwoH=running
[[14:40:17]] [INFO] Executing action 633/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:40:16]] [SUCCESS] Screenshot refreshed successfully
[[14:40:16]] [SUCCESS] Screenshot refreshed
[[14:40:16]] [INFO] Refreshing screenshot...
[[14:40:16]] [INFO] qIF9CVPc56=pass
[[14:40:12]] [INFO] qIF9CVPc56=running
[[14:40:12]] [INFO] Executing action 632/643: iOS Function: text - Text: "mat"
[[14:40:12]] [SUCCESS] Screenshot refreshed successfully
[[14:40:12]] [SUCCESS] Screenshot refreshed
[[14:40:12]] [INFO] Refreshing screenshot...
[[14:40:12]] [INFO] yEga5MkcRe=pass
[[14:40:08]] [INFO] yEga5MkcRe=running
[[14:40:08]] [INFO] Executing action 631/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:40:08]] [SUCCESS] Screenshot refreshed successfully
[[14:40:08]] [SUCCESS] Screenshot refreshed
[[14:40:08]] [INFO] Refreshing screenshot...
[[14:40:08]] [INFO] F4NGh9HrLw=pass
[[14:40:04]] [INFO] F4NGh9HrLw=running
[[14:40:04]] [INFO] Executing action 630/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:40:03]] [SUCCESS] Screenshot refreshed successfully
[[14:40:03]] [SUCCESS] Screenshot refreshed
[[14:40:03]] [INFO] Refreshing screenshot...
[[14:40:03]] [INFO] kz9lnCdwoH=pass
[[14:39:59]] [INFO] kz9lnCdwoH=running
[[14:39:59]] [INFO] Executing action 629/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[14:39:59]] [SUCCESS] Screenshot refreshed successfully
[[14:39:58]] [SUCCESS] Screenshot refreshed
[[14:39:58]] [INFO] Refreshing screenshot...
[[14:39:58]] [INFO] kz9lnCdwoH=pass
[[14:39:55]] [INFO] kz9lnCdwoH=running
[[14:39:55]] [INFO] Executing action 628/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:39:55]] [SUCCESS] Screenshot refreshed successfully
[[14:39:54]] [SUCCESS] Screenshot refreshed
[[14:39:54]] [INFO] Refreshing screenshot...
[[14:39:54]] [INFO] JRheDTvpJf=pass
[[14:39:50]] [INFO] JRheDTvpJf=running
[[14:39:50]] [INFO] Executing action 627/643: iOS Function: text - Text: "Kid toy"
[[14:39:50]] [SUCCESS] Screenshot refreshed successfully
[[14:39:50]] [SUCCESS] Screenshot refreshed
[[14:39:50]] [INFO] Refreshing screenshot...
[[14:39:50]] [INFO] yEga5MkcRe=pass
[[14:39:46]] [INFO] yEga5MkcRe=running
[[14:39:46]] [INFO] Executing action 626/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:39:46]] [SUCCESS] Screenshot refreshed successfully
[[14:39:46]] [SUCCESS] Screenshot refreshed
[[14:39:46]] [INFO] Refreshing screenshot...
[[14:39:46]] [INFO] F4NGh9HrLw=pass
[[14:39:42]] [INFO] F4NGh9HrLw=running
[[14:39:42]] [INFO] Executing action 625/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:39:42]] [SUCCESS] Screenshot refreshed successfully
[[14:39:41]] [SUCCESS] Screenshot refreshed
[[14:39:41]] [INFO] Refreshing screenshot...
[[14:39:41]] [INFO] XPEr3w6Zof=pass
[[14:39:38]] [INFO] XPEr3w6Zof=running
[[14:39:38]] [INFO] Executing action 624/643: Restart app: env[appid]
[[14:39:37]] [SUCCESS] Screenshot refreshed successfully
[[14:39:37]] [SUCCESS] Screenshot refreshed
[[14:39:37]] [INFO] Refreshing screenshot...
[[14:39:37]] [INFO] PiQRBWBe3E=pass
[[14:39:33]] [INFO] PiQRBWBe3E=running
[[14:39:33]] [INFO] Executing action 623/643: Tap on image: env[device-back-img]
[[14:39:33]] [SUCCESS] Screenshot refreshed successfully
[[14:39:33]] [SUCCESS] Screenshot refreshed
[[14:39:33]] [INFO] Refreshing screenshot...
[[14:39:33]] [INFO] GWoppouz1l=pass
[[14:39:30]] [INFO] GWoppouz1l=running
[[14:39:30]] [INFO] Executing action 622/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[14:39:30]] [SUCCESS] Screenshot refreshed successfully
[[14:39:30]] [SUCCESS] Screenshot refreshed
[[14:39:30]] [INFO] Refreshing screenshot...
[[14:39:30]] [INFO] B6GDXWAmWp=pass
[[14:39:26]] [INFO] B6GDXWAmWp=running
[[14:39:26]] [INFO] Executing action 621/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[14:39:25]] [SUCCESS] Screenshot refreshed successfully
[[14:39:25]] [SUCCESS] Screenshot refreshed
[[14:39:25]] [INFO] Refreshing screenshot...
[[14:39:25]] [INFO] mtYqeDttRc=pass
[[14:39:21]] [INFO] mtYqeDttRc=running
[[14:39:21]] [INFO] Executing action 620/643: Tap on image: env[paypal-close-img]
[[14:39:21]] [SUCCESS] Screenshot refreshed successfully
[[14:39:21]] [SUCCESS] Screenshot refreshed
[[14:39:21]] [INFO] Refreshing screenshot...
[[14:39:21]] [INFO] q6cKxgMAIn=pass
[[14:39:14]] [INFO] q6cKxgMAIn=running
[[14:39:14]] [INFO] Executing action 619/643: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[14:39:14]] [SUCCESS] Screenshot refreshed successfully
[[14:39:14]] [SUCCESS] Screenshot refreshed
[[14:39:14]] [INFO] Refreshing screenshot...
[[14:39:14]] [INFO] KRQDBv2D3A=pass
[[14:39:10]] [INFO] KRQDBv2D3A=running
[[14:39:10]] [INFO] Executing action 618/643: Tap on image: env[device-back-img]
[[14:39:10]] [SUCCESS] Screenshot refreshed successfully
[[14:39:10]] [SUCCESS] Screenshot refreshed
[[14:39:10]] [INFO] Refreshing screenshot...
[[14:39:10]] [INFO] P4b2BITpCf=pass
[[14:39:07]] [INFO] P4b2BITpCf=running
[[14:39:07]] [INFO] Executing action 617/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[14:39:07]] [SUCCESS] Screenshot refreshed successfully
[[14:39:06]] [SUCCESS] Screenshot refreshed
[[14:39:06]] [INFO] Refreshing screenshot...
[[14:39:06]] [INFO] inrxgdWzXr=pass
[[14:39:00]] [INFO] inrxgdWzXr=running
[[14:39:00]] [INFO] Executing action 616/643: Tap on element with accessibility_id: Learn more about Zip
[[14:39:00]] [SUCCESS] Screenshot refreshed successfully
[[14:39:00]] [SUCCESS] Screenshot refreshed
[[14:39:00]] [INFO] Refreshing screenshot...
[[14:39:00]] [INFO] Et3kvnFdxh=pass
[[14:38:56]] [INFO] Et3kvnFdxh=running
[[14:38:56]] [INFO] Executing action 615/643: Tap on image: env[device-back-img]
[[14:38:56]] [INFO] Skipping disabled action 614/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[14:38:56]] [SUCCESS] Screenshot refreshed successfully
[[14:38:55]] [SUCCESS] Screenshot refreshed
[[14:38:55]] [INFO] Refreshing screenshot...
[[14:38:55]] [INFO] pk2DLZFBmx=pass
[[14:38:49]] [INFO] pk2DLZFBmx=running
[[14:38:49]] [INFO] Executing action 613/643: Tap on element with accessibility_id: Learn more about AfterPay
[[14:38:49]] [SUCCESS] Screenshot refreshed successfully
[[14:38:49]] [SUCCESS] Screenshot refreshed
[[14:38:49]] [INFO] Refreshing screenshot...
[[14:38:49]] [INFO] ShJSdXvmVL=pass
[[14:38:40]] [INFO] ShJSdXvmVL=running
[[14:38:40]] [INFO] Executing action 612/643: Swipe up till element accessibility_id: "Learn more about AfterPay" is visible
[[14:38:40]] [SUCCESS] Screenshot refreshed successfully
[[14:38:40]] [SUCCESS] Screenshot refreshed
[[14:38:40]] [INFO] Refreshing screenshot...
[[14:38:40]] [INFO] eShagNJmzI=pass
[[14:38:35]] [INFO] eShagNJmzI=running
[[14:38:35]] [INFO] Executing action 611/643: Wait for 3 ms
[[14:38:35]] [SUCCESS] Screenshot refreshed successfully
[[14:38:35]] [SUCCESS] Screenshot refreshed
[[14:38:35]] [INFO] Refreshing screenshot...
[[14:38:35]] [INFO] sHQtYzpI4s=pass
[[14:38:30]] [INFO] sHQtYzpI4s=running
[[14:38:30]] [INFO] Executing action 610/643: Tap on image: env[closebtnimage]
[[14:38:30]] [SUCCESS] Screenshot refreshed successfully
[[14:38:30]] [SUCCESS] Screenshot refreshed
[[14:38:30]] [INFO] Refreshing screenshot...
[[14:38:30]] [INFO] 83tV9A4NOn=pass
[[14:38:26]] [INFO] 83tV9A4NOn=running
[[14:38:26]] [INFO] Executing action 609/643: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[14:38:26]] [SUCCESS] Screenshot refreshed successfully
[[14:38:26]] [SUCCESS] Screenshot refreshed
[[14:38:26]] [INFO] Refreshing screenshot...
[[14:38:26]] [INFO] dCqKBG3e7u=pass
[[14:38:22]] [INFO] dCqKBG3e7u=running
[[14:38:22]] [INFO] Executing action 608/643: Tap on image: env[product-share-img]
[[14:38:21]] [SUCCESS] Screenshot refreshed successfully
[[14:38:21]] [SUCCESS] Screenshot refreshed
[[14:38:21]] [INFO] Refreshing screenshot...
[[14:38:21]] [INFO] kAQ1yIIw3h=pass
[[14:38:17]] [INFO] kAQ1yIIw3h=running
[[14:38:17]] [INFO] Executing action 607/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)
[[14:38:17]] [SUCCESS] Screenshot refreshed successfully
[[14:38:17]] [SUCCESS] Screenshot refreshed
[[14:38:17]] [INFO] Refreshing screenshot...
[[14:38:17]] [INFO] OmKfD9iBjD=pass
[[14:38:13]] [INFO] OmKfD9iBjD=running
[[14:38:13]] [INFO] Executing action 606/643: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:38:13]] [SUCCESS] Screenshot refreshed successfully
[[14:38:12]] [SUCCESS] Screenshot refreshed
[[14:38:12]] [INFO] Refreshing screenshot...
[[14:38:12]] [INFO] dMl1PH9Dlc=pass
[[14:38:01]] [INFO] dMl1PH9Dlc=running
[[14:38:01]] [INFO] Executing action 605/643: Wait for 10 ms
[[14:38:01]] [SUCCESS] Screenshot refreshed successfully
[[14:38:00]] [SUCCESS] Screenshot refreshed
[[14:38:00]] [INFO] Refreshing screenshot...
[[14:38:00]] [INFO] eHLWiRoqqS=pass
[[14:37:56]] [INFO] eHLWiRoqqS=running
[[14:37:56]] [INFO] Executing action 604/643: Swipe from (50%, 70%) to (50%, 30%)
[[14:37:56]] [SUCCESS] Screenshot refreshed successfully
[[14:37:55]] [SUCCESS] Screenshot refreshed
[[14:37:55]] [INFO] Refreshing screenshot...
[[14:37:55]] [INFO] huUnpMMjVR=pass
[[14:37:51]] [INFO] huUnpMMjVR=running
[[14:37:51]] [INFO] Executing action 603/643: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[14:37:51]] [SUCCESS] Screenshot refreshed successfully
[[14:37:51]] [SUCCESS] Screenshot refreshed
[[14:37:51]] [INFO] Refreshing screenshot...
[[14:37:51]] [INFO] XmAxcBtFI0=pass
[[14:37:48]] [INFO] XmAxcBtFI0=running
[[14:37:48]] [INFO] Executing action 602/643: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[14:37:48]] [SUCCESS] Screenshot refreshed successfully
[[14:37:47]] [SUCCESS] Screenshot refreshed
[[14:37:47]] [INFO] Refreshing screenshot...
[[14:37:47]] [INFO] ktAufkDJnF=pass
[[14:37:44]] [INFO] ktAufkDJnF=running
[[14:37:44]] [INFO] Executing action 601/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show (")]
[[14:37:43]] [SUCCESS] Screenshot refreshed successfully
[[14:37:43]] [SUCCESS] Screenshot refreshed
[[14:37:43]] [INFO] Refreshing screenshot...
[[14:37:43]] [INFO] dMl1PH9Dlc=pass
[[14:37:37]] [INFO] dMl1PH9Dlc=running
[[14:37:37]] [INFO] Executing action 600/643: Wait for 5 ms
[[14:37:36]] [SUCCESS] Screenshot refreshed successfully
[[14:37:36]] [SUCCESS] Screenshot refreshed
[[14:37:36]] [INFO] Refreshing screenshot...
[[14:37:36]] [INFO] a50JhCx0ir=pass
[[14:37:33]] [INFO] a50JhCx0ir=running
[[14:37:33]] [INFO] Executing action 599/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[14:37:32]] [SUCCESS] Screenshot refreshed successfully
[[14:37:32]] [SUCCESS] Screenshot refreshed
[[14:37:32]] [INFO] Refreshing screenshot...
[[14:37:32]] [INFO] Y1O1clhMSJ=pass
[[14:37:28]] [INFO] Y1O1clhMSJ=running
[[14:37:28]] [INFO] Executing action 598/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[14:37:28]] [SUCCESS] Screenshot refreshed successfully
[[14:37:28]] [SUCCESS] Screenshot refreshed
[[14:37:28]] [INFO] Refreshing screenshot...
[[14:37:28]] [INFO] lYPskZt0Ya=pass
[[14:37:24]] [INFO] lYPskZt0Ya=running
[[14:37:24]] [INFO] Executing action 597/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:37:24]] [SUCCESS] Screenshot refreshed successfully
[[14:37:24]] [SUCCESS] Screenshot refreshed
[[14:37:24]] [INFO] Refreshing screenshot...
[[14:37:24]] [INFO] xUbWFa8Ok2=pass
[[14:37:19]] [INFO] xUbWFa8Ok2=running
[[14:37:19]] [INFO] Executing action 596/643: Tap on Text: "Latest"
[[14:37:19]] [SUCCESS] Screenshot refreshed successfully
[[14:37:19]] [SUCCESS] Screenshot refreshed
[[14:37:19]] [INFO] Refreshing screenshot...
[[14:37:19]] [INFO] RbNtEW6N9T=pass
[[14:37:15]] [INFO] RbNtEW6N9T=running
[[14:37:15]] [INFO] Executing action 595/643: Tap on Text: "Toys"
[[14:37:15]] [SUCCESS] Screenshot refreshed successfully
[[14:37:15]] [SUCCESS] Screenshot refreshed
[[14:37:15]] [INFO] Refreshing screenshot...
[[14:37:15]] [INFO] ltDXyWvtEz=pass
[[14:37:11]] [INFO] ltDXyWvtEz=running
[[14:37:11]] [INFO] Executing action 594/643: Tap on image: env[device-back-img]
[[14:37:10]] [SUCCESS] Screenshot refreshed successfully
[[14:37:10]] [SUCCESS] Screenshot refreshed
[[14:37:10]] [INFO] Refreshing screenshot...
[[14:37:10]] [INFO] QPKR6jUF9O=pass
[[14:37:07]] [INFO] QPKR6jUF9O=running
[[14:37:07]] [INFO] Executing action 593/643: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[14:37:07]] [SUCCESS] Screenshot refreshed successfully
[[14:37:07]] [SUCCESS] Screenshot refreshed
[[14:37:07]] [INFO] Refreshing screenshot...
[[14:37:07]] [INFO] vfwUVEyq6X=pass
[[14:37:04]] [INFO] vfwUVEyq6X=running
[[14:37:04]] [INFO] Executing action 592/643: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[14:37:04]] [SUCCESS] Screenshot refreshed successfully
[[14:37:04]] [SUCCESS] Screenshot refreshed
[[14:37:04]] [INFO] Refreshing screenshot...
[[14:37:04]] [INFO] Xr6F8gdd8q=pass
[[14:37:00]] [INFO] Xr6F8gdd8q=running
[[14:37:00]] [INFO] Executing action 591/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:37:00]] [SUCCESS] Screenshot refreshed successfully
[[14:37:00]] [SUCCESS] Screenshot refreshed
[[14:37:00]] [INFO] Refreshing screenshot...
[[14:37:00]] [INFO] Xr6F8gdd8q=pass
[[14:36:56]] [INFO] Xr6F8gdd8q=running
[[14:36:56]] [INFO] Executing action 590/643: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:36:56]] [SUCCESS] Screenshot refreshed successfully
[[14:36:56]] [SUCCESS] Screenshot refreshed
[[14:36:56]] [INFO] Refreshing screenshot...
[[14:36:56]] [INFO] F4NGh9HrLw=pass
[[14:36:52]] [INFO] F4NGh9HrLw=running
[[14:36:52]] [INFO] Executing action 589/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:36:52]] [SUCCESS] Screenshot refreshed successfully
[[14:36:51]] [SUCCESS] Screenshot refreshed
[[14:36:51]] [INFO] Refreshing screenshot...
[[14:36:51]] [INFO] H9fy9qcFbZ=pass
[[14:36:39]] [SUCCESS] Screenshot refreshed successfully
[[14:36:39]] [INFO] H9fy9qcFbZ=running
[[14:36:39]] [INFO] Executing action 588/643: Restart app: env[appid]
[[14:36:39]] [SUCCESS] Screenshot refreshed
[[14:36:39]] [INFO] Refreshing screenshot...
[[14:36:39]] [SUCCESS] Screenshot refreshed successfully
[[14:36:38]] [SUCCESS] Screenshot refreshed
[[14:36:38]] [INFO] Refreshing screenshot...
[[14:36:36]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:36:36]] [SUCCESS] Screenshot refreshed successfully
[[14:36:35]] [SUCCESS] Screenshot refreshed
[[14:36:35]] [INFO] Refreshing screenshot...
[[14:36:31]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:36:30]] [SUCCESS] Screenshot refreshed successfully
[[14:36:30]] [SUCCESS] Screenshot refreshed
[[14:36:30]] [INFO] Refreshing screenshot...
[[14:36:26]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:36:26]] [SUCCESS] Screenshot refreshed successfully
[[14:36:26]] [SUCCESS] Screenshot refreshed
[[14:36:26]] [INFO] Refreshing screenshot...
[[14:36:22]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:36:22]] [SUCCESS] Screenshot refreshed successfully
[[14:36:22]] [SUCCESS] Screenshot refreshed
[[14:36:22]] [INFO] Refreshing screenshot...
[[14:36:15]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:36:15]] [SUCCESS] Screenshot refreshed successfully
[[14:36:15]] [SUCCESS] Screenshot refreshed
[[14:36:15]] [INFO] Refreshing screenshot...
[[14:36:09]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:36:09]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:36:09]] [INFO] Loading steps for multiStep action: Kmart_AU_Cleanup
[[14:36:09]] [INFO] 4pFqgUdIwt=running
[[14:36:09]] [INFO] Executing action 587/643: Execute Test Case: Kmart_AU_Cleanup (6 steps)
[[14:36:09]] [SUCCESS] Screenshot refreshed successfully
[[14:36:09]] [SUCCESS] Screenshot refreshed
[[14:36:09]] [INFO] Refreshing screenshot...
[[14:36:09]] [INFO] q6kSH9e0MI=pass
[[14:36:05]] [INFO] q6kSH9e0MI=running
[[14:36:05]] [INFO] Executing action 586/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[14:36:05]] [SUCCESS] Screenshot refreshed successfully
[[14:36:04]] [SUCCESS] Screenshot refreshed
[[14:36:04]] [INFO] Refreshing screenshot...
[[14:36:04]] [INFO] a4pJa7EAyI=pass
[[14:36:01]] [INFO] a4pJa7EAyI=running
[[14:36:01]] [INFO] Executing action 585/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:36:00]] [SUCCESS] Screenshot refreshed successfully
[[14:36:00]] [SUCCESS] Screenshot refreshed
[[14:36:00]] [INFO] Refreshing screenshot...
[[14:36:00]] [INFO] 2bcxKJ2cPg=pass
[[14:35:54]] [INFO] 2bcxKJ2cPg=running
[[14:35:54]] [INFO] Executing action 584/643: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[14:35:53]] [SUCCESS] Screenshot refreshed successfully
[[14:35:53]] [SUCCESS] Screenshot refreshed
[[14:35:53]] [INFO] Refreshing screenshot...
[[14:35:53]] [INFO] aqBkqyVhrZ=pass
[[14:35:48]] [INFO] aqBkqyVhrZ=running
[[14:35:48]] [INFO] Executing action 583/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[14:35:48]] [SUCCESS] Screenshot refreshed successfully
[[14:35:48]] [SUCCESS] Screenshot refreshed
[[14:35:48]] [INFO] Refreshing screenshot...
[[14:35:48]] [INFO] wSHsGWAwPm=pass
[[14:35:43]] [INFO] wSHsGWAwPm=running
[[14:35:43]] [INFO] Executing action 582/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:35:43]] [SUCCESS] Screenshot refreshed successfully
[[14:35:43]] [SUCCESS] Screenshot refreshed
[[14:35:43]] [INFO] Refreshing screenshot...
[[14:35:43]] [INFO] gPYNwJ0HKo=pass
[[14:35:39]] [INFO] gPYNwJ0HKo=running
[[14:35:39]] [INFO] Executing action 581/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:35:39]] [SUCCESS] Screenshot refreshed successfully
[[14:35:39]] [SUCCESS] Screenshot refreshed
[[14:35:39]] [INFO] Refreshing screenshot...
[[14:35:39]] [INFO] vYLhraWpQm=pass
[[14:35:35]] [INFO] vYLhraWpQm=running
[[14:35:35]] [INFO] Executing action 580/643: Tap on image: banner-close-updated.png
[[14:35:35]] [SUCCESS] Screenshot refreshed successfully
[[14:35:34]] [SUCCESS] Screenshot refreshed
[[14:35:34]] [INFO] Refreshing screenshot...
[[14:35:34]] [INFO] TAKgcEDqvz=pass
[[14:35:32]] [INFO] TAKgcEDqvz=running
[[14:35:32]] [INFO] Executing action 579/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
[[14:35:31]] [SUCCESS] Screenshot refreshed successfully
[[14:35:31]] [SUCCESS] Screenshot refreshed
[[14:35:31]] [INFO] Refreshing screenshot...
[[14:35:31]] [INFO] UgjXUTZy7Z=pass
[[14:35:27]] [INFO] UgjXUTZy7Z=running
[[14:35:27]] [INFO] Executing action 578/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[14:35:27]] [SUCCESS] Screenshot refreshed successfully
[[14:35:27]] [SUCCESS] Screenshot refreshed
[[14:35:27]] [INFO] Refreshing screenshot...
[[14:35:27]] [INFO] YqmO7h7VP0=pass
[[14:35:23]] [INFO] YqmO7h7VP0=running
[[14:35:23]] [INFO] Executing action 577/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
[[14:35:23]] [SUCCESS] Screenshot refreshed successfully
[[14:35:23]] [SUCCESS] Screenshot refreshed
[[14:35:23]] [INFO] Refreshing screenshot...
[[14:35:23]] [INFO] vYLhraWpQm=pass
[[14:35:19]] [INFO] vYLhraWpQm=running
[[14:35:19]] [INFO] Executing action 576/643: Tap on image: banner-close-updated.png
[[14:35:19]] [SUCCESS] Screenshot refreshed successfully
[[14:35:18]] [SUCCESS] Screenshot refreshed
[[14:35:18]] [INFO] Refreshing screenshot...
[[14:35:18]] [INFO] lSG7un0qKK=pass
[[14:35:13]] [INFO] lSG7un0qKK=running
[[14:35:13]] [INFO] Executing action 575/643: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[14:35:13]] [SUCCESS] Screenshot refreshed successfully
[[14:35:12]] [SUCCESS] Screenshot refreshed
[[14:35:12]] [INFO] Refreshing screenshot...
[[14:35:12]] [INFO] 9Pwdq32eUk=pass
[[14:35:09]] [INFO] 9Pwdq32eUk=running
[[14:35:09]] [INFO] Executing action 574/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[14:35:08]] [SUCCESS] Screenshot refreshed successfully
[[14:35:08]] [SUCCESS] Screenshot refreshed
[[14:35:08]] [INFO] Refreshing screenshot...
[[14:35:08]] [INFO] YBT2MVclAv=pass
[[14:35:04]] [INFO] YBT2MVclAv=running
[[14:35:04]] [INFO] Executing action 573/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
[[14:35:04]] [SUCCESS] Screenshot refreshed successfully
[[14:35:04]] [SUCCESS] Screenshot refreshed
[[14:35:04]] [INFO] Refreshing screenshot...
[[14:35:04]] [INFO] TzPItWbvDR=pass
[[14:35:00]] [INFO] TzPItWbvDR=running
[[14:35:00]] [INFO] Executing action 572/643: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[14:35:00]] [SUCCESS] Screenshot refreshed successfully
[[14:34:59]] [SUCCESS] Screenshot refreshed
[[14:34:59]] [INFO] Refreshing screenshot...
[[14:34:59]] [INFO] wSdfNe4Kww=pass
[[14:34:56]] [INFO] wSdfNe4Kww=running
[[14:34:56]] [INFO] Executing action 571/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
[[14:34:56]] [SUCCESS] Screenshot refreshed successfully
[[14:34:56]] [SUCCESS] Screenshot refreshed
[[14:34:56]] [INFO] Refreshing screenshot...
[[14:34:56]] [INFO] GN587Y6VBQ=pass
[[14:34:52]] [INFO] GN587Y6VBQ=running
[[14:34:52]] [INFO] Executing action 570/643: Tap on element with xpath: //XCUIElementTypeLink[@name="Pay in 4"]
[[14:34:52]] [SUCCESS] Screenshot refreshed successfully
[[14:34:52]] [SUCCESS] Screenshot refreshed
[[14:34:52]] [INFO] Refreshing screenshot...
[[14:34:52]] [INFO] dkSs61jGvX=pass
[[14:34:48]] [INFO] dkSs61jGvX=running
[[14:34:48]] [INFO] Executing action 569/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[14:34:48]] [SUCCESS] Screenshot refreshed successfully
[[14:34:47]] [SUCCESS] Screenshot refreshed
[[14:34:47]] [INFO] Refreshing screenshot...
[[14:34:47]] [INFO] XLpUP3Wr93=pass
[[14:34:44]] [INFO] XLpUP3Wr93=running
[[14:34:44]] [INFO] Executing action 568/643: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[14:34:43]] [SUCCESS] Screenshot refreshed successfully
[[14:34:43]] [SUCCESS] Screenshot refreshed
[[14:34:43]] [INFO] Refreshing screenshot...
[[14:34:43]] [INFO] mfOWujfRpL=pass
[[14:34:40]] [INFO] mfOWujfRpL=running
[[14:34:40]] [INFO] Executing action 567/643: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
[[14:34:40]] [SUCCESS] Screenshot refreshed successfully
[[14:34:39]] [SUCCESS] Screenshot refreshed
[[14:34:39]] [INFO] Refreshing screenshot...
[[14:34:39]] [INFO] ftA0OJvd0W=pass
[[14:34:36]] [INFO] ftA0OJvd0W=running
[[14:34:36]] [INFO] Executing action 566/643: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[14:34:35]] [SUCCESS] Screenshot refreshed successfully
[[14:34:35]] [SUCCESS] Screenshot refreshed
[[14:34:35]] [INFO] Refreshing screenshot...
[[14:34:35]] [INFO] CBBib3pFkq=pass
[[14:34:27]] [INFO] CBBib3pFkq=running
[[14:34:27]] [INFO] Executing action 565/643: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
[[14:34:27]] [SUCCESS] Screenshot refreshed successfully
[[14:34:27]] [SUCCESS] Screenshot refreshed
[[14:34:27]] [INFO] Refreshing screenshot...
[[14:34:27]] [INFO] 6LQ5cq0f6N=pass
[[14:34:19]] [INFO] 6LQ5cq0f6N=running
[[14:34:19]] [INFO] Executing action 564/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[14:34:19]] [SUCCESS] Screenshot refreshed successfully
[[14:34:18]] [SUCCESS] Screenshot refreshed
[[14:34:18]] [INFO] Refreshing screenshot...
[[14:34:18]] [INFO] 1Lirmyxkft=pass
[[14:34:15]] [INFO] 1Lirmyxkft=running
[[14:34:15]] [INFO] Executing action 563/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[14:34:14]] [SUCCESS] Screenshot refreshed successfully
[[14:34:14]] [SUCCESS] Screenshot refreshed
[[14:34:14]] [INFO] Refreshing screenshot...
[[14:34:14]] [INFO] TTpwkHEyuE=pass
[[14:34:07]] [INFO] TTpwkHEyuE=running
[[14:34:07]] [INFO] Executing action 562/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
[[14:34:06]] [SUCCESS] Screenshot refreshed successfully
[[14:34:06]] [SUCCESS] Screenshot refreshed
[[14:34:06]] [INFO] Refreshing screenshot...
[[14:34:06]] [INFO] mMnRNh3NEd=pass
[[14:34:02]] [INFO] mMnRNh3NEd=running
[[14:34:02]] [INFO] Executing action 561/643: Tap on image: env[delivery-address-img]
[[14:34:02]] [SUCCESS] Screenshot refreshed successfully
[[14:34:02]] [SUCCESS] Screenshot refreshed
[[14:34:02]] [INFO] Refreshing screenshot...
[[14:34:02]] [INFO] NcU6aex76k=pass
[[14:33:58]] [INFO] NcU6aex76k=running
[[14:33:58]] [INFO] Executing action 560/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[14:33:58]] [SUCCESS] Screenshot refreshed successfully
[[14:33:58]] [SUCCESS] Screenshot refreshed
[[14:33:58]] [INFO] Refreshing screenshot...
[[14:33:58]] [INFO] SQ1i1ElZCE=pass
[[14:33:51]] [INFO] SQ1i1ElZCE=running
[[14:33:51]] [INFO] Executing action 559/643: Tap and Type at (54, 304): "305 238 Flinders"
[[14:33:50]] [SUCCESS] Screenshot refreshed successfully
[[14:33:50]] [SUCCESS] Screenshot refreshed
[[14:33:50]] [INFO] Refreshing screenshot...
[[14:33:50]] [INFO] 5ZzW1VVSzy=pass
[[14:33:45]] [INFO] 5ZzW1VVSzy=running
[[14:33:45]] [INFO] Executing action 558/643: Tap on Text: "address"
[[14:33:45]] [SUCCESS] Screenshot refreshed successfully
[[14:33:45]] [SUCCESS] Screenshot refreshed
[[14:33:45]] [INFO] Refreshing screenshot...
[[14:33:45]] [INFO] kDpsm2D3xt=pass
[[14:33:41]] [INFO] kDpsm2D3xt=running
[[14:33:41]] [INFO] Executing action 557/643: iOS Function: text - Text: " "
[[14:33:41]] [SUCCESS] Screenshot refreshed successfully
[[14:33:40]] [SUCCESS] Screenshot refreshed
[[14:33:40]] [INFO] Refreshing screenshot...
[[14:33:40]] [INFO] SFj4Aa7RHQ=pass
[[14:33:34]] [INFO] SFj4Aa7RHQ=running
[[14:33:34]] [INFO] Executing action 556/643: textClear action
[[14:33:33]] [SUCCESS] Screenshot refreshed successfully
[[14:33:33]] [SUCCESS] Screenshot refreshed
[[14:33:33]] [INFO] Refreshing screenshot...
[[14:33:33]] [INFO] yi5EsHEFvc=pass
[[14:33:29]] [INFO] yi5EsHEFvc=running
[[14:33:29]] [INFO] Executing action 555/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[14:33:29]] [SUCCESS] Screenshot refreshed successfully
[[14:33:29]] [SUCCESS] Screenshot refreshed
[[14:33:29]] [INFO] Refreshing screenshot...
[[14:33:29]] [INFO] lWJtKSqlPS=pass
[[14:33:22]] [INFO] lWJtKSqlPS=running
[[14:33:22]] [INFO] Executing action 554/643: textClear action
[[14:33:21]] [SUCCESS] Screenshot refreshed successfully
[[14:33:21]] [SUCCESS] Screenshot refreshed
[[14:33:21]] [INFO] Refreshing screenshot...
[[14:33:21]] [INFO] 9B5MQGTmpP=pass
[[14:33:17]] [INFO] 9B5MQGTmpP=running
[[14:33:17]] [INFO] Executing action 553/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:33:17]] [SUCCESS] Screenshot refreshed successfully
[[14:33:17]] [SUCCESS] Screenshot refreshed
[[14:33:17]] [INFO] Refreshing screenshot...
[[14:33:17]] [INFO] QvuueoTR8W=pass
[[14:33:10]] [INFO] QvuueoTR8W=running
[[14:33:10]] [INFO] Executing action 552/643: textClear action
[[14:33:10]] [SUCCESS] Screenshot refreshed successfully
[[14:33:10]] [SUCCESS] Screenshot refreshed
[[14:33:10]] [INFO] Refreshing screenshot...
[[14:33:10]] [INFO] p8rfQL9ara=pass
[[14:33:06]] [INFO] p8rfQL9ara=running
[[14:33:06]] [INFO] Executing action 551/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[14:33:06]] [SUCCESS] Screenshot refreshed successfully
[[14:33:05]] [SUCCESS] Screenshot refreshed
[[14:33:05]] [INFO] Refreshing screenshot...
[[14:33:05]] [INFO] CLMmkV1OIM=pass
[[14:32:59]] [INFO] CLMmkV1OIM=running
[[14:32:59]] [INFO] Executing action 550/643: textClear action
[[14:32:59]] [SUCCESS] Screenshot refreshed successfully
[[14:32:58]] [SUCCESS] Screenshot refreshed
[[14:32:58]] [INFO] Refreshing screenshot...
[[14:32:58]] [INFO] h9trcMrvxt=pass
[[14:32:55]] [INFO] h9trcMrvxt=running
[[14:32:55]] [INFO] Executing action 549/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[14:32:54]] [SUCCESS] Screenshot refreshed successfully
[[14:32:54]] [SUCCESS] Screenshot refreshed
[[14:32:54]] [INFO] Refreshing screenshot...
[[14:32:54]] [INFO] Q5A0cNaJ24=pass
[[14:32:50]] [INFO] Q5A0cNaJ24=running
[[14:32:50]] [INFO] Executing action 548/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[14:32:50]] [SUCCESS] Screenshot refreshed successfully
[[14:32:50]] [SUCCESS] Screenshot refreshed
[[14:32:50]] [INFO] Refreshing screenshot...
[[14:32:50]] [INFO] xAa049Qgls=pass
[[14:32:22]] [INFO] xAa049Qgls=running
[[14:32:22]] [INFO] Executing action 547/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[14:32:22]] [SUCCESS] Screenshot refreshed successfully
[[14:32:21]] [SUCCESS] Screenshot refreshed
[[14:32:21]] [INFO] Refreshing screenshot...
[[14:32:21]] [INFO] hwdyCKFAUJ=pass
[[14:32:18]] [INFO] hwdyCKFAUJ=running
[[14:32:18]] [INFO] Executing action 546/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[14:32:17]] [SUCCESS] Screenshot refreshed successfully
[[14:32:17]] [SUCCESS] Screenshot refreshed
[[14:32:17]] [INFO] Refreshing screenshot...
[[14:32:17]] [INFO] aqBkqyVhrZ=pass
[[14:32:14]] [INFO] aqBkqyVhrZ=running
[[14:32:14]] [INFO] Executing action 545/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[14:32:13]] [SUCCESS] Screenshot refreshed successfully
[[14:32:13]] [SUCCESS] Screenshot refreshed
[[14:32:13]] [INFO] Refreshing screenshot...
[[14:32:13]] [INFO] E3RDcrIH6J=pass
[[14:32:08]] [INFO] E3RDcrIH6J=running
[[14:32:08]] [INFO] Executing action 544/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:32:08]] [SUCCESS] Screenshot refreshed successfully
[[14:32:08]] [SUCCESS] Screenshot refreshed
[[14:32:08]] [INFO] Refreshing screenshot...
[[14:32:08]] [INFO] gPYNwJ0HKo=pass
[[14:32:04]] [INFO] gPYNwJ0HKo=running
[[14:32:04]] [INFO] Executing action 543/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:32:03]] [SUCCESS] Screenshot refreshed successfully
[[14:32:03]] [SUCCESS] Screenshot refreshed
[[14:32:03]] [INFO] Refreshing screenshot...
[[14:32:03]] [INFO] VLrfDHfkI8=pass
[[14:31:56]] [INFO] VLrfDHfkI8=running
[[14:31:56]] [INFO] Executing action 542/643: Tap on element with accessibility_id: Add to bag
[[14:31:56]] [SUCCESS] Screenshot refreshed successfully
[[14:31:56]] [SUCCESS] Screenshot refreshed
[[14:31:56]] [INFO] Refreshing screenshot...
[[14:31:56]] [INFO] PzxTDnwsZ7=pass
[[14:31:51]] [INFO] PzxTDnwsZ7=running
[[14:31:51]] [INFO] Executing action 541/643: Swipe from (50%, 70%) to (50%, 50%)
[[14:31:51]] [SUCCESS] Screenshot refreshed successfully
[[14:31:51]] [SUCCESS] Screenshot refreshed
[[14:31:51]] [INFO] Refreshing screenshot...
[[14:31:51]] [INFO] 6GkdPPZo8e=pass
[[14:31:47]] [INFO] 6GkdPPZo8e=running
[[14:31:47]] [INFO] Executing action 540/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:31:46]] [SUCCESS] Screenshot refreshed successfully
[[14:31:46]] [SUCCESS] Screenshot refreshed
[[14:31:46]] [INFO] Refreshing screenshot...
[[14:31:46]] [INFO] FSM5PqLDko=pass
[[14:31:43]] [INFO] FSM5PqLDko=running
[[14:31:43]] [INFO] Executing action 539/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:31:43]] [SUCCESS] Screenshot refreshed successfully
[[14:31:43]] [SUCCESS] Screenshot refreshed
[[14:31:43]] [INFO] Refreshing screenshot...
[[14:31:43]] [INFO] GPTMDcrFC2=pass
[[14:31:39]] [INFO] GPTMDcrFC2=running
[[14:31:39]] [INFO] Executing action 538/643: iOS Function: text - Text: "P_42691341"
[[14:31:38]] [SUCCESS] Screenshot refreshed successfully
[[14:31:38]] [SUCCESS] Screenshot refreshed
[[14:31:38]] [INFO] Refreshing screenshot...
[[14:31:38]] [INFO] 91WZz4k3NI=pass
[[14:31:33]] [INFO] 91WZz4k3NI=running
[[14:31:33]] [INFO] Executing action 537/643: Tap on Text: "Find"
[[14:31:33]] [SUCCESS] Screenshot refreshed successfully
[[14:31:33]] [SUCCESS] Screenshot refreshed
[[14:31:33]] [INFO] Refreshing screenshot...
[[14:31:33]] [INFO] ACaNCAo69V=pass
[[14:31:21]] [SUCCESS] Screenshot refreshed successfully
[[14:31:20]] [INFO] ACaNCAo69V=running
[[14:31:20]] [INFO] Executing action 536/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[14:31:20]] [SUCCESS] Screenshot refreshed
[[14:31:20]] [INFO] Refreshing screenshot...
[[14:31:20]] [SUCCESS] Screenshot refreshed successfully
[[14:31:20]] [SUCCESS] Screenshot refreshed
[[14:31:20]] [INFO] Refreshing screenshot...
[[14:31:16]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[14:31:15]] [SUCCESS] Screenshot refreshed successfully
[[14:31:15]] [SUCCESS] Screenshot refreshed
[[14:31:15]] [INFO] Refreshing screenshot...
[[14:31:11]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:31:11]] [SUCCESS] Screenshot refreshed successfully
[[14:31:11]] [SUCCESS] Screenshot refreshed
[[14:31:11]] [INFO] Refreshing screenshot...
[[14:31:06]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[14:31:06]] [SUCCESS] Screenshot refreshed successfully
[[14:31:06]] [SUCCESS] Screenshot refreshed
[[14:31:06]] [INFO] Refreshing screenshot...
[[14:31:02]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:31:02]] [SUCCESS] Screenshot refreshed successfully
[[14:31:01]] [SUCCESS] Screenshot refreshed
[[14:31:01]] [INFO] Refreshing screenshot...
[[14:30:56]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:30:56]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[14:30:56]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[14:30:56]] [INFO] JEpLBji8jZ=running
[[14:30:56]] [INFO] Executing action 535/643: Execute Test Case: Kmart-Signin (5 steps)
[[14:30:55]] [SUCCESS] Screenshot refreshed successfully
[[14:30:55]] [SUCCESS] Screenshot refreshed
[[14:30:55]] [INFO] Refreshing screenshot...
[[14:30:55]] [INFO] TrbMRAIV8i=pass
[[14:30:53]] [INFO] TrbMRAIV8i=running
[[14:30:53]] [INFO] Executing action 534/643: iOS Function: alert_accept
[[14:30:52]] [SUCCESS] Screenshot refreshed successfully
[[14:30:52]] [SUCCESS] Screenshot refreshed
[[14:30:52]] [INFO] Refreshing screenshot...
[[14:30:52]] [INFO] MxtVneSHFi=pass
[[14:30:45]] [INFO] MxtVneSHFi=running
[[14:30:45]] [INFO] Executing action 533/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:30:45]] [SUCCESS] Screenshot refreshed successfully
[[14:30:45]] [SUCCESS] Screenshot refreshed
[[14:30:45]] [INFO] Refreshing screenshot...
[[14:30:45]] [INFO] 3uORTsBIAg=pass
[[14:30:42]] [INFO] 3uORTsBIAg=running
[[14:30:42]] [INFO] Executing action 532/643: Restart app: au.com.kmart
[[14:30:42]] [SUCCESS] Screenshot refreshed successfully
[[14:30:42]] [SUCCESS] Screenshot refreshed
[[14:30:42]] [INFO] Refreshing screenshot...
[[14:30:42]] [INFO] K8uGC1LDOS=pass
[[14:30:31]] [SUCCESS] Screenshot refreshed successfully
[[14:30:30]] [INFO] K8uGC1LDOS=running
[[14:30:30]] [INFO] Executing action 531/643: Terminate app: au.com.kmart
[[14:30:30]] [SUCCESS] Screenshot refreshed
[[14:30:30]] [INFO] Refreshing screenshot...
[[14:30:30]] [SUCCESS] Screenshot refreshed successfully
[[14:30:30]] [SUCCESS] Screenshot refreshed
[[14:30:30]] [INFO] Refreshing screenshot...
[[14:30:27]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:30:27]] [SUCCESS] Screenshot refreshed successfully
[[14:30:27]] [SUCCESS] Screenshot refreshed
[[14:30:27]] [INFO] Refreshing screenshot...
[[14:30:15]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:30:14]] [SUCCESS] Screenshot refreshed successfully
[[14:30:14]] [SUCCESS] Screenshot refreshed
[[14:30:14]] [INFO] Refreshing screenshot...
[[14:30:10]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:30:10]] [SUCCESS] Screenshot refreshed successfully
[[14:30:10]] [SUCCESS] Screenshot refreshed
[[14:30:10]] [INFO] Refreshing screenshot...
[[14:30:06]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:30:06]] [SUCCESS] Screenshot refreshed successfully
[[14:30:06]] [SUCCESS] Screenshot refreshed
[[14:30:06]] [INFO] Refreshing screenshot...
[[14:29:59]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:29:59]] [SUCCESS] Screenshot refreshed successfully
[[14:29:59]] [SUCCESS] Screenshot refreshed
[[14:29:59]] [INFO] Refreshing screenshot...
[[14:29:54]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:29:54]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:29:54]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:29:54]] [INFO] Ll4UlkE3L9=running
[[14:29:54]] [INFO] Executing action 530/643: cleanupSteps action
[[14:29:54]] [SUCCESS] Screenshot refreshed successfully
[[14:29:54]] [SUCCESS] Screenshot refreshed
[[14:29:54]] [INFO] Refreshing screenshot...
[[14:29:54]] [INFO] 25UEKPIknm=pass
[[14:29:51]] [INFO] 25UEKPIknm=running
[[14:29:51]] [INFO] Executing action 529/643: Terminate app: env[appid]
[[14:29:51]] [SUCCESS] Screenshot refreshed successfully
[[14:29:51]] [SUCCESS] Screenshot refreshed
[[14:29:51]] [INFO] Refreshing screenshot...
[[14:29:51]] [INFO] UqgDn5CuPY=pass
[[14:29:48]] [INFO] UqgDn5CuPY=running
[[14:29:48]] [INFO] Executing action 528/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[14:29:47]] [SUCCESS] Screenshot refreshed successfully
[[14:29:47]] [SUCCESS] Screenshot refreshed
[[14:29:47]] [INFO] Refreshing screenshot...
[[14:29:47]] [INFO] VfTTTtrliQ=pass
[[14:29:45]] [INFO] VfTTTtrliQ=running
[[14:29:45]] [INFO] Executing action 527/643: iOS Function: alert_accept
[[14:29:44]] [SUCCESS] Screenshot refreshed successfully
[[14:29:44]] [SUCCESS] Screenshot refreshed
[[14:29:44]] [INFO] Refreshing screenshot...
[[14:29:44]] [INFO] ipT2XD9io6=pass
[[14:29:40]] [INFO] ipT2XD9io6=running
[[14:29:40]] [INFO] Executing action 526/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountJoinTodayButton"]
[[14:29:40]] [SUCCESS] Screenshot refreshed successfully
[[14:29:40]] [SUCCESS] Screenshot refreshed
[[14:29:40]] [INFO] Refreshing screenshot...
[[14:29:40]] [INFO] OKCHAK6HCJ=pass
[[14:29:36]] [INFO] OKCHAK6HCJ=running
[[14:29:36]] [INFO] Executing action 525/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:29:36]] [SUCCESS] Screenshot refreshed successfully
[[14:29:36]] [SUCCESS] Screenshot refreshed
[[14:29:36]] [INFO] Refreshing screenshot...
[[14:29:36]] [INFO] VLlqyGmmr8=pass
[[14:29:23]] [INFO] VLlqyGmmr8=running
[[14:29:23]] [INFO] Executing action 524/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Remove item"]"
[[14:29:23]] [SUCCESS] Screenshot refreshed successfully
[[14:29:23]] [SUCCESS] Screenshot refreshed
[[14:29:23]] [INFO] Refreshing screenshot...
[[14:29:23]] [INFO] rWuyGodCon=pass
[[14:29:11]] [INFO] rWuyGodCon=running
[[14:29:11]] [INFO] Executing action 523/643: Tap if locator exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]"
[[14:29:10]] [SUCCESS] Screenshot refreshed successfully
[[14:29:10]] [SUCCESS] Screenshot refreshed
[[14:29:10]] [INFO] Refreshing screenshot...
[[14:29:10]] [INFO] HlpBHrQZnk=pass
[[14:29:07]] [INFO] HlpBHrQZnk=running
[[14:29:07]] [INFO] Executing action 522/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[14:29:06]] [SUCCESS] Screenshot refreshed successfully
[[14:29:06]] [SUCCESS] Screenshot refreshed
[[14:29:06]] [INFO] Refreshing screenshot...
[[14:29:06]] [INFO] AEnFqnkOa1=pass
[[14:29:02]] [INFO] AEnFqnkOa1=running
[[14:29:02]] [INFO] Executing action 521/643: Tap on image: banner-close-updated.png
[[14:29:02]] [SUCCESS] Screenshot refreshed successfully
[[14:29:02]] [SUCCESS] Screenshot refreshed
[[14:29:02]] [INFO] Refreshing screenshot...
[[14:29:02]] [INFO] z1CfcW4xYT=pass
[[14:28:46]] [INFO] z1CfcW4xYT=running
[[14:28:46]] [INFO] Executing action 520/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:28:46]] [SUCCESS] Screenshot refreshed successfully
[[14:28:46]] [SUCCESS] Screenshot refreshed
[[14:28:46]] [INFO] Refreshing screenshot...
[[14:28:46]] [INFO] dJNRgTXoqs=pass
[[14:28:42]] [INFO] dJNRgTXoqs=running
[[14:28:42]] [INFO] Executing action 519/643: Swipe from (50%, 30%) to (50%, 70%)
[[14:28:41]] [SUCCESS] Screenshot refreshed successfully
[[14:28:41]] [SUCCESS] Screenshot refreshed
[[14:28:41]] [INFO] Refreshing screenshot...
[[14:28:41]] [INFO] ceF4VRTJlO=pass
[[14:28:38]] [INFO] ceF4VRTJlO=running
[[14:28:38]] [INFO] Executing action 518/643: Tap on image: banner-close-updated.png
[[14:28:37]] [SUCCESS] Screenshot refreshed successfully
[[14:28:37]] [SUCCESS] Screenshot refreshed
[[14:28:37]] [INFO] Refreshing screenshot...
[[14:28:37]] [INFO] 8hCPyY2zPt=pass
[[14:28:33]] [INFO] 8hCPyY2zPt=running
[[14:28:33]] [INFO] Executing action 517/643: Tap on element with xpath: //XCUIElementTypeButton[@name="About KHub Stores"]
[[14:28:33]] [SUCCESS] Screenshot refreshed successfully
[[14:28:33]] [SUCCESS] Screenshot refreshed
[[14:28:33]] [INFO] Refreshing screenshot...
[[14:28:33]] [INFO] r0FfJ85LFM=pass
[[14:28:29]] [INFO] r0FfJ85LFM=running
[[14:28:29]] [INFO] Executing action 516/643: Tap on image: banner-close-updated.png
[[14:28:29]] [SUCCESS] Screenshot refreshed successfully
[[14:28:29]] [SUCCESS] Screenshot refreshed
[[14:28:29]] [INFO] Refreshing screenshot...
[[14:28:29]] [INFO] 2QEdm5WM18=pass
[[14:28:25]] [INFO] 2QEdm5WM18=running
[[14:28:25]] [INFO] Executing action 515/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Bags for Click & Collect orders"]
[[14:28:24]] [SUCCESS] Screenshot refreshed successfully
[[14:28:24]] [SUCCESS] Screenshot refreshed
[[14:28:24]] [INFO] Refreshing screenshot...
[[14:28:24]] [INFO] NW6M15JbAy=pass
[[14:28:06]] [INFO] NW6M15JbAy=running
[[14:28:06]] [INFO] Executing action 514/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Bags for Click & Collect orders"]" is visible
[[14:28:06]] [SUCCESS] Screenshot refreshed successfully
[[14:28:05]] [SUCCESS] Screenshot refreshed
[[14:28:05]] [INFO] Refreshing screenshot...
[[14:28:05]] [INFO] njiHWyVooT=pass
[[14:28:02]] [INFO] njiHWyVooT=running
[[14:28:02]] [INFO] Executing action 513/643: Tap on image: banner-close-updated.png
[[14:28:01]] [SUCCESS] Screenshot refreshed successfully
[[14:28:01]] [SUCCESS] Screenshot refreshed
[[14:28:01]] [INFO] Refreshing screenshot...
[[14:28:01]] [INFO] 93bAew9Y4Y=pass
[[14:27:57]] [INFO] 93bAew9Y4Y=running
[[14:27:57]] [INFO] Executing action 512/643: Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,"store details")])[1]
[[14:27:57]] [SUCCESS] Screenshot refreshed successfully
[[14:27:56]] [SUCCESS] Screenshot refreshed
[[14:27:56]] [INFO] Refreshing screenshot...
[[14:27:56]] [INFO] rPQ5EkTza1=pass
[[14:27:52]] [SUCCESS] Screenshot refreshed successfully
[[14:27:52]] [INFO] rPQ5EkTza1=running
[[14:27:52]] [INFO] Executing action 511/643: Tap on Text: "Click"
[[14:27:52]] [SUCCESS] Screenshot refreshed
[[14:27:52]] [INFO] Refreshing screenshot...
[[14:27:52]] [SUCCESS] Screenshot refreshed successfully
[[14:27:52]] [SUCCESS] Screenshot refreshed
[[14:27:52]] [INFO] Refreshing screenshot...
[[14:27:46]] [INFO] Executing Multi Step action step 8/8: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[14:27:45]] [SUCCESS] Screenshot refreshed successfully
[[14:27:45]] [SUCCESS] Screenshot refreshed
[[14:27:45]] [INFO] Refreshing screenshot...
[[14:27:40]] [INFO] Executing Multi Step action step 7/8: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:27:40]] [SUCCESS] Screenshot refreshed successfully
[[14:27:40]] [SUCCESS] Screenshot refreshed
[[14:27:40]] [INFO] Refreshing screenshot...
[[14:27:34]] [INFO] Executing Multi Step action step 6/8: Wait for 5 ms
[[14:27:33]] [SUCCESS] Screenshot refreshed successfully
[[14:27:33]] [SUCCESS] Screenshot refreshed
[[14:27:33]] [INFO] Refreshing screenshot...
[[14:27:29]] [INFO] Executing Multi Step action step 5/8: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:27:29]] [SUCCESS] Screenshot refreshed successfully
[[14:27:29]] [SUCCESS] Screenshot refreshed
[[14:27:29]] [INFO] Refreshing screenshot...
[[14:27:25]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[14:27:25]] [SUCCESS] Screenshot refreshed successfully
[[14:27:24]] [SUCCESS] Screenshot refreshed
[[14:27:24]] [INFO] Refreshing screenshot...
[[14:27:21]] [INFO] Executing Multi Step action step 3/8: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:27:21]] [SUCCESS] Screenshot refreshed successfully
[[14:27:20]] [SUCCESS] Screenshot refreshed
[[14:27:20]] [INFO] Refreshing screenshot...
[[14:27:16]] [INFO] Executing Multi Step action step 2/8: iOS Function: text - Text: "Notebook"
[[14:27:16]] [SUCCESS] Screenshot refreshed successfully
[[14:27:16]] [SUCCESS] Screenshot refreshed
[[14:27:16]] [INFO] Refreshing screenshot...
[[14:27:09]] [INFO] Executing Multi Step action step 1/8: Tap on Text: "Find"
[[14:27:09]] [INFO] Loaded 8 steps from test case: Search and Add (Notebooks)
[[14:27:09]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[14:27:09]] [INFO] 0YgZZfWdYY=running
[[14:27:09]] [INFO] Executing action 510/643: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[14:27:09]] [SUCCESS] Screenshot refreshed successfully
[[14:27:08]] [SUCCESS] Screenshot refreshed
[[14:27:08]] [INFO] Refreshing screenshot...
[[14:27:08]] [INFO] arH1CZCPXh=pass
[[14:27:04]] [INFO] arH1CZCPXh=running
[[14:27:04]] [INFO] Executing action 509/643: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[14:27:03]] [SUCCESS] Screenshot refreshed successfully
[[14:27:03]] [SUCCESS] Screenshot refreshed
[[14:27:03]] [INFO] Refreshing screenshot...
[[14:27:03]] [INFO] JLAJhxPdsl=pass
[[14:26:58]] [INFO] JLAJhxPdsl=running
[[14:26:58]] [INFO] Executing action 508/643: Tap on Text: "Cancel"
[[14:26:58]] [SUCCESS] Screenshot refreshed successfully
[[14:26:58]] [SUCCESS] Screenshot refreshed
[[14:26:58]] [INFO] Refreshing screenshot...
[[14:26:58]] [INFO] UqgDn5CuPY=pass
[[14:26:55]] [INFO] UqgDn5CuPY=running
[[14:26:55]] [INFO] Executing action 507/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[14:26:55]] [SUCCESS] Screenshot refreshed successfully
[[14:26:54]] [SUCCESS] Screenshot refreshed
[[14:26:54]] [INFO] Refreshing screenshot...
[[14:26:54]] [INFO] VfTTTtrliQ=pass
[[14:26:52]] [INFO] VfTTTtrliQ=running
[[14:26:52]] [INFO] Executing action 506/643: iOS Function: alert_accept
[[14:26:52]] [SUCCESS] Screenshot refreshed successfully
[[14:26:51]] [SUCCESS] Screenshot refreshed
[[14:26:51]] [INFO] Refreshing screenshot...
[[14:26:51]] [INFO] ipT2XD9io6=pass
[[14:26:47]] [INFO] ipT2XD9io6=running
[[14:26:47]] [INFO] Executing action 505/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtJoinTodayButton"]
[[14:26:47]] [SUCCESS] Screenshot refreshed successfully
[[14:26:47]] [SUCCESS] Screenshot refreshed
[[14:26:47]] [INFO] Refreshing screenshot...
[[14:26:47]] [INFO] OKCHAK6HCJ=pass
[[14:26:43]] [INFO] OKCHAK6HCJ=running
[[14:26:43]] [INFO] Executing action 504/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[14:26:43]] [SUCCESS] Screenshot refreshed successfully
[[14:26:43]] [SUCCESS] Screenshot refreshed
[[14:26:43]] [INFO] Refreshing screenshot...
[[14:26:43]] [INFO] RbD937Xbte=pass
[[14:26:39]] [INFO] RbD937Xbte=running
[[14:26:39]] [INFO] Executing action 503/643: Tap on Text: "out"
[[14:26:38]] [SUCCESS] Screenshot refreshed successfully
[[14:26:38]] [SUCCESS] Screenshot refreshed
[[14:26:38]] [INFO] Refreshing screenshot...
[[14:26:38]] [INFO] ylslyLAYKb=pass
[[14:26:35]] [INFO] ylslyLAYKb=running
[[14:26:35]] [INFO] Executing action 502/643: Swipe from (50%, 70%) to (50%, 30%)
[[14:26:34]] [SUCCESS] Screenshot refreshed successfully
[[14:26:34]] [SUCCESS] Screenshot refreshed
[[14:26:34]] [INFO] Refreshing screenshot...
[[14:26:34]] [INFO] wguGCt7OoB=pass
[[14:26:30]] [INFO] wguGCt7OoB=running
[[14:26:30]] [INFO] Executing action 501/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:26:30]] [SUCCESS] Screenshot refreshed successfully
[[14:26:30]] [SUCCESS] Screenshot refreshed
[[14:26:30]] [INFO] Refreshing screenshot...
[[14:26:30]] [INFO] RDQCFIxjA0=pass
[[14:26:27]] [INFO] RDQCFIxjA0=running
[[14:26:27]] [INFO] Executing action 500/643: Swipe from (90%, 30%) to (30%, 30%)
[[14:26:26]] [SUCCESS] Screenshot refreshed successfully
[[14:26:26]] [SUCCESS] Screenshot refreshed
[[14:26:26]] [INFO] Refreshing screenshot...
[[14:26:26]] [INFO] x4Mid4HQ0Z=pass
[[14:26:23]] [INFO] x4Mid4HQ0Z=running
[[14:26:23]] [INFO] Executing action 499/643: Swipe from (90%, 30%) to (30%, 30%)
[[14:26:22]] [SUCCESS] Screenshot refreshed successfully
[[14:26:22]] [SUCCESS] Screenshot refreshed
[[14:26:22]] [INFO] Refreshing screenshot...
[[14:26:22]] [INFO] wguGCt7OoB=pass
[[14:26:18]] [INFO] wguGCt7OoB=running
[[14:26:18]] [INFO] Executing action 498/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[14:26:17]] [SUCCESS] Screenshot refreshed successfully
[[14:26:17]] [SUCCESS] Screenshot refreshed
[[14:26:17]] [INFO] Refreshing screenshot...
[[14:26:17]] [INFO] 39pu9NW124=pass
[[14:26:01]] [INFO] 39pu9NW124=running
[[14:26:01]] [INFO] Executing action 497/643: Tap if locator exists: xpath="//XCUIElementTypeButton[contains(@name,"Select to add") and contains(@name,"to wishlist")]"
[[14:26:00]] [SUCCESS] Screenshot refreshed successfully
[[14:26:00]] [SUCCESS] Screenshot refreshed
[[14:26:00]] [INFO] Refreshing screenshot...
[[14:26:00]] [INFO] ylslyLAYKb=pass
[[14:25:54]] [INFO] ylslyLAYKb=running
[[14:25:54]] [INFO] Executing action 496/643: Swipe from (50%, 70%) to (50%, 40%)
[[14:25:54]] [SUCCESS] Screenshot refreshed successfully
[[14:25:54]] [SUCCESS] Screenshot refreshed
[[14:25:54]] [INFO] Refreshing screenshot...
[[14:25:54]] [INFO] 0bnBNoqPt8=pass
[[14:25:49]] [INFO] 0bnBNoqPt8=running
[[14:25:49]] [INFO] Executing action 495/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[14:25:49]] [SUCCESS] Screenshot refreshed successfully
[[14:25:49]] [SUCCESS] Screenshot refreshed
[[14:25:49]] [INFO] Refreshing screenshot...
[[14:25:49]] [INFO] xmelRkcdVx=pass
[[14:25:45]] [INFO] xmelRkcdVx=running
[[14:25:45]] [INFO] Executing action 494/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:25:45]] [SUCCESS] Screenshot refreshed successfully
[[14:25:44]] [SUCCESS] Screenshot refreshed
[[14:25:44]] [INFO] Refreshing screenshot...
[[14:25:44]] [INFO] ksCBjJiwHZ=pass
[[14:25:41]] [INFO] ksCBjJiwHZ=running
[[14:25:41]] [INFO] Executing action 493/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:25:41]] [SUCCESS] Screenshot refreshed successfully
[[14:25:40]] [SUCCESS] Screenshot refreshed
[[14:25:40]] [INFO] Refreshing screenshot...
[[14:25:40]] [INFO] d40Oo7famr=pass
[[14:25:36]] [INFO] d40Oo7famr=running
[[14:25:36]] [INFO] Executing action 492/643: iOS Function: text - Text: "env[cooker-id]"
[[14:25:36]] [SUCCESS] Screenshot refreshed successfully
[[14:25:36]] [SUCCESS] Screenshot refreshed
[[14:25:36]] [INFO] Refreshing screenshot...
[[14:25:36]] [INFO] ewuLtuqVuo=pass
[[14:25:31]] [INFO] ewuLtuqVuo=running
[[14:25:31]] [INFO] Executing action 491/643: Tap on Text: "Find"
[[14:25:31]] [SUCCESS] Screenshot refreshed successfully
[[14:25:31]] [SUCCESS] Screenshot refreshed
[[14:25:31]] [INFO] Refreshing screenshot...
[[14:25:31]] [INFO] GTXmST3hEA=pass
[[14:25:25]] [INFO] GTXmST3hEA=running
[[14:25:25]] [INFO] Executing action 490/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[14:25:25]] [SUCCESS] Screenshot refreshed successfully
[[14:25:25]] [SUCCESS] Screenshot refreshed
[[14:25:25]] [INFO] Refreshing screenshot...
[[14:25:25]] [INFO] qkZ5KShdEU=pass
[[14:25:20]] [INFO] qkZ5KShdEU=running
[[14:25:20]] [INFO] Executing action 489/643: iOS Function: text - Text: "env[pwd]"
[[14:25:20]] [SUCCESS] Screenshot refreshed successfully
[[14:25:20]] [SUCCESS] Screenshot refreshed
[[14:25:20]] [INFO] Refreshing screenshot...
[[14:25:20]] [INFO] 7g2LmvjtEZ=pass
[[14:25:16]] [INFO] 7g2LmvjtEZ=running
[[14:25:16]] [INFO] Executing action 488/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:25:15]] [SUCCESS] Screenshot refreshed successfully
[[14:25:15]] [SUCCESS] Screenshot refreshed
[[14:25:15]] [INFO] Refreshing screenshot...
[[14:25:15]] [INFO] OUT2ASweb6=pass
[[14:25:11]] [INFO] OUT2ASweb6=running
[[14:25:11]] [INFO] Executing action 487/643: iOS Function: text - Text: "env[uname]"
[[14:25:10]] [SUCCESS] Screenshot refreshed successfully
[[14:25:10]] [SUCCESS] Screenshot refreshed
[[14:25:10]] [INFO] Refreshing screenshot...
[[14:25:10]] [INFO] TV4kJIIV9v=pass
[[14:25:06]] [INFO] TV4kJIIV9v=running
[[14:25:06]] [INFO] Executing action 486/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:25:06]] [SUCCESS] Screenshot refreshed successfully
[[14:25:06]] [SUCCESS] Screenshot refreshed
[[14:25:06]] [INFO] Refreshing screenshot...
[[14:25:06]] [INFO] kQJbqm7uCi=pass
[[14:25:03]] [INFO] kQJbqm7uCi=running
[[14:25:03]] [INFO] Executing action 485/643: iOS Function: alert_accept
[[14:25:03]] [SUCCESS] Screenshot refreshed successfully
[[14:25:03]] [SUCCESS] Screenshot refreshed
[[14:25:03]] [INFO] Refreshing screenshot...
[[14:25:03]] [INFO] SPE01N6pyp=pass
[[14:24:57]] [INFO] SPE01N6pyp=running
[[14:24:57]] [INFO] Executing action 484/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:24:57]] [SUCCESS] Screenshot refreshed successfully
[[14:24:56]] [SUCCESS] Screenshot refreshed
[[14:24:56]] [INFO] Refreshing screenshot...
[[14:24:56]] [INFO] WEB5St2Mb7=pass
[[14:24:53]] [INFO] WEB5St2Mb7=running
[[14:24:53]] [INFO] Executing action 483/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[14:24:52]] [SUCCESS] Screenshot refreshed successfully
[[14:24:52]] [SUCCESS] Screenshot refreshed
[[14:24:52]] [INFO] Refreshing screenshot...
[[14:24:52]] [INFO] To7bij5MnF=pass
[[14:24:47]] [INFO] To7bij5MnF=running
[[14:24:47]] [INFO] Executing action 482/643: Swipe from (5%, 50%) to (90%, 50%)
[[14:24:47]] [SUCCESS] Screenshot refreshed successfully
[[14:24:47]] [SUCCESS] Screenshot refreshed
[[14:24:47]] [INFO] Refreshing screenshot...
[[14:24:47]] [INFO] NkybTKfs2U=pass
[[14:24:41]] [INFO] NkybTKfs2U=running
[[14:24:41]] [INFO] Executing action 481/643: Swipe from (5%, 50%) to (90%, 50%)
[[14:24:41]] [SUCCESS] Screenshot refreshed successfully
[[14:24:41]] [SUCCESS] Screenshot refreshed
[[14:24:41]] [INFO] Refreshing screenshot...
[[14:24:41]] [INFO] dYEtjrv6lz=pass
[[14:24:37]] [INFO] dYEtjrv6lz=running
[[14:24:37]] [INFO] Executing action 480/643: Tap on Text: "Months"
[[14:24:37]] [SUCCESS] Screenshot refreshed successfully
[[14:24:36]] [SUCCESS] Screenshot refreshed
[[14:24:36]] [INFO] Refreshing screenshot...
[[14:24:36]] [INFO] eGQ7VrKUSo=pass
[[14:24:32]] [INFO] eGQ7VrKUSo=running
[[14:24:32]] [INFO] Executing action 479/643: Tap on Text: "Age"
[[14:24:32]] [SUCCESS] Screenshot refreshed successfully
[[14:24:32]] [SUCCESS] Screenshot refreshed
[[14:24:32]] [INFO] Refreshing screenshot...
[[14:24:32]] [INFO] zNRPvs2cC4=pass
[[14:24:28]] [INFO] zNRPvs2cC4=running
[[14:24:28]] [INFO] Executing action 478/643: Tap on Text: "Toys"
[[14:24:28]] [SUCCESS] Screenshot refreshed successfully
[[14:24:28]] [SUCCESS] Screenshot refreshed
[[14:24:28]] [INFO] Refreshing screenshot...
[[14:24:28]] [INFO] KyyS139agr=pass
[[14:24:24]] [INFO] KyyS139agr=running
[[14:24:24]] [INFO] Executing action 477/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:24:23]] [SUCCESS] Screenshot refreshed successfully
[[14:24:23]] [SUCCESS] Screenshot refreshed
[[14:24:23]] [INFO] Refreshing screenshot...
[[14:24:23]] [INFO] 5e4LeoW1YU=pass
[[14:24:19]] [SUCCESS] Screenshot refreshed successfully
[[14:24:19]] [INFO] 5e4LeoW1YU=running
[[14:24:19]] [INFO] Executing action 476/643: Restart app: env[appid]
[[14:24:19]] [SUCCESS] Screenshot refreshed
[[14:24:19]] [INFO] Refreshing screenshot...
[[14:24:19]] [SUCCESS] Screenshot refreshed successfully
[[14:24:18]] [SUCCESS] Screenshot refreshed
[[14:24:18]] [INFO] Refreshing screenshot...
[[14:24:01]] [INFO] Executing Multi Step action step 8/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[14:24:01]] [SUCCESS] Screenshot refreshed successfully
[[14:24:01]] [SUCCESS] Screenshot refreshed
[[14:24:01]] [INFO] Refreshing screenshot...
[[14:23:17]] [INFO] Executing Multi Step action step 7/8: Swipe from (50%, 80%) to (50%, 10%)
[[14:23:16]] [SUCCESS] Screenshot refreshed successfully
[[14:23:16]] [SUCCESS] Screenshot refreshed
[[14:23:16]] [INFO] Refreshing screenshot...
[[14:22:59]] [INFO] Executing Multi Step action step 6/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[14:22:59]] [SUCCESS] Screenshot refreshed successfully
[[14:22:59]] [SUCCESS] Screenshot refreshed
[[14:22:59]] [INFO] Refreshing screenshot...
[[14:22:15]] [INFO] Executing Multi Step action step 5/8: Swipe from (50%, 80%) to (50%, 10%)
[[14:22:14]] [SUCCESS] Screenshot refreshed successfully
[[14:22:14]] [SUCCESS] Screenshot refreshed
[[14:22:14]] [INFO] Refreshing screenshot...
[[14:21:57]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[14:21:57]] [SUCCESS] Screenshot refreshed successfully
[[14:21:57]] [SUCCESS] Screenshot refreshed
[[14:21:57]] [INFO] Refreshing screenshot...
[[14:21:13]] [INFO] Executing Multi Step action step 3/8: Swipe from (50%, 80%) to (50%, 10%)
[[14:21:12]] [SUCCESS] Screenshot refreshed successfully
[[14:21:12]] [SUCCESS] Screenshot refreshed
[[14:21:12]] [INFO] Refreshing screenshot...
[[14:20:55]] [INFO] Executing Multi Step action step 2/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[14:20:54]] [SUCCESS] Screenshot refreshed successfully
[[14:20:54]] [SUCCESS] Screenshot refreshed
[[14:20:54]] [INFO] Refreshing screenshot...
[[14:20:08]] [INFO] Executing Multi Step action step 1/8: Swipe from (50%, 80%) to (50%, 10%)
[[14:20:08]] [INFO] Loaded 8 steps from test case: Click_Paginations
[[14:20:07]] [INFO] Loading steps for multiStep action: Click_Paginations
[[14:20:07]] [INFO] Z86xBjGUKY=running
[[14:20:07]] [INFO] Executing action 475/643: Execute Test Case: Click_Paginations (8 steps)
[[14:20:07]] [SUCCESS] Screenshot refreshed successfully
[[14:20:07]] [SUCCESS] Screenshot refreshed
[[14:20:07]] [INFO] Refreshing screenshot...
[[14:20:07]] [INFO] IL6kON0uQ9=pass
[[14:20:03]] [INFO] IL6kON0uQ9=running
[[14:20:03]] [INFO] Executing action 474/643: iOS Function: text - Text: "kids toys"
[[14:20:03]] [SUCCESS] Screenshot refreshed successfully
[[14:20:03]] [SUCCESS] Screenshot refreshed
[[14:20:03]] [INFO] Refreshing screenshot...
[[14:20:03]] [INFO] 6G6P3UE7Uy=pass
[[14:19:58]] [INFO] 6G6P3UE7Uy=running
[[14:19:58]] [INFO] Executing action 473/643: Tap on Text: "Find"
[[14:19:57]] [SUCCESS] Screenshot refreshed successfully
[[14:19:57]] [SUCCESS] Screenshot refreshed
[[14:19:57]] [INFO] Refreshing screenshot...
[[14:19:57]] [INFO] 7xs3GiydGF=pass
[[14:19:53]] [INFO] 7xs3GiydGF=running
[[14:19:53]] [INFO] Executing action 472/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[14:19:53]] [SUCCESS] Screenshot refreshed successfully
[[14:19:53]] [SUCCESS] Screenshot refreshed
[[14:19:53]] [INFO] Refreshing screenshot...
[[14:19:53]] [INFO] VqSa9z9R2Q=pass
[[14:19:51]] [INFO] VqSa9z9R2Q=running
[[14:19:51]] [INFO] Executing action 471/643: Launch app: env[appid]
[[14:19:51]] [SUCCESS] Screenshot refreshed successfully
[[14:19:51]] [SUCCESS] Screenshot refreshed
[[14:19:51]] [INFO] Refreshing screenshot...
[[14:19:51]] [INFO] RHEU77LRMw=pass
[[14:19:47]] [INFO] RHEU77LRMw=running
[[14:19:47]] [INFO] Executing action 470/643: Tap on Text: "+61"
[[14:19:47]] [SUCCESS] Screenshot refreshed successfully
[[14:19:46]] [SUCCESS] Screenshot refreshed
[[14:19:46]] [INFO] Refreshing screenshot...
[[14:19:46]] [INFO] MTRbUlaRvI=pass
[[14:19:42]] [INFO] MTRbUlaRvI=running
[[14:19:42]] [INFO] Executing action 469/643: Tap on Text: "1800"
[[14:19:42]] [SUCCESS] Screenshot refreshed successfully
[[14:19:42]] [SUCCESS] Screenshot refreshed
[[14:19:42]] [INFO] Refreshing screenshot...
[[14:19:42]] [INFO] I0tM87Yjhc=pass
[[14:19:38]] [INFO] I0tM87Yjhc=running
[[14:19:38]] [INFO] Executing action 468/643: Tap on Text: "click"
[[14:19:37]] [SUCCESS] Screenshot refreshed successfully
[[14:19:37]] [SUCCESS] Screenshot refreshed
[[14:19:37]] [INFO] Refreshing screenshot...
[[14:19:37]] [INFO] t6L5vWfBYM=pass
[[14:19:17]] [INFO] t6L5vWfBYM=running
[[14:19:17]] [INFO] Executing action 467/643: Swipe from (50%, 70%) to (50%, 30%)
[[14:19:17]] [SUCCESS] Screenshot refreshed successfully
[[14:19:17]] [SUCCESS] Screenshot refreshed
[[14:19:17]] [INFO] Refreshing screenshot...
[[14:19:17]] [INFO] DhFJzlme9K=pass
[[14:19:13]] [INFO] DhFJzlme9K=running
[[14:19:13]] [INFO] Executing action 466/643: Tap on Text: "FAQ"
[[14:19:12]] [SUCCESS] Screenshot refreshed successfully
[[14:19:12]] [SUCCESS] Screenshot refreshed
[[14:19:12]] [INFO] Refreshing screenshot...
[[14:19:12]] [INFO] g17Boaefhg=pass
[[14:19:08]] [INFO] g17Boaefhg=running
[[14:19:08]] [INFO] Executing action 465/643: Tap on Text: "Help"
[[14:19:08]] [SUCCESS] Screenshot refreshed successfully
[[14:19:08]] [SUCCESS] Screenshot refreshed
[[14:19:08]] [INFO] Refreshing screenshot...
[[14:19:08]] [INFO] nPp27xJcCn=pass
[[14:18:55]] [INFO] nPp27xJcCn=running
[[14:18:55]] [INFO] Executing action 464/643: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
[[14:18:55]] [SUCCESS] Screenshot refreshed successfully
[[14:18:55]] [SUCCESS] Screenshot refreshed
[[14:18:55]] [INFO] Refreshing screenshot...
[[14:18:55]] [INFO] SqDiBhmyOG=pass
[[14:18:51]] [INFO] SqDiBhmyOG=running
[[14:18:51]] [INFO] Executing action 463/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:18:51]] [SUCCESS] Screenshot refreshed successfully
[[14:18:50]] [SUCCESS] Screenshot refreshed
[[14:18:50]] [INFO] Refreshing screenshot...
[[14:18:50]] [INFO] OR0SKKnFxy=pass
[[14:18:38]] [SUCCESS] Screenshot refreshed successfully
[[14:18:38]] [INFO] OR0SKKnFxy=running
[[14:18:38]] [INFO] Executing action 462/643: Restart app: env[appid]
[[14:18:38]] [SUCCESS] Screenshot refreshed
[[14:18:38]] [INFO] Refreshing screenshot...
[[14:18:38]] [SUCCESS] Screenshot refreshed successfully
[[14:18:37]] [SUCCESS] Screenshot refreshed
[[14:18:37]] [INFO] Refreshing screenshot...
[[14:18:35]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:18:35]] [SUCCESS] Screenshot refreshed successfully
[[14:18:34]] [SUCCESS] Screenshot refreshed
[[14:18:34]] [INFO] Refreshing screenshot...
[[14:18:22]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:18:22]] [SUCCESS] Screenshot refreshed successfully
[[14:18:21]] [SUCCESS] Screenshot refreshed
[[14:18:21]] [INFO] Refreshing screenshot...
[[14:18:18]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:18:17]] [SUCCESS] Screenshot refreshed successfully
[[14:18:17]] [SUCCESS] Screenshot refreshed
[[14:18:17]] [INFO] Refreshing screenshot...
[[14:18:14]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:18:13]] [SUCCESS] Screenshot refreshed successfully
[[14:18:13]] [SUCCESS] Screenshot refreshed
[[14:18:13]] [INFO] Refreshing screenshot...
[[14:18:07]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:18:07]] [SUCCESS] Screenshot refreshed successfully
[[14:18:06]] [SUCCESS] Screenshot refreshed
[[14:18:06]] [INFO] Refreshing screenshot...
[[14:18:01]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:18:01]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:18:01]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:18:01]] [INFO] kPdSiomhwu=running
[[14:18:01]] [INFO] Executing action 461/643: cleanupSteps action
[[14:18:00]] [SUCCESS] Screenshot refreshed successfully
[[14:18:00]] [SUCCESS] Screenshot refreshed
[[14:18:00]] [INFO] Refreshing screenshot...
[[14:18:00]] [INFO] Qb1AArnpCH=pass
[[14:17:54]] [INFO] Qb1AArnpCH=running
[[14:17:54]] [INFO] Executing action 460/643: Wait for 5 ms
[[14:17:53]] [SUCCESS] Screenshot refreshed successfully
[[14:17:53]] [SUCCESS] Screenshot refreshed
[[14:17:53]] [INFO] Refreshing screenshot...
[[14:17:53]] [INFO] yxlzTytgFT=pass
[[14:17:46]] [INFO] yxlzTytgFT=running
[[14:17:46]] [INFO] Executing action 459/643: Tap if locator exists: xpath="//XCUIElementTypeButton[contains(@name,"Remove")]"
[[14:17:46]] [SUCCESS] Screenshot refreshed successfully
[[14:17:45]] [SUCCESS] Screenshot refreshed
[[14:17:45]] [INFO] Refreshing screenshot...
[[14:17:45]] [INFO] K2w7X1cPdH=pass
[[14:17:36]] [INFO] K2w7X1cPdH=running
[[14:17:36]] [INFO] Executing action 458/643: Swipe from (50%, 50%) to (50%, 30%)
[[14:17:36]] [SUCCESS] Screenshot refreshed successfully
[[14:17:36]] [SUCCESS] Screenshot refreshed
[[14:17:36]] [INFO] Refreshing screenshot...
[[14:17:36]] [INFO] P26OyuqWlb=pass
[[14:17:31]] [INFO] P26OyuqWlb=running
[[14:17:31]] [INFO] Executing action 457/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:17:31]] [SUCCESS] Screenshot refreshed successfully
[[14:17:31]] [SUCCESS] Screenshot refreshed
[[14:17:31]] [INFO] Refreshing screenshot...
[[14:17:31]] [INFO] UpUSVInizv=pass
[[14:17:26]] [INFO] UpUSVInizv=running
[[14:17:26]] [INFO] Executing action 456/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[14:17:26]] [SUCCESS] Screenshot refreshed successfully
[[14:17:25]] [SUCCESS] Screenshot refreshed
[[14:17:25]] [INFO] Refreshing screenshot...
[[14:17:25]] [INFO] c4T3INQkzn=pass
[[14:17:22]] [INFO] c4T3INQkzn=running
[[14:17:22]] [INFO] Executing action 455/643: Restart app: env[appid]
[[14:17:22]] [SUCCESS] Screenshot refreshed successfully
[[14:17:21]] [SUCCESS] Screenshot refreshed
[[14:17:21]] [INFO] Refreshing screenshot...
[[14:17:21]] [INFO] Teyz3d55XS=pass
[[14:17:13]] [INFO] Teyz3d55XS=running
[[14:17:13]] [INFO] Executing action 454/643: Tap if locator exists: accessibility_id="Add to bag"
[[14:17:13]] [SUCCESS] Screenshot refreshed successfully
[[14:17:13]] [SUCCESS] Screenshot refreshed
[[14:17:13]] [INFO] Refreshing screenshot...
[[14:17:13]] [INFO] MA2re5cDWr=pass
[[14:17:05]] [INFO] MA2re5cDWr=running
[[14:17:05]] [INFO] Executing action 453/643: Swipe from (50%, 50%) to (50%, 30%)
[[14:17:04]] [SUCCESS] Screenshot refreshed successfully
[[14:17:04]] [SUCCESS] Screenshot refreshed
[[14:17:04]] [INFO] Refreshing screenshot...
[[14:17:04]] [INFO] 2hGhWulI52=pass
[[14:17:00]] [SUCCESS] Screenshot refreshed successfully
[[14:17:00]] [INFO] 2hGhWulI52=running
[[14:17:00]] [INFO] Executing action 452/643: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[14:17:00]] [SUCCESS] Screenshot refreshed
[[14:17:00]] [INFO] Refreshing screenshot...
[[14:17:00]] [INFO] n57KEWjTea=pass
[[14:16:56]] [INFO] n57KEWjTea=running
[[14:16:56]] [INFO] Executing action 451/643: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[14:16:55]] [SUCCESS] Screenshot refreshed successfully
[[14:16:55]] [SUCCESS] Screenshot refreshed
[[14:16:55]] [INFO] Refreshing screenshot...
[[14:16:55]] [INFO] L59V5hqMX9=pass
[[14:16:51]] [INFO] L59V5hqMX9=running
[[14:16:51]] [INFO] Executing action 450/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[14:16:51]] [SUCCESS] Screenshot refreshed successfully
[[14:16:51]] [SUCCESS] Screenshot refreshed
[[14:16:51]] [INFO] Refreshing screenshot...
[[14:16:51]] [INFO] OKiI82VdnE=pass
[[14:16:44]] [INFO] OKiI82VdnE=running
[[14:16:44]] [INFO] Executing action 449/643: Swipe up till element xpath: "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]" is visible
[[14:16:44]] [SUCCESS] Screenshot refreshed successfully
[[14:16:44]] [SUCCESS] Screenshot refreshed
[[14:16:44]] [INFO] Refreshing screenshot...
[[14:16:44]] [INFO] 3KNqlNy6Bj=pass
[[14:16:40]] [INFO] 3KNqlNy6Bj=running
[[14:16:40]] [INFO] Executing action 448/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[14:16:40]] [SUCCESS] Screenshot refreshed successfully
[[14:16:40]] [SUCCESS] Screenshot refreshed
[[14:16:40]] [INFO] Refreshing screenshot...
[[14:16:40]] [INFO] 3NOS1fbxZs=pass
[[14:16:36]] [INFO] 3NOS1fbxZs=running
[[14:16:36]] [INFO] Executing action 447/643: Tap on image: banner-close-updated.png
[[14:16:36]] [SUCCESS] Screenshot refreshed successfully
[[14:16:35]] [SUCCESS] Screenshot refreshed
[[14:16:35]] [INFO] Refreshing screenshot...
[[14:16:35]] [INFO] K0c1gL9UK1=pass
[[14:16:27]] [INFO] K0c1gL9UK1=running
[[14:16:27]] [INFO] Executing action 446/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:16:27]] [SUCCESS] Screenshot refreshed successfully
[[14:16:26]] [SUCCESS] Screenshot refreshed
[[14:16:26]] [INFO] Refreshing screenshot...
[[14:16:26]] [INFO] IW6uAwdtiW=pass
[[14:16:17]] [INFO] IW6uAwdtiW=running
[[14:16:17]] [INFO] Executing action 445/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[14:16:16]] [SUCCESS] Screenshot refreshed successfully
[[14:16:16]] [SUCCESS] Screenshot refreshed
[[14:16:16]] [INFO] Refreshing screenshot...
[[14:16:16]] [INFO] DbM0d0m6rU=pass
[[14:16:10]] [INFO] DbM0d0m6rU=running
[[14:16:10]] [INFO] Executing action 444/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[14:16:10]] [SUCCESS] Screenshot refreshed successfully
[[14:16:10]] [SUCCESS] Screenshot refreshed
[[14:16:10]] [INFO] Refreshing screenshot...
[[14:16:10]] [INFO] saiPPHQSPa=pass
[[14:16:05]] [INFO] saiPPHQSPa=running
[[14:16:05]] [INFO] Executing action 443/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:16:05]] [SUCCESS] Screenshot refreshed successfully
[[14:16:04]] [SUCCESS] Screenshot refreshed
[[14:16:04]] [INFO] Refreshing screenshot...
[[14:16:04]] [INFO] UpUSVInizv=pass
[[14:16:00]] [INFO] UpUSVInizv=running
[[14:16:00]] [INFO] Executing action 442/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[14:16:00]] [SUCCESS] Screenshot refreshed successfully
[[14:16:00]] [SUCCESS] Screenshot refreshed
[[14:16:00]] [INFO] Refreshing screenshot...
[[14:16:00]] [INFO] Iab9zCfpqO=pass
[[14:15:43]] [INFO] Iab9zCfpqO=running
[[14:15:43]] [INFO] Executing action 441/643: Tap on element with accessibility_id: Add to bag
[[14:15:43]] [SUCCESS] Screenshot refreshed successfully
[[14:15:43]] [SUCCESS] Screenshot refreshed
[[14:15:43]] [INFO] Refreshing screenshot...
[[14:15:43]] [INFO] Qy0Y0uJchm=pass
[[14:15:39]] [INFO] Qy0Y0uJchm=running
[[14:15:39]] [INFO] Executing action 440/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[14:15:39]] [SUCCESS] Screenshot refreshed successfully
[[14:15:39]] [SUCCESS] Screenshot refreshed
[[14:15:39]] [INFO] Refreshing screenshot...
[[14:15:39]] [INFO] YHaMIjULRf=pass
[[14:15:33]] [INFO] YHaMIjULRf=running
[[14:15:33]] [INFO] Executing action 439/643: Tap on Text: "List"
[[14:15:33]] [SUCCESS] Screenshot refreshed successfully
[[14:15:33]] [SUCCESS] Screenshot refreshed
[[14:15:33]] [INFO] Refreshing screenshot...
[[14:15:33]] [INFO] igReeDqips=pass
[[14:15:28]] [INFO] igReeDqips=running
[[14:15:28]] [INFO] Executing action 438/643: Tap on image: env[catalogue-menu-img]
[[14:15:28]] [SUCCESS] Screenshot refreshed successfully
[[14:15:28]] [SUCCESS] Screenshot refreshed
[[14:15:28]] [INFO] Refreshing screenshot...
[[14:15:28]] [INFO] gcSsGpqKwk=pass
[[14:15:05]] [INFO] gcSsGpqKwk=running
[[14:15:05]] [INFO] Executing action 437/643: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[14:15:05]] [SUCCESS] Screenshot refreshed successfully
[[14:15:04]] [SUCCESS] Screenshot refreshed
[[14:15:04]] [INFO] Refreshing screenshot...
[[14:15:04]] [INFO] gkkQzTCmma=pass
[[14:15:00]] [INFO] gkkQzTCmma=running
[[14:15:00]] [INFO] Executing action 436/643: Tap on Text: "Catalogue"
[[14:15:00]] [SUCCESS] Screenshot refreshed successfully
[[14:14:59]] [SUCCESS] Screenshot refreshed
[[14:14:59]] [INFO] Refreshing screenshot...
[[14:14:59]] [INFO] VpOhIxEl53=pass
[[14:14:47]] [INFO] VpOhIxEl53=running
[[14:14:47]] [INFO] Executing action 435/643: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
[[14:14:47]] [SUCCESS] Screenshot refreshed successfully
[[14:14:46]] [SUCCESS] Screenshot refreshed
[[14:14:46]] [INFO] Refreshing screenshot...
[[14:14:46]] [INFO] UpUSVInizv=pass
[[14:14:42]] [INFO] UpUSVInizv=running
[[14:14:42]] [INFO] Executing action 434/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[14:14:42]] [SUCCESS] Screenshot refreshed successfully
[[14:14:42]] [SUCCESS] Screenshot refreshed
[[14:14:42]] [INFO] Refreshing screenshot...
[[14:14:42]] [INFO] Cmvm82hiAa=pass
[[14:14:35]] [INFO] Cmvm82hiAa=running
[[14:14:35]] [INFO] Executing action 433/643: Tap on element with accessibility_id: Add to bag
[[14:14:35]] [SUCCESS] Screenshot refreshed successfully
[[14:14:34]] [SUCCESS] Screenshot refreshed
[[14:14:34]] [INFO] Refreshing screenshot...
[[14:14:34]] [INFO] ZZPNqTJ65s=pass
[[14:14:29]] [INFO] ZZPNqTJ65s=running
[[14:14:29]] [INFO] Executing action 432/643: Swipe from (50%, 70%) to (50%, 30%)
[[14:14:29]] [SUCCESS] Screenshot refreshed successfully
[[14:14:29]] [SUCCESS] Screenshot refreshed
[[14:14:29]] [INFO] Refreshing screenshot...
[[14:14:29]] [INFO] JcAR0JctQ6=pass
[[14:14:25]] [INFO] JcAR0JctQ6=running
[[14:14:25]] [INFO] Executing action 431/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[14:14:25]] [SUCCESS] Screenshot refreshed successfully
[[14:14:25]] [SUCCESS] Screenshot refreshed
[[14:14:25]] [INFO] Refreshing screenshot...
[[14:14:25]] [INFO] Pd7cReoJM6=pass
[[14:14:19]] [INFO] Pd7cReoJM6=running
[[14:14:19]] [INFO] Executing action 430/643: Tap on Text: "List"
[[14:14:19]] [SUCCESS] Screenshot refreshed successfully
[[14:14:19]] [SUCCESS] Screenshot refreshed
[[14:14:19]] [INFO] Refreshing screenshot...
[[14:14:19]] [INFO] igReeDqips=pass
[[14:14:14]] [INFO] igReeDqips=running
[[14:14:14]] [INFO] Executing action 429/643: Tap on image: env[catalogue-menu-img]
[[14:14:14]] [SUCCESS] Screenshot refreshed successfully
[[14:14:13]] [SUCCESS] Screenshot refreshed
[[14:14:13]] [INFO] Refreshing screenshot...
[[14:14:13]] [INFO] Jh6RTFWeOU=pass
[[14:13:51]] [INFO] Jh6RTFWeOU=running
[[14:13:51]] [INFO] Executing action 428/643: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[14:13:51]] [SUCCESS] Screenshot refreshed successfully
[[14:13:50]] [SUCCESS] Screenshot refreshed
[[14:13:50]] [INFO] Refreshing screenshot...
[[14:13:50]] [INFO] gkkQzTCmma=pass
[[14:13:46]] [INFO] gkkQzTCmma=running
[[14:13:46]] [INFO] Executing action 427/643: Tap on Text: "Catalogue"
[[14:13:46]] [SUCCESS] Screenshot refreshed successfully
[[14:13:45]] [SUCCESS] Screenshot refreshed
[[14:13:45]] [INFO] Refreshing screenshot...
[[14:13:45]] [INFO] QUeGIASAxV=pass
[[14:13:42]] [INFO] QUeGIASAxV=running
[[14:13:42]] [INFO] Executing action 426/643: Swipe from (50%, 50%) to (50%, 30%)
[[14:13:42]] [SUCCESS] Screenshot refreshed successfully
[[14:13:41]] [SUCCESS] Screenshot refreshed
[[14:13:41]] [INFO] Refreshing screenshot...
[[14:13:41]] [INFO] UpUSVInizv=pass
[[14:13:38]] [INFO] UpUSVInizv=running
[[14:13:38]] [INFO] Executing action 425/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[14:13:37]] [SUCCESS] Screenshot refreshed successfully
[[14:13:37]] [SUCCESS] Screenshot refreshed
[[14:13:37]] [INFO] Refreshing screenshot...
[[14:13:37]] [INFO] 0QtNHB5WEK=pass
[[14:13:34]] [INFO] 0QtNHB5WEK=running
[[14:13:34]] [INFO] Executing action 424/643: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[14:13:34]] [SUCCESS] Screenshot refreshed successfully
[[14:13:34]] [SUCCESS] Screenshot refreshed
[[14:13:34]] [INFO] Refreshing screenshot...
[[14:13:34]] [INFO] fTdGMJ3NH3=pass
[[14:13:31]] [INFO] fTdGMJ3NH3=running
[[14:13:31]] [INFO] Executing action 423/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[14:13:31]] [SUCCESS] Screenshot refreshed successfully
[[14:13:31]] [SUCCESS] Screenshot refreshed
[[14:13:31]] [INFO] Refreshing screenshot...
[[14:13:31]] [INFO] rYJcLPh8Aq=pass
[[14:13:28]] [INFO] rYJcLPh8Aq=running
[[14:13:28]] [INFO] Executing action 422/643: iOS Function: text - Text: "kmart au"
[[14:13:27]] [SUCCESS] Screenshot refreshed successfully
[[14:13:27]] [SUCCESS] Screenshot refreshed
[[14:13:27]] [INFO] Refreshing screenshot...
[[14:13:27]] [INFO] 0Q0fm6OTij=pass
[[14:13:25]] [INFO] 0Q0fm6OTij=running
[[14:13:25]] [INFO] Executing action 421/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[14:13:24]] [SUCCESS] Screenshot refreshed successfully
[[14:13:24]] [SUCCESS] Screenshot refreshed
[[14:13:24]] [INFO] Refreshing screenshot...
[[14:13:24]] [INFO] xVuuejtCFA=pass
[[14:13:21]] [INFO] xVuuejtCFA=running
[[14:13:21]] [INFO] Executing action 420/643: Restart app: com.apple.mobilesafari
[[14:13:20]] [SUCCESS] Screenshot refreshed successfully
[[14:13:20]] [SUCCESS] Screenshot refreshed
[[14:13:20]] [INFO] Refreshing screenshot...
[[14:13:20]] [INFO] LcYLwUffqj=pass
[[14:13:16]] [INFO] LcYLwUffqj=running
[[14:13:16]] [INFO] Executing action 419/643: Tap on Text: "out"
[[14:13:16]] [SUCCESS] Screenshot refreshed successfully
[[14:13:16]] [SUCCESS] Screenshot refreshed
[[14:13:16]] [INFO] Refreshing screenshot...
[[14:13:16]] [INFO] ZZPNqTJ65s=pass
[[14:13:12]] [INFO] ZZPNqTJ65s=running
[[14:13:12]] [INFO] Executing action 418/643: Swipe from (50%, 70%) to (50%, 30%)
[[14:13:11]] [SUCCESS] Screenshot refreshed successfully
[[14:13:11]] [SUCCESS] Screenshot refreshed
[[14:13:11]] [INFO] Refreshing screenshot...
[[14:13:11]] [INFO] UpUSVInizv=pass
[[14:13:07]] [INFO] UpUSVInizv=running
[[14:13:07]] [INFO] Executing action 417/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[14:13:07]] [SUCCESS] Screenshot refreshed successfully
[[14:13:06]] [SUCCESS] Screenshot refreshed
[[14:13:06]] [INFO] Refreshing screenshot...
[[14:13:06]] [INFO] hCCEvRtj1A=pass
[[14:13:03]] [INFO] hCCEvRtj1A=running
[[14:13:03]] [INFO] Executing action 416/643: Restart app: env[appid]
[[14:13:03]] [SUCCESS] Screenshot refreshed successfully
[[14:13:02]] [SUCCESS] Screenshot refreshed
[[14:13:02]] [INFO] Refreshing screenshot...
[[14:13:02]] [INFO] V42eHtTRYW=pass
[[14:12:56]] [INFO] V42eHtTRYW=running
[[14:12:56]] [INFO] Executing action 415/643: Wait for 5 ms
[[14:12:56]] [SUCCESS] Screenshot refreshed successfully
[[14:12:56]] [SUCCESS] Screenshot refreshed
[[14:12:56]] [INFO] Refreshing screenshot...
[[14:12:56]] [INFO] GRwHMVK4sA=pass
[[14:12:54]] [INFO] GRwHMVK4sA=running
[[14:12:54]] [INFO] Executing action 414/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[14:12:53]] [SUCCESS] Screenshot refreshed successfully
[[14:12:53]] [SUCCESS] Screenshot refreshed
[[14:12:53]] [INFO] Refreshing screenshot...
[[14:12:53]] [INFO] V42eHtTRYW=pass
[[14:12:47]] [INFO] V42eHtTRYW=running
[[14:12:47]] [INFO] Executing action 413/643: Wait for 5 ms
[[14:12:46]] [SUCCESS] Screenshot refreshed successfully
[[14:12:46]] [SUCCESS] Screenshot refreshed
[[14:12:46]] [INFO] Refreshing screenshot...
[[14:12:46]] [INFO] LfyQctrEJn=pass
[[14:12:45]] [INFO] LfyQctrEJn=running
[[14:12:45]] [INFO] Executing action 412/643: Launch app: com.apple.Preferences
[[14:12:45]] [SUCCESS] Screenshot refreshed successfully
[[14:12:44]] [SUCCESS] Screenshot refreshed
[[14:12:44]] [INFO] Refreshing screenshot...
[[14:12:44]] [INFO] seQcUKjkSU=pass
[[14:12:43]] [INFO] seQcUKjkSU=running
[[14:12:43]] [INFO] Executing action 411/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[14:12:43]] [SUCCESS] Screenshot refreshed successfully
[[14:12:42]] [SUCCESS] Screenshot refreshed
[[14:12:42]] [INFO] Refreshing screenshot...
[[14:12:42]] [INFO] UpUSVInizv=pass
[[14:12:40]] [INFO] UpUSVInizv=running
[[14:12:40]] [INFO] Executing action 410/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[14:12:40]] [SUCCESS] Screenshot refreshed successfully
[[14:12:40]] [SUCCESS] Screenshot refreshed
[[14:12:40]] [INFO] Refreshing screenshot...
[[14:12:40]] [INFO] WoymrHdtrO=pass
[[14:12:38]] [INFO] WoymrHdtrO=running
[[14:12:38]] [INFO] Executing action 409/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[14:12:38]] [SUCCESS] Screenshot refreshed successfully
[[14:12:38]] [SUCCESS] Screenshot refreshed
[[14:12:38]] [INFO] Refreshing screenshot...
[[14:12:38]] [INFO] 6xgrAWyfZ4=pass
[[14:12:36]] [INFO] 6xgrAWyfZ4=running
[[14:12:36]] [INFO] Executing action 408/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[14:12:36]] [SUCCESS] Screenshot refreshed successfully
[[14:12:35]] [SUCCESS] Screenshot refreshed
[[14:12:35]] [INFO] Refreshing screenshot...
[[14:12:35]] [INFO] eSr9EFlJek=pass
[[14:12:34]] [INFO] eSr9EFlJek=running
[[14:12:34]] [INFO] Executing action 407/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[14:12:34]] [SUCCESS] Screenshot refreshed successfully
[[14:12:33]] [SUCCESS] Screenshot refreshed
[[14:12:33]] [INFO] Refreshing screenshot...
[[14:12:33]] [INFO] 3KNqlNy6Bj=pass
[[14:12:31]] [INFO] 3KNqlNy6Bj=running
[[14:12:31]] [INFO] Executing action 406/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[14:12:31]] [SUCCESS] Screenshot refreshed successfully
[[14:12:31]] [SUCCESS] Screenshot refreshed
[[14:12:31]] [INFO] Refreshing screenshot...
[[14:12:31]] [INFO] cokvFXhj4c=pass
[[14:12:29]] [INFO] cokvFXhj4c=running
[[14:12:29]] [INFO] Executing action 405/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[14:12:29]] [SUCCESS] Screenshot refreshed successfully
[[14:12:29]] [SUCCESS] Screenshot refreshed
[[14:12:29]] [INFO] Refreshing screenshot...
[[14:12:29]] [INFO] oSQ8sPdVOJ=pass
[[14:12:25]] [INFO] oSQ8sPdVOJ=running
[[14:12:25]] [INFO] Executing action 404/643: Restart app: env[appid]
[[14:12:25]] [SUCCESS] Screenshot refreshed successfully
[[14:12:25]] [SUCCESS] Screenshot refreshed
[[14:12:25]] [INFO] Refreshing screenshot...
[[14:12:25]] [INFO] V42eHtTRYW=pass
[[14:12:19]] [INFO] V42eHtTRYW=running
[[14:12:19]] [INFO] Executing action 403/643: Wait for 5 ms
[[14:12:18]] [SUCCESS] Screenshot refreshed successfully
[[14:12:18]] [SUCCESS] Screenshot refreshed
[[14:12:18]] [INFO] Refreshing screenshot...
[[14:12:18]] [INFO] jUCAk6GJc4=pass
[[14:12:16]] [INFO] jUCAk6GJc4=running
[[14:12:16]] [INFO] Executing action 402/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[14:12:16]] [SUCCESS] Screenshot refreshed successfully
[[14:12:15]] [SUCCESS] Screenshot refreshed
[[14:12:15]] [INFO] Refreshing screenshot...
[[14:12:15]] [INFO] V42eHtTRYW=pass
[[14:12:09]] [INFO] V42eHtTRYW=running
[[14:12:09]] [INFO] Executing action 401/643: Wait for 5 ms
[[14:12:09]] [SUCCESS] Screenshot refreshed successfully
[[14:12:09]] [SUCCESS] Screenshot refreshed
[[14:12:09]] [INFO] Refreshing screenshot...
[[14:12:09]] [INFO] w1RV76df9x=pass
[[14:12:05]] [INFO] w1RV76df9x=running
[[14:12:05]] [INFO] Executing action 400/643: Tap on Text: "Wi-Fi"
[[14:12:05]] [SUCCESS] Screenshot refreshed successfully
[[14:12:04]] [SUCCESS] Screenshot refreshed
[[14:12:04]] [INFO] Refreshing screenshot...
[[14:12:04]] [INFO] LfyQctrEJn=pass
[[14:12:02]] [INFO] LfyQctrEJn=running
[[14:12:02]] [INFO] Executing action 399/643: Launch app: com.apple.Preferences
[[14:12:02]] [SUCCESS] Screenshot refreshed successfully
[[14:12:01]] [SUCCESS] Screenshot refreshed
[[14:12:01]] [INFO] Refreshing screenshot...
[[14:12:01]] [INFO] mIKA85kXaW=pass
[[14:11:59]] [SUCCESS] Screenshot refreshed successfully
[[14:11:59]] [INFO] mIKA85kXaW=running
[[14:11:59]] [INFO] Executing action 398/643: Terminate app: com.apple.Preferences
[[14:11:59]] [SUCCESS] Screenshot refreshed
[[14:11:59]] [INFO] Refreshing screenshot...
[[14:11:59]] [SUCCESS] Screenshot refreshed successfully
[[14:11:58]] [SUCCESS] Screenshot refreshed
[[14:11:58]] [INFO] Refreshing screenshot...
[[14:11:54]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[14:11:54]] [SUCCESS] Screenshot refreshed successfully
[[14:11:54]] [SUCCESS] Screenshot refreshed
[[14:11:54]] [INFO] Refreshing screenshot...
[[14:11:50]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:11:49]] [SUCCESS] Screenshot refreshed successfully
[[14:11:49]] [SUCCESS] Screenshot refreshed
[[14:11:49]] [INFO] Refreshing screenshot...
[[14:11:45]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[14:11:44]] [SUCCESS] Screenshot refreshed successfully
[[14:11:44]] [SUCCESS] Screenshot refreshed
[[14:11:44]] [INFO] Refreshing screenshot...
[[14:11:41]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:11:40]] [SUCCESS] Screenshot refreshed successfully
[[14:11:40]] [SUCCESS] Screenshot refreshed
[[14:11:40]] [INFO] Refreshing screenshot...
[[14:11:35]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:11:35]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[14:11:35]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[14:11:35]] [INFO] gx05zu87DK=running
[[14:11:35]] [INFO] Executing action 397/643: Execute Test Case: Kmart-Signin (5 steps)
[[14:11:35]] [SUCCESS] Screenshot refreshed successfully
[[14:11:35]] [SUCCESS] Screenshot refreshed
[[14:11:35]] [INFO] Refreshing screenshot...
[[14:11:35]] [INFO] rJ86z4njuR=pass
[[14:11:32]] [INFO] rJ86z4njuR=running
[[14:11:32]] [INFO] Executing action 396/643: iOS Function: alert_accept
[[14:11:32]] [SUCCESS] Screenshot refreshed successfully
[[14:11:32]] [SUCCESS] Screenshot refreshed
[[14:11:32]] [INFO] Refreshing screenshot...
[[14:11:32]] [INFO] veukWo4573=pass
[[14:11:28]] [INFO] veukWo4573=running
[[14:11:28]] [INFO] Executing action 395/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[14:11:28]] [SUCCESS] Screenshot refreshed successfully
[[14:11:27]] [SUCCESS] Screenshot refreshed
[[14:11:27]] [INFO] Refreshing screenshot...
[[14:11:27]] [INFO] XEbZHdi0GT=pass
[[14:11:15]] [SUCCESS] Screenshot refreshed successfully
[[14:11:15]] [INFO] XEbZHdi0GT=running
[[14:11:15]] [INFO] Executing action 394/643: Restart app: env[appid]
[[14:11:15]] [SUCCESS] Screenshot refreshed
[[14:11:15]] [INFO] Refreshing screenshot...
[[14:11:15]] [SUCCESS] Screenshot refreshed successfully
[[14:11:14]] [SUCCESS] Screenshot refreshed
[[14:11:14]] [INFO] Refreshing screenshot...
[[14:11:12]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:11:12]] [SUCCESS] Screenshot refreshed successfully
[[14:11:12]] [SUCCESS] Screenshot refreshed
[[14:11:12]] [INFO] Refreshing screenshot...
[[14:10:59]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:10:59]] [SUCCESS] Screenshot refreshed successfully
[[14:10:59]] [SUCCESS] Screenshot refreshed
[[14:10:59]] [INFO] Refreshing screenshot...
[[14:10:55]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:10:55]] [SUCCESS] Screenshot refreshed successfully
[[14:10:55]] [SUCCESS] Screenshot refreshed
[[14:10:55]] [INFO] Refreshing screenshot...
[[14:10:51]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:10:51]] [SUCCESS] Screenshot refreshed successfully
[[14:10:50]] [SUCCESS] Screenshot refreshed
[[14:10:50]] [INFO] Refreshing screenshot...
[[14:10:44]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:10:44]] [SUCCESS] Screenshot refreshed successfully
[[14:10:44]] [SUCCESS] Screenshot refreshed
[[14:10:44]] [INFO] Refreshing screenshot...
[[14:10:38]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:10:38]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:10:38]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:10:38]] [INFO] ubySifeF65=running
[[14:10:38]] [INFO] Executing action 393/643: cleanupSteps action
[[14:10:38]] [SUCCESS] Screenshot refreshed successfully
[[14:10:37]] [SUCCESS] Screenshot refreshed
[[14:10:37]] [INFO] Refreshing screenshot...
[[14:10:37]] [INFO] xyHVihJMBi=pass
[[14:10:34]] [INFO] xyHVihJMBi=running
[[14:10:34]] [INFO] Executing action 392/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:10:33]] [SUCCESS] Screenshot refreshed successfully
[[14:10:33]] [SUCCESS] Screenshot refreshed
[[14:10:33]] [INFO] Refreshing screenshot...
[[14:10:33]] [INFO] mWeLQtXiL6=pass
[[14:10:26]] [INFO] mWeLQtXiL6=running
[[14:10:26]] [INFO] Executing action 391/643: Swipe from (50%, 70%) to (50%, 30%)
[[14:10:26]] [SUCCESS] Screenshot refreshed successfully
[[14:10:26]] [SUCCESS] Screenshot refreshed
[[14:10:26]] [INFO] Refreshing screenshot...
[[14:10:26]] [INFO] F4NGh9HrLw=pass
[[14:10:22]] [INFO] F4NGh9HrLw=running
[[14:10:22]] [INFO] Executing action 390/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:10:21]] [SUCCESS] Screenshot refreshed successfully
[[14:10:21]] [SUCCESS] Screenshot refreshed
[[14:10:21]] [INFO] Refreshing screenshot...
[[14:10:21]] [INFO] 0f2FSZYjWq=pass
[[14:10:05]] [INFO] 0f2FSZYjWq=running
[[14:10:05]] [INFO] Executing action 389/643: Check if element with text="Melbourne" exists
[[14:10:05]] [SUCCESS] Screenshot refreshed successfully
[[14:10:05]] [SUCCESS] Screenshot refreshed
[[14:10:05]] [INFO] Refreshing screenshot...
[[14:10:05]] [INFO] Tebej51pT2=pass
[[14:10:01]] [INFO] Tebej51pT2=running
[[14:10:01]] [INFO] Executing action 388/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[14:10:01]] [SUCCESS] Screenshot refreshed successfully
[[14:10:01]] [SUCCESS] Screenshot refreshed
[[14:10:01]] [INFO] Refreshing screenshot...
[[14:10:01]] [INFO] I4gwigwXSj=pass
[[14:09:57]] [INFO] I4gwigwXSj=running
[[14:09:57]] [INFO] Executing action 387/643: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[14:09:57]] [SUCCESS] Screenshot refreshed successfully
[[14:09:57]] [SUCCESS] Screenshot refreshed
[[14:09:57]] [INFO] Refreshing screenshot...
[[14:09:57]] [INFO] eVytJrry9x=pass
[[14:09:53]] [INFO] eVytJrry9x=running
[[14:09:53]] [INFO] Executing action 386/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:09:53]] [SUCCESS] Screenshot refreshed successfully
[[14:09:53]] [SUCCESS] Screenshot refreshed
[[14:09:53]] [INFO] Refreshing screenshot...
[[14:09:53]] [INFO] s8h8VDUIOC=pass
[[14:09:48]] [INFO] s8h8VDUIOC=running
[[14:09:48]] [INFO] Executing action 385/643: Swipe from (50%, 70%) to (50%, 30%)
[[14:09:48]] [SUCCESS] Screenshot refreshed successfully
[[14:09:48]] [SUCCESS] Screenshot refreshed
[[14:09:48]] [INFO] Refreshing screenshot...
[[14:09:48]] [INFO] bkU728TrRF=pass
[[14:09:42]] [INFO] bkU728TrRF=running
[[14:09:42]] [INFO] Executing action 384/643: Tap on element with accessibility_id: Done
[[14:09:42]] [SUCCESS] Screenshot refreshed successfully
[[14:09:41]] [SUCCESS] Screenshot refreshed
[[14:09:41]] [INFO] Refreshing screenshot...
[[14:09:41]] [INFO] ZWpYNcpbFA=pass
[[14:09:37]] [INFO] ZWpYNcpbFA=running
[[14:09:37]] [INFO] Executing action 383/643: Tap on Text: "VIC"
[[14:09:37]] [SUCCESS] Screenshot refreshed successfully
[[14:09:36]] [SUCCESS] Screenshot refreshed
[[14:09:36]] [INFO] Refreshing screenshot...
[[14:09:36]] [INFO] Wld5Urg70o=pass
[[14:09:30]] [INFO] Wld5Urg70o=running
[[14:09:30]] [INFO] Executing action 382/643: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "3000"
[[14:09:30]] [SUCCESS] Screenshot refreshed successfully
[[14:09:29]] [SUCCESS] Screenshot refreshed
[[14:09:29]] [INFO] Refreshing screenshot...
[[14:09:29]] [INFO] QpBLC6BStn=pass
[[14:09:23]] [INFO] QpBLC6BStn=running
[[14:09:23]] [INFO] Executing action 381/643: Tap on element with accessibility_id: delete
[[14:09:23]] [SUCCESS] Screenshot refreshed successfully
[[14:09:23]] [SUCCESS] Screenshot refreshed
[[14:09:23]] [INFO] Refreshing screenshot...
[[14:09:23]] [INFO] G4A3KBlXHq=pass
[[14:09:18]] [INFO] G4A3KBlXHq=running
[[14:09:18]] [INFO] Executing action 380/643: Tap on Text: "Nearby"
[[14:09:18]] [SUCCESS] Screenshot refreshed successfully
[[14:09:18]] [SUCCESS] Screenshot refreshed
[[14:09:18]] [INFO] Refreshing screenshot...
[[14:09:18]] [INFO] uArzgeZYf7=pass
[[14:09:14]] [INFO] uArzgeZYf7=running
[[14:09:14]] [INFO] Executing action 379/643: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[14:09:14]] [SUCCESS] Screenshot refreshed successfully
[[14:09:14]] [SUCCESS] Screenshot refreshed
[[14:09:14]] [INFO] Refreshing screenshot...
[[14:09:14]] [INFO] 3gJsiap2Ds=pass
[[14:09:10]] [INFO] 3gJsiap2Ds=running
[[14:09:10]] [INFO] Executing action 378/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[14:09:09]] [SUCCESS] Screenshot refreshed successfully
[[14:09:09]] [SUCCESS] Screenshot refreshed
[[14:09:09]] [INFO] Refreshing screenshot...
[[14:09:09]] [INFO] EReijW5iNX=pass
[[14:09:05]] [INFO] EReijW5iNX=running
[[14:09:05]] [INFO] Executing action 377/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:09:04]] [SUCCESS] Screenshot refreshed successfully
[[14:09:04]] [SUCCESS] Screenshot refreshed
[[14:09:04]] [INFO] Refreshing screenshot...
[[14:09:04]] [INFO] 94ikwhIEE2=pass
[[14:09:00]] [INFO] 94ikwhIEE2=running
[[14:09:00]] [INFO] Executing action 376/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:09:00]] [SUCCESS] Screenshot refreshed successfully
[[14:08:59]] [SUCCESS] Screenshot refreshed
[[14:08:59]] [INFO] Refreshing screenshot...
[[14:08:59]] [INFO] q8oldD8uZt=pass
[[14:08:56]] [INFO] q8oldD8uZt=running
[[14:08:56]] [INFO] Executing action 375/643: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[14:08:56]] [SUCCESS] Screenshot refreshed successfully
[[14:08:56]] [SUCCESS] Screenshot refreshed
[[14:08:56]] [INFO] Refreshing screenshot...
[[14:08:56]] [INFO] Jf2wJyOphY=pass
[[14:08:37]] [INFO] Jf2wJyOphY=running
[[14:08:37]] [INFO] Executing action 374/643: Tap on element with accessibility_id: Add to bag
[[14:08:37]] [SUCCESS] Screenshot refreshed successfully
[[14:08:37]] [SUCCESS] Screenshot refreshed
[[14:08:37]] [INFO] Refreshing screenshot...
[[14:08:37]] [INFO] eRCmRhc3re=pass
[[14:08:24]] [INFO] eRCmRhc3re=running
[[14:08:24]] [INFO] Executing action 373/643: Check if element with text="Broadway" exists
[[14:08:23]] [SUCCESS] Screenshot refreshed successfully
[[14:08:23]] [SUCCESS] Screenshot refreshed
[[14:08:23]] [INFO] Refreshing screenshot...
[[14:08:23]] [INFO] ORI6ZFMBK1=pass
[[14:08:19]] [INFO] ORI6ZFMBK1=running
[[14:08:19]] [INFO] Executing action 372/643: Tap on Text: "Save"
[[14:08:19]] [SUCCESS] Screenshot refreshed successfully
[[14:08:19]] [SUCCESS] Screenshot refreshed
[[14:08:19]] [INFO] Refreshing screenshot...
[[14:08:19]] [INFO] hr0IVckpYI=pass
[[14:08:14]] [INFO] hr0IVckpYI=running
[[14:08:14]] [INFO] Executing action 371/643: Wait till accessibility_id=btnSaveOrContinue
[[14:08:14]] [SUCCESS] Screenshot refreshed successfully
[[14:08:13]] [SUCCESS] Screenshot refreshed
[[14:08:13]] [INFO] Refreshing screenshot...
[[14:08:13]] [INFO] H0ODFz7sWJ=pass
[[14:08:09]] [INFO] H0ODFz7sWJ=running
[[14:08:09]] [INFO] Executing action 370/643: Tap on Text: "2000"
[[14:08:09]] [SUCCESS] Screenshot refreshed successfully
[[14:08:09]] [SUCCESS] Screenshot refreshed
[[14:08:09]] [INFO] Refreshing screenshot...
[[14:08:09]] [INFO] uZHvvAzVfx=pass
[[14:08:04]] [INFO] uZHvvAzVfx=running
[[14:08:04]] [INFO] Executing action 369/643: textClear action
[[14:08:04]] [SUCCESS] Screenshot refreshed successfully
[[14:08:04]] [SUCCESS] Screenshot refreshed
[[14:08:04]] [INFO] Refreshing screenshot...
[[14:08:04]] [INFO] WmNWcsWVHv=pass
[[14:07:58]] [INFO] WmNWcsWVHv=running
[[14:07:58]] [INFO] Executing action 368/643: Tap on element with accessibility_id: Search suburb or postcode
[[14:07:58]] [SUCCESS] Screenshot refreshed successfully
[[14:07:58]] [SUCCESS] Screenshot refreshed
[[14:07:58]] [INFO] Refreshing screenshot...
[[14:07:58]] [INFO] lnjoz8hHUU=pass
[[14:07:53]] [INFO] lnjoz8hHUU=running
[[14:07:53]] [INFO] Executing action 367/643: Tap on Text: "Edit"
[[14:07:53]] [SUCCESS] Screenshot refreshed successfully
[[14:07:52]] [SUCCESS] Screenshot refreshed
[[14:07:52]] [INFO] Refreshing screenshot...
[[14:07:52]] [INFO] letbbewlnA=pass
[[14:07:48]] [INFO] letbbewlnA=running
[[14:07:48]] [INFO] Executing action 366/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[14:07:48]] [SUCCESS] Screenshot refreshed successfully
[[14:07:48]] [SUCCESS] Screenshot refreshed
[[14:07:48]] [INFO] Refreshing screenshot...
[[14:07:48]] [INFO] trBISwJ8eZ=pass
[[14:07:44]] [INFO] trBISwJ8eZ=running
[[14:07:44]] [INFO] Executing action 365/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:07:44]] [SUCCESS] Screenshot refreshed successfully
[[14:07:43]] [SUCCESS] Screenshot refreshed
[[14:07:43]] [INFO] Refreshing screenshot...
[[14:07:43]] [INFO] foVGMl9wvu=pass
[[14:07:40]] [INFO] foVGMl9wvu=running
[[14:07:40]] [INFO] Executing action 364/643: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:07:40]] [SUCCESS] Screenshot refreshed successfully
[[14:07:39]] [SUCCESS] Screenshot refreshed
[[14:07:39]] [INFO] Refreshing screenshot...
[[14:07:39]] [INFO] 73NABkfWyY=pass
[[14:07:25]] [INFO] 73NABkfWyY=running
[[14:07:25]] [INFO] Executing action 363/643: Check if element with text="Tarneit" exists
[[14:07:25]] [SUCCESS] Screenshot refreshed successfully
[[14:07:24]] [SUCCESS] Screenshot refreshed
[[14:07:24]] [INFO] Refreshing screenshot...
[[14:07:24]] [INFO] pKjXoj4mNg=pass
[[14:07:20]] [INFO] pKjXoj4mNg=running
[[14:07:20]] [INFO] Executing action 362/643: Tap on Text: "Save"
[[14:07:20]] [SUCCESS] Screenshot refreshed successfully
[[14:07:19]] [SUCCESS] Screenshot refreshed
[[14:07:19]] [INFO] Refreshing screenshot...
[[14:07:19]] [INFO] M3dXqigqRv=pass
[[14:07:15]] [INFO] M3dXqigqRv=running
[[14:07:15]] [INFO] Executing action 361/643: Wait till accessibility_id=btnSaveOrContinue
[[14:07:15]] [SUCCESS] Screenshot refreshed successfully
[[14:07:14]] [SUCCESS] Screenshot refreshed
[[14:07:14]] [INFO] Refreshing screenshot...
[[14:07:14]] [INFO] GYRHQr7TWx=pass
[[14:07:10]] [INFO] GYRHQr7TWx=running
[[14:07:10]] [INFO] Executing action 360/643: Tap on Text: "current"
[[14:07:10]] [SUCCESS] Screenshot refreshed successfully
[[14:07:10]] [SUCCESS] Screenshot refreshed
[[14:07:10]] [INFO] Refreshing screenshot...
[[14:07:10]] [INFO] kiM0WyWE9I=pass
[[14:07:05]] [INFO] kiM0WyWE9I=running
[[14:07:05]] [INFO] Executing action 359/643: Wait till accessibility_id=btnCurrentLocationButton
[[14:07:05]] [SUCCESS] Screenshot refreshed successfully
[[14:07:05]] [SUCCESS] Screenshot refreshed
[[14:07:05]] [INFO] Refreshing screenshot...
[[14:07:05]] [INFO] VkUKQbf1Qt=pass
[[14:07:00]] [INFO] VkUKQbf1Qt=running
[[14:07:00]] [INFO] Executing action 358/643: Tap on Text: "Edit"
[[14:07:00]] [SUCCESS] Screenshot refreshed successfully
[[14:07:00]] [SUCCESS] Screenshot refreshed
[[14:07:00]] [INFO] Refreshing screenshot...
[[14:07:00]] [INFO] C6JHhLdWTv=pass
[[14:06:56]] [INFO] C6JHhLdWTv=running
[[14:06:56]] [INFO] Executing action 357/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:06:56]] [SUCCESS] Screenshot refreshed successfully
[[14:06:56]] [SUCCESS] Screenshot refreshed
[[14:06:56]] [INFO] Refreshing screenshot...
[[14:06:56]] [INFO] IupxLP2Jsr=pass
[[14:06:52]] [INFO] IupxLP2Jsr=running
[[14:06:52]] [INFO] Executing action 356/643: iOS Function: text - Text: "Uno card"
[[14:06:51]] [SUCCESS] Screenshot refreshed successfully
[[14:06:51]] [SUCCESS] Screenshot refreshed
[[14:06:51]] [INFO] Refreshing screenshot...
[[14:06:51]] [INFO] 70iOOakiG7=pass
[[14:06:46]] [INFO] 70iOOakiG7=running
[[14:06:46]] [INFO] Executing action 355/643: Tap on Text: "Find"
[[14:06:46]] [SUCCESS] Screenshot refreshed successfully
[[14:06:46]] [SUCCESS] Screenshot refreshed
[[14:06:46]] [INFO] Refreshing screenshot...
[[14:06:46]] [INFO] vL26X6PBjc=pass
[[14:06:39]] [INFO] vL26X6PBjc=running
[[14:06:39]] [INFO] Executing action 354/643: Tap if locator exists: accessibility_id="btnUpdate"
[[14:06:39]] [SUCCESS] Screenshot refreshed successfully
[[14:06:39]] [SUCCESS] Screenshot refreshed
[[14:06:39]] [INFO] Refreshing screenshot...
[[14:06:39]] [INFO] E2jpN7BioW=pass
[[14:06:35]] [INFO] E2jpN7BioW=running
[[14:06:35]] [INFO] Executing action 353/643: Tap on Text: "Save"
[[14:06:34]] [SUCCESS] Screenshot refreshed successfully
[[14:06:34]] [SUCCESS] Screenshot refreshed
[[14:06:34]] [INFO] Refreshing screenshot...
[[14:06:34]] [INFO] Sl6eiqZkRm=pass
[[14:06:30]] [INFO] Sl6eiqZkRm=running
[[14:06:30]] [INFO] Executing action 352/643: Wait till accessibility_id=btnSaveOrContinue
[[14:06:29]] [SUCCESS] Screenshot refreshed successfully
[[14:06:29]] [SUCCESS] Screenshot refreshed
[[14:06:29]] [INFO] Refreshing screenshot...
[[14:06:29]] [INFO] mw9GQ4mzRE=pass
[[14:06:25]] [INFO] mw9GQ4mzRE=running
[[14:06:25]] [INFO] Executing action 351/643: Tap on Text: "2000"
[[14:06:24]] [SUCCESS] Screenshot refreshed successfully
[[14:06:24]] [SUCCESS] Screenshot refreshed
[[14:06:24]] [INFO] Refreshing screenshot...
[[14:06:24]] [INFO] kbdEPCPYod=pass
[[14:06:20]] [INFO] kbdEPCPYod=running
[[14:06:20]] [INFO] Executing action 350/643: textClear action
[[14:06:19]] [SUCCESS] Screenshot refreshed successfully
[[14:06:19]] [SUCCESS] Screenshot refreshed
[[14:06:19]] [INFO] Refreshing screenshot...
[[14:06:19]] [INFO] 8WCusTZ8q9=pass
[[14:06:14]] [INFO] 8WCusTZ8q9=running
[[14:06:14]] [INFO] Executing action 349/643: Tap on element with accessibility_id: Search suburb or postcode
[[14:06:13]] [SUCCESS] Screenshot refreshed successfully
[[14:06:13]] [SUCCESS] Screenshot refreshed
[[14:06:13]] [INFO] Refreshing screenshot...
[[14:06:13]] [INFO] QMXBlswP6H=pass
[[14:06:09]] [INFO] QMXBlswP6H=running
[[14:06:09]] [INFO] Executing action 348/643: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[14:06:09]] [SUCCESS] Screenshot refreshed successfully
[[14:06:09]] [SUCCESS] Screenshot refreshed
[[14:06:09]] [INFO] Refreshing screenshot...
[[14:06:09]] [INFO] m0956RsrdM=pass
[[14:06:05]] [SUCCESS] Screenshot refreshed successfully
[[14:06:05]] [INFO] m0956RsrdM=running
[[14:06:05]] [INFO] Executing action 347/643: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[14:06:05]] [SUCCESS] Screenshot refreshed
[[14:06:05]] [INFO] Refreshing screenshot...
[[14:06:05]] [SUCCESS] Screenshot refreshed successfully
[[14:06:05]] [SUCCESS] Screenshot refreshed
[[14:06:05]] [INFO] Refreshing screenshot...
[[14:06:00]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[14:06:00]] [SUCCESS] Screenshot refreshed successfully
[[14:06:00]] [SUCCESS] Screenshot refreshed
[[14:06:00]] [INFO] Refreshing screenshot...
[[14:05:56]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:05:56]] [SUCCESS] Screenshot refreshed successfully
[[14:05:55]] [SUCCESS] Screenshot refreshed
[[14:05:55]] [INFO] Refreshing screenshot...
[[14:05:51]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[14:05:51]] [SUCCESS] Screenshot refreshed successfully
[[14:05:51]] [SUCCESS] Screenshot refreshed
[[14:05:51]] [INFO] Refreshing screenshot...
[[14:05:47]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:05:46]] [SUCCESS] Screenshot refreshed successfully
[[14:05:46]] [SUCCESS] Screenshot refreshed
[[14:05:46]] [INFO] Refreshing screenshot...
[[14:05:41]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:05:41]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[14:05:41]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[14:05:41]] [INFO] HlWGryBWT9=running
[[14:05:41]] [INFO] Executing action 346/643: Execute Test Case: Kmart-Signin (5 steps)
[[14:05:40]] [SUCCESS] Screenshot refreshed successfully
[[14:05:40]] [SUCCESS] Screenshot refreshed
[[14:05:40]] [INFO] Refreshing screenshot...
[[14:05:40]] [INFO] Azb1flbIJJ=pass
[[14:05:37]] [INFO] Azb1flbIJJ=running
[[14:05:37]] [INFO] Executing action 345/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:05:36]] [SUCCESS] Screenshot refreshed successfully
[[14:05:36]] [SUCCESS] Screenshot refreshed
[[14:05:36]] [INFO] Refreshing screenshot...
[[14:05:36]] [INFO] 2xC5fLfLe8=pass
[[14:05:34]] [INFO] 2xC5fLfLe8=running
[[14:05:34]] [INFO] Executing action 344/643: iOS Function: alert_accept
[[14:05:34]] [SUCCESS] Screenshot refreshed successfully
[[14:05:33]] [SUCCESS] Screenshot refreshed
[[14:05:33]] [INFO] Refreshing screenshot...
[[14:05:33]] [INFO] Y8vz7AJD1i=pass
[[14:05:26]] [INFO] Y8vz7AJD1i=running
[[14:05:26]] [INFO] Executing action 343/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:05:26]] [SUCCESS] Screenshot refreshed successfully
[[14:05:26]] [SUCCESS] Screenshot refreshed
[[14:05:26]] [INFO] Refreshing screenshot...
[[14:05:26]] [INFO] H9fy9qcFbZ=pass
[[14:05:14]] [SUCCESS] Screenshot refreshed successfully
[[14:05:13]] [INFO] H9fy9qcFbZ=running
[[14:05:13]] [INFO] Executing action 342/643: Restart app: env[appid]
[[14:05:13]] [SUCCESS] Screenshot refreshed
[[14:05:13]] [INFO] Refreshing screenshot...
[[14:05:13]] [SUCCESS] Screenshot refreshed
[[14:05:13]] [INFO] Refreshing screenshot...
[[14:05:11]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:05:10]] [SUCCESS] Screenshot refreshed successfully
[[14:05:10]] [SUCCESS] Screenshot refreshed
[[14:05:10]] [INFO] Refreshing screenshot...
[[14:04:58]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:04:57]] [SUCCESS] Screenshot refreshed successfully
[[14:04:57]] [SUCCESS] Screenshot refreshed
[[14:04:57]] [INFO] Refreshing screenshot...
[[14:04:54]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:04:53]] [SUCCESS] Screenshot refreshed successfully
[[14:04:53]] [SUCCESS] Screenshot refreshed
[[14:04:53]] [INFO] Refreshing screenshot...
[[14:04:49]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:04:49]] [SUCCESS] Screenshot refreshed successfully
[[14:04:49]] [SUCCESS] Screenshot refreshed
[[14:04:49]] [INFO] Refreshing screenshot...
[[14:04:43]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:04:42]] [SUCCESS] Screenshot refreshed successfully
[[14:04:42]] [SUCCESS] Screenshot refreshed
[[14:04:42]] [INFO] Refreshing screenshot...
[[14:04:38]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:04:38]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:04:38]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:04:37]] [INFO] OMgc2gHHyq=running
[[14:04:37]] [INFO] Executing action 341/643: cleanupSteps action
[[14:04:37]] [SUCCESS] Screenshot refreshed successfully
[[14:04:37]] [SUCCESS] Screenshot refreshed
[[14:04:37]] [INFO] Refreshing screenshot...
[[14:04:37]] [INFO] x4yLCZHaCR=pass
[[14:04:35]] [INFO] x4yLCZHaCR=running
[[14:04:35]] [INFO] Executing action 340/643: Terminate app: env[appid]
[[14:04:34]] [SUCCESS] Screenshot refreshed successfully
[[14:04:34]] [SUCCESS] Screenshot refreshed
[[14:04:34]] [INFO] Refreshing screenshot...
[[14:04:34]] [INFO] 2p13JoJbbA=pass
[[14:04:30]] [INFO] 2p13JoJbbA=running
[[14:04:30]] [INFO] Executing action 339/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:04:30]] [SUCCESS] Screenshot refreshed successfully
[[14:04:30]] [SUCCESS] Screenshot refreshed
[[14:04:30]] [INFO] Refreshing screenshot...
[[14:04:30]] [INFO] qHdMgerbTE=pass
[[14:04:26]] [INFO] qHdMgerbTE=running
[[14:04:26]] [INFO] Executing action 338/643: Swipe from (50%, 70%) to (50%, 30%)
[[14:04:25]] [SUCCESS] Screenshot refreshed successfully
[[14:04:25]] [SUCCESS] Screenshot refreshed
[[14:04:25]] [INFO] Refreshing screenshot...
[[14:04:25]] [INFO] F4NGh9HrLw=pass
[[14:04:21]] [INFO] F4NGh9HrLw=running
[[14:04:21]] [INFO] Executing action 337/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:04:21]] [SUCCESS] Screenshot refreshed successfully
[[14:04:21]] [SUCCESS] Screenshot refreshed
[[14:04:21]] [INFO] Refreshing screenshot...
[[14:04:21]] [INFO] 7mnBGa2GCk=pass
[[14:04:09]] [INFO] 7mnBGa2GCk=running
[[14:04:09]] [INFO] Executing action 336/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Save my location"]"
[[14:04:09]] [SUCCESS] Screenshot refreshed successfully
[[14:04:09]] [SUCCESS] Screenshot refreshed
[[14:04:09]] [INFO] Refreshing screenshot...
[[14:04:09]] [INFO] tWq2Qzn22D=pass
[[14:04:05]] [INFO] tWq2Qzn22D=running
[[14:04:05]] [INFO] Executing action 335/643: Tap on image: env[device-back-img]
[[14:04:05]] [SUCCESS] Screenshot refreshed successfully
[[14:04:04]] [SUCCESS] Screenshot refreshed
[[14:04:04]] [INFO] Refreshing screenshot...
[[14:04:04]] [INFO] ysJIY9A3gq=pass
[[14:03:52]] [INFO] ysJIY9A3gq=running
[[14:03:52]] [INFO] Executing action 334/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]"
[[14:03:52]] [SUCCESS] Screenshot refreshed successfully
[[14:03:52]] [SUCCESS] Screenshot refreshed
[[14:03:52]] [INFO] Refreshing screenshot...
[[14:03:52]] [INFO] jmKjclMUWT=pass
[[14:03:47]] [INFO] jmKjclMUWT=running
[[14:03:47]] [INFO] Executing action 333/643: Tap on Text: "current"
[[14:03:47]] [SUCCESS] Screenshot refreshed successfully
[[14:03:47]] [SUCCESS] Screenshot refreshed
[[14:03:47]] [INFO] Refreshing screenshot...
[[14:03:47]] [INFO] UoH0wdtcLk=pass
[[14:03:43]] [INFO] UoH0wdtcLk=running
[[14:03:43]] [INFO] Executing action 332/643: Tap on Text: "Edit"
[[14:03:42]] [SUCCESS] Screenshot refreshed successfully
[[14:03:42]] [SUCCESS] Screenshot refreshed
[[14:03:42]] [INFO] Refreshing screenshot...
[[14:03:42]] [INFO] U48qCNydwd=pass
[[14:03:39]] [INFO] U48qCNydwd=running
[[14:03:39]] [INFO] Executing action 331/643: Restart app: env[appid]
[[14:03:38]] [SUCCESS] Screenshot refreshed successfully
[[14:03:38]] [SUCCESS] Screenshot refreshed
[[14:03:38]] [INFO] Refreshing screenshot...
[[14:03:38]] [INFO] XjclKOaCTh=pass
[[14:03:34]] [INFO] XjclKOaCTh=running
[[14:03:34]] [INFO] Executing action 330/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[14:03:33]] [SUCCESS] Screenshot refreshed successfully
[[14:03:33]] [SUCCESS] Screenshot refreshed
[[14:03:33]] [INFO] Refreshing screenshot...
[[14:03:33]] [INFO] q6cKxgMAIn=pass
[[14:03:30]] [INFO] q6cKxgMAIn=running
[[14:03:30]] [INFO] Executing action 329/643: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[14:03:30]] [SUCCESS] Screenshot refreshed successfully
[[14:03:29]] [SUCCESS] Screenshot refreshed
[[14:03:29]] [INFO] Refreshing screenshot...
[[14:03:29]] [INFO] zdh8hKYC1a=pass
[[14:03:26]] [INFO] zdh8hKYC1a=running
[[14:03:26]] [INFO] Executing action 328/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[14:03:25]] [SUCCESS] Screenshot refreshed successfully
[[14:03:25]] [SUCCESS] Screenshot refreshed
[[14:03:25]] [INFO] Refreshing screenshot...
[[14:03:25]] [INFO] P4b2BITpCf=pass
[[14:03:22]] [INFO] P4b2BITpCf=running
[[14:03:22]] [INFO] Executing action 327/643: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[14:03:22]] [SUCCESS] Screenshot refreshed successfully
[[14:03:22]] [SUCCESS] Screenshot refreshed
[[14:03:22]] [INFO] Refreshing screenshot...
[[14:03:22]] [INFO] inrxgdWzXr=pass
[[14:03:18]] [INFO] inrxgdWzXr=running
[[14:03:18]] [INFO] Executing action 326/643: Tap on Text: "Store"
[[14:03:17]] [SUCCESS] Screenshot refreshed successfully
[[14:03:17]] [SUCCESS] Screenshot refreshed
[[14:03:17]] [INFO] Refreshing screenshot...
[[14:03:17]] [INFO] inrxgdWzXr=pass
[[14:03:13]] [INFO] inrxgdWzXr=running
[[14:03:13]] [INFO] Executing action 325/643: Tap on Text: "receipts"
[[14:03:13]] [SUCCESS] Screenshot refreshed successfully
[[14:03:13]] [SUCCESS] Screenshot refreshed
[[14:03:13]] [INFO] Refreshing screenshot...
[[14:03:13]] [INFO] GEMv6goQtW=pass
[[14:03:09]] [INFO] GEMv6goQtW=running
[[14:03:09]] [INFO] Executing action 324/643: Tap on image: env[device-back-img]
[[14:03:09]] [SUCCESS] Screenshot refreshed successfully
[[14:03:09]] [SUCCESS] Screenshot refreshed
[[14:03:09]] [INFO] Refreshing screenshot...
[[14:03:09]] [INFO] DhWa2PCBXE=pass
[[14:03:06]] [INFO] DhWa2PCBXE=running
[[14:03:06]] [INFO] Executing action 323/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[14:03:05]] [SUCCESS] Screenshot refreshed successfully
[[14:03:05]] [SUCCESS] Screenshot refreshed
[[14:03:05]] [INFO] Refreshing screenshot...
[[14:03:05]] [INFO] pk2DLZFBmx=pass
[[14:03:02]] [INFO] pk2DLZFBmx=running
[[14:03:02]] [INFO] Executing action 322/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[14:03:01]] [SUCCESS] Screenshot refreshed successfully
[[14:03:01]] [SUCCESS] Screenshot refreshed
[[14:03:01]] [INFO] Refreshing screenshot...
[[14:03:01]] [INFO] ShJSdXvmVL=pass
[[14:02:56]] [INFO] ShJSdXvmVL=running
[[14:02:56]] [INFO] Executing action 321/643: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[14:02:56]] [SUCCESS] Screenshot refreshed successfully
[[14:02:55]] [SUCCESS] Screenshot refreshed
[[14:02:55]] [INFO] Refreshing screenshot...
[[14:02:55]] [INFO] A57bC3QuEM=pass
[[14:02:51]] [INFO] A57bC3QuEM=running
[[14:02:51]] [INFO] Executing action 320/643: iOS Function: text - Text: "Wonderbaby@5"
[[14:02:51]] [SUCCESS] Screenshot refreshed successfully
[[14:02:51]] [SUCCESS] Screenshot refreshed
[[14:02:51]] [INFO] Refreshing screenshot...
[[14:02:51]] [INFO] d6vTfR4Y0D=pass
[[14:02:47]] [INFO] d6vTfR4Y0D=running
[[14:02:47]] [INFO] Executing action 319/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:02:46]] [SUCCESS] Screenshot refreshed successfully
[[14:02:46]] [SUCCESS] Screenshot refreshed
[[14:02:46]] [INFO] Refreshing screenshot...
[[14:02:46]] [INFO] g2CqCO1Kr6=pass
[[14:02:42]] [INFO] g2CqCO1Kr6=running
[[14:02:42]] [INFO] Executing action 318/643: iOS Function: text - Text: "<EMAIL>"
[[14:02:41]] [SUCCESS] Screenshot refreshed successfully
[[14:02:41]] [SUCCESS] Screenshot refreshed
[[14:02:41]] [INFO] Refreshing screenshot...
[[14:02:41]] [INFO] u928vFzSni=pass
[[14:02:37]] [INFO] u928vFzSni=running
[[14:02:37]] [INFO] Executing action 317/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:02:37]] [SUCCESS] Screenshot refreshed successfully
[[14:02:37]] [SUCCESS] Screenshot refreshed
[[14:02:37]] [INFO] Refreshing screenshot...
[[14:02:37]] [INFO] s0WyiD1w0B=pass
[[14:02:34]] [INFO] s0WyiD1w0B=running
[[14:02:34]] [INFO] Executing action 316/643: iOS Function: alert_accept
[[14:02:34]] [SUCCESS] Screenshot refreshed successfully
[[14:02:34]] [SUCCESS] Screenshot refreshed
[[14:02:34]] [INFO] Refreshing screenshot...
[[14:02:34]] [INFO] gekNSY5O2E=pass
[[14:02:30]] [INFO] gekNSY5O2E=running
[[14:02:30]] [INFO] Executing action 315/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[14:02:30]] [SUCCESS] Screenshot refreshed successfully
[[14:02:30]] [SUCCESS] Screenshot refreshed
[[14:02:30]] [INFO] Refreshing screenshot...
[[14:02:30]] [INFO] VJJ3EXXotU=pass
[[14:02:26]] [INFO] VJJ3EXXotU=running
[[14:02:26]] [INFO] Executing action 314/643: Tap on image: env[device-back-img]
[[14:02:26]] [SUCCESS] Screenshot refreshed successfully
[[14:02:25]] [SUCCESS] Screenshot refreshed
[[14:02:25]] [INFO] Refreshing screenshot...
[[14:02:25]] [INFO] 83tV9A4NOn=pass
[[14:02:23]] [INFO] 83tV9A4NOn=running
[[14:02:23]] [INFO] Executing action 313/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[14:02:22]] [SUCCESS] Screenshot refreshed successfully
[[14:02:22]] [SUCCESS] Screenshot refreshed
[[14:02:22]] [INFO] Refreshing screenshot...
[[14:02:22]] [INFO] aNN0yYFLEd=pass
[[14:02:18]] [INFO] aNN0yYFLEd=running
[[14:02:18]] [INFO] Executing action 312/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[14:02:18]] [SUCCESS] Screenshot refreshed successfully
[[14:02:18]] [SUCCESS] Screenshot refreshed
[[14:02:18]] [INFO] Refreshing screenshot...
[[14:02:18]] [INFO] XJv08Gkucs=pass
[[14:02:15]] [INFO] XJv08Gkucs=running
[[14:02:15]] [INFO] Executing action 311/643: Input text: "<EMAIL>"
[[14:02:14]] [SUCCESS] Screenshot refreshed successfully
[[14:02:14]] [SUCCESS] Screenshot refreshed
[[14:02:14]] [INFO] Refreshing screenshot...
[[14:02:14]] [INFO] kAQ1yIIw3h=pass
[[14:02:11]] [INFO] kAQ1yIIw3h=running
[[14:02:11]] [INFO] Executing action 310/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[14:02:10]] [SUCCESS] Screenshot refreshed successfully
[[14:02:10]] [SUCCESS] Screenshot refreshed
[[14:02:10]] [INFO] Refreshing screenshot...
[[14:02:10]] [INFO] 7YbjwQH1Jc=pass
[[14:02:07]] [INFO] 7YbjwQH1Jc=running
[[14:02:07]] [INFO] Executing action 309/643: Input text: "env[searchorder]"
[[14:02:07]] [SUCCESS] Screenshot refreshed successfully
[[14:02:07]] [SUCCESS] Screenshot refreshed
[[14:02:07]] [INFO] Refreshing screenshot...
[[14:02:07]] [INFO] OmKfD9iBjD=pass
[[14:02:03]] [INFO] OmKfD9iBjD=running
[[14:02:03]] [INFO] Executing action 308/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[14:02:03]] [SUCCESS] Screenshot refreshed successfully
[[14:02:03]] [SUCCESS] Screenshot refreshed
[[14:02:03]] [INFO] Refreshing screenshot...
[[14:02:03]] [INFO] eHLWiRoqqS=pass
[[14:01:59]] [INFO] eHLWiRoqqS=running
[[14:01:59]] [INFO] Executing action 307/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[14:01:59]] [SUCCESS] Screenshot refreshed successfully
[[14:01:58]] [SUCCESS] Screenshot refreshed
[[14:01:58]] [INFO] Refreshing screenshot...
[[14:01:58]] [INFO] F4NGh9HrLw=pass
[[14:01:55]] [INFO] F4NGh9HrLw=running
[[14:01:55]] [INFO] Executing action 306/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:01:54]] [SUCCESS] Screenshot refreshed successfully
[[14:01:54]] [SUCCESS] Screenshot refreshed
[[14:01:54]] [INFO] Refreshing screenshot...
[[14:01:54]] [INFO] 74XW7x54ad=pass
[[14:01:50]] [INFO] 74XW7x54ad=running
[[14:01:50]] [INFO] Executing action 305/643: Tap on image: env[device-back-img]
[[14:01:50]] [SUCCESS] Screenshot refreshed successfully
[[14:01:50]] [SUCCESS] Screenshot refreshed
[[14:01:50]] [INFO] Refreshing screenshot...
[[14:01:50]] [INFO] xUbWFa8Ok2=pass
[[14:01:47]] [INFO] xUbWFa8Ok2=running
[[14:01:47]] [INFO] Executing action 304/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[14:01:47]] [SUCCESS] Screenshot refreshed successfully
[[14:01:46]] [SUCCESS] Screenshot refreshed
[[14:01:46]] [INFO] Refreshing screenshot...
[[14:01:46]] [INFO] RbNtEW6N9T=pass
[[14:01:43]] [INFO] RbNtEW6N9T=running
[[14:01:43]] [INFO] Executing action 303/643: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[14:01:43]] [SUCCESS] Screenshot refreshed successfully
[[14:01:43]] [SUCCESS] Screenshot refreshed
[[14:01:43]] [INFO] Refreshing screenshot...
[[14:01:43]] [INFO] F4NGh9HrLw=pass
[[14:01:40]] [INFO] F4NGh9HrLw=running
[[14:01:40]] [INFO] Executing action 302/643: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[14:01:40]] [SUCCESS] Screenshot refreshed successfully
[[14:01:40]] [SUCCESS] Screenshot refreshed
[[14:01:40]] [INFO] Refreshing screenshot...
[[14:01:40]] [INFO] RlDZFks4Lc=pass
[[14:01:38]] [INFO] RlDZFks4Lc=running
[[14:01:38]] [INFO] Executing action 301/643: iOS Function: alert_accept
[[14:01:38]] [SUCCESS] Screenshot refreshed successfully
[[14:01:37]] [SUCCESS] Screenshot refreshed
[[14:01:37]] [INFO] Refreshing screenshot...
[[14:01:37]] [INFO] Dzn2Q7JTe0=pass
[[14:01:33]] [INFO] Dzn2Q7JTe0=running
[[14:01:33]] [INFO] Executing action 300/643: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[14:01:33]] [SUCCESS] Screenshot refreshed successfully
[[14:01:33]] [SUCCESS] Screenshot refreshed
[[14:01:33]] [INFO] Refreshing screenshot...
[[14:01:33]] [INFO] H9fy9qcFbZ=pass
[[14:01:21]] [SUCCESS] Screenshot refreshed successfully
[[14:01:20]] [INFO] H9fy9qcFbZ=running
[[14:01:20]] [INFO] Executing action 299/643: Restart app: env[appid]
[[14:01:20]] [SUCCESS] Screenshot refreshed
[[14:01:20]] [INFO] Refreshing screenshot...
[[14:01:20]] [SUCCESS] Screenshot refreshed successfully
[[14:01:20]] [SUCCESS] Screenshot refreshed
[[14:01:20]] [INFO] Refreshing screenshot...
[[14:01:17]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:01:17]] [SUCCESS] Screenshot refreshed successfully
[[14:01:17]] [SUCCESS] Screenshot refreshed
[[14:01:17]] [INFO] Refreshing screenshot...
[[14:01:04]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:01:04]] [SUCCESS] Screenshot refreshed successfully
[[14:01:04]] [SUCCESS] Screenshot refreshed
[[14:01:04]] [INFO] Refreshing screenshot...
[[14:01:00]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:01:00]] [SUCCESS] Screenshot refreshed successfully
[[14:01:00]] [SUCCESS] Screenshot refreshed
[[14:01:00]] [INFO] Refreshing screenshot...
[[14:00:56]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:00:56]] [SUCCESS] Screenshot refreshed successfully
[[14:00:56]] [SUCCESS] Screenshot refreshed
[[14:00:56]] [INFO] Refreshing screenshot...
[[14:00:49]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:00:49]] [SUCCESS] Screenshot refreshed successfully
[[14:00:49]] [SUCCESS] Screenshot refreshed
[[14:00:49]] [INFO] Refreshing screenshot...
[[14:00:43]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:00:43]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:00:43]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:00:43]] [INFO] AeQaElnzUN=running
[[14:00:43]] [INFO] Executing action 298/643: cleanupSteps action
[[14:00:43]] [SUCCESS] Screenshot refreshed successfully
[[14:00:43]] [SUCCESS] Screenshot refreshed
[[14:00:43]] [INFO] Refreshing screenshot...
[[14:00:43]] [INFO] BracBsfa3Y=pass
[[14:00:39]] [INFO] BracBsfa3Y=running
[[14:00:39]] [INFO] Executing action 297/643: Tap on Text: "out"
[[14:00:38]] [SUCCESS] Screenshot refreshed successfully
[[14:00:38]] [SUCCESS] Screenshot refreshed
[[14:00:38]] [INFO] Refreshing screenshot...
[[14:00:38]] [INFO] s6tWdQ5URW=pass
[[14:00:31]] [INFO] s6tWdQ5URW=running
[[14:00:31]] [INFO] Executing action 296/643: Swipe from (50%, 70%) to (50%, 30%)
[[14:00:31]] [SUCCESS] Screenshot refreshed successfully
[[14:00:31]] [SUCCESS] Screenshot refreshed
[[14:00:31]] [INFO] Refreshing screenshot...
[[14:00:31]] [INFO] wNGRrfUjpK=pass
[[14:00:27]] [INFO] wNGRrfUjpK=running
[[14:00:27]] [INFO] Executing action 295/643: Tap on image: env[device-back-img]
[[14:00:27]] [SUCCESS] Screenshot refreshed successfully
[[14:00:27]] [SUCCESS] Screenshot refreshed
[[14:00:27]] [INFO] Refreshing screenshot...
[[14:00:27]] [INFO] BracBsfa3Y=pass
[[14:00:23]] [INFO] BracBsfa3Y=running
[[14:00:23]] [INFO] Executing action 294/643: Tap on Text: "Customer"
[[14:00:22]] [SUCCESS] Screenshot refreshed successfully
[[14:00:22]] [SUCCESS] Screenshot refreshed
[[14:00:22]] [INFO] Refreshing screenshot...
[[14:00:22]] [INFO] H4WfwVU8YP=pass
[[14:00:18]] [INFO] H4WfwVU8YP=running
[[14:00:18]] [INFO] Executing action 293/643: Tap on image: banner-close-updated.png
[[14:00:18]] [SUCCESS] Screenshot refreshed successfully
[[14:00:18]] [SUCCESS] Screenshot refreshed
[[14:00:18]] [INFO] Refreshing screenshot...
[[14:00:18]] [INFO] ePyaYpttQA=pass
[[14:00:15]] [INFO] ePyaYpttQA=running
[[14:00:15]] [INFO] Executing action 292/643: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"I’m loving the new Kmart app")]" exists
[[14:00:14]] [SUCCESS] Screenshot refreshed successfully
[[14:00:14]] [SUCCESS] Screenshot refreshed
[[14:00:14]] [INFO] Refreshing screenshot...
[[14:00:14]] [INFO] BracBsfa3Y=pass
[[14:00:10]] [INFO] BracBsfa3Y=running
[[14:00:10]] [INFO] Executing action 291/643: Tap on Text: "Invite"
[[14:00:10]] [SUCCESS] Screenshot refreshed successfully
[[14:00:10]] [SUCCESS] Screenshot refreshed
[[14:00:10]] [INFO] Refreshing screenshot...
[[14:00:10]] [INFO] xVbCNStsOP=pass
[[14:00:06]] [INFO] xVbCNStsOP=running
[[14:00:06]] [INFO] Executing action 290/643: Tap on image: env[device-back-img]
[[14:00:06]] [SUCCESS] Screenshot refreshed successfully
[[14:00:06]] [SUCCESS] Screenshot refreshed
[[14:00:06]] [INFO] Refreshing screenshot...
[[14:00:06]] [INFO] 8kQkC2FGyZ=pass
[[14:00:03]] [INFO] 8kQkC2FGyZ=running
[[14:00:03]] [INFO] Executing action 289/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Melbourne Cbd"]" exists
[[14:00:02]] [SUCCESS] Screenshot refreshed successfully
[[14:00:02]] [SUCCESS] Screenshot refreshed
[[14:00:02]] [INFO] Refreshing screenshot...
[[14:00:02]] [INFO] PgjJCrKFYo=pass
[[13:59:58]] [INFO] PgjJCrKFYo=running
[[13:59:58]] [INFO] Executing action 288/643: Tap on Text: "VIC"
[[13:59:57]] [SUCCESS] Screenshot refreshed successfully
[[13:59:57]] [SUCCESS] Screenshot refreshed
[[13:59:57]] [INFO] Refreshing screenshot...
[[13:59:57]] [INFO] 3Si0csRNaw=pass
[[13:59:51]] [INFO] 3Si0csRNaw=running
[[13:59:51]] [INFO] Executing action 287/643: Tap and Type at (env[store-locator-x], env[store-locator-y]): "env[store-locator-postcode]"
[[13:59:50]] [SUCCESS] Screenshot refreshed successfully
[[13:59:50]] [SUCCESS] Screenshot refreshed
[[13:59:50]] [INFO] Refreshing screenshot...
[[13:59:50]] [INFO] BracBsfa3Y=pass
[[13:59:44]] [INFO] BracBsfa3Y=running
[[13:59:44]] [INFO] Executing action 286/643: Tap on Text: "Nearby"
[[13:59:43]] [SUCCESS] Screenshot refreshed successfully
[[13:59:43]] [SUCCESS] Screenshot refreshed
[[13:59:43]] [INFO] Refreshing screenshot...
[[13:59:43]] [INFO] BracBsfa3Y=pass
[[13:59:39]] [INFO] BracBsfa3Y=running
[[13:59:39]] [INFO] Executing action 285/643: Tap on Text: "locator"
[[13:59:39]] [SUCCESS] Screenshot refreshed successfully
[[13:59:39]] [SUCCESS] Screenshot refreshed
[[13:59:39]] [INFO] Refreshing screenshot...
[[13:59:39]] [INFO] s6tWdQ5URW=pass
[[13:59:32]] [INFO] s6tWdQ5URW=running
[[13:59:32]] [INFO] Executing action 284/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:59:32]] [SUCCESS] Screenshot refreshed successfully
[[13:59:32]] [SUCCESS] Screenshot refreshed
[[13:59:32]] [INFO] Refreshing screenshot...
[[13:59:32]] [INFO] 2M0KHOVecv=pass
[[13:59:28]] [INFO] 2M0KHOVecv=running
[[13:59:28]] [INFO] Executing action 283/643: Check if element with accessibility_id="txtMy Flybuys card" exists
[[13:59:28]] [SUCCESS] Screenshot refreshed successfully
[[13:59:28]] [SUCCESS] Screenshot refreshed
[[13:59:28]] [INFO] Refreshing screenshot...
[[13:59:28]] [INFO] LBgsj3oLcu=pass
[[13:59:24]] [INFO] LBgsj3oLcu=running
[[13:59:24]] [INFO] Executing action 282/643: Tap on image: env[device-back-img]
[[13:59:24]] [SUCCESS] Screenshot refreshed successfully
[[13:59:24]] [SUCCESS] Screenshot refreshed
[[13:59:24]] [INFO] Refreshing screenshot...
[[13:59:24]] [INFO] biRyWs3nSs=pass
[[13:59:18]] [INFO] biRyWs3nSs=running
[[13:59:18]] [INFO] Executing action 281/643: Tap on element with accessibility_id: btnSaveFlybuysCard
[[13:59:18]] [SUCCESS] Screenshot refreshed successfully
[[13:59:18]] [SUCCESS] Screenshot refreshed
[[13:59:18]] [INFO] Refreshing screenshot...
[[13:59:18]] [INFO] 8cFGh3GD68=pass
[[13:59:12]] [INFO] 8cFGh3GD68=running
[[13:59:12]] [INFO] Executing action 280/643: Tap on element with accessibility_id: Done
[[13:59:12]] [SUCCESS] Screenshot refreshed successfully
[[13:59:12]] [SUCCESS] Screenshot refreshed
[[13:59:12]] [INFO] Refreshing screenshot...
[[13:59:12]] [INFO] sLe0Wurhgm=pass
[[13:59:09]] [INFO] sLe0Wurhgm=running
[[13:59:09]] [INFO] Executing action 279/643: Input text: "2791234567890"
[[13:59:08]] [SUCCESS] Screenshot refreshed successfully
[[13:59:08]] [SUCCESS] Screenshot refreshed
[[13:59:08]] [INFO] Refreshing screenshot...
[[13:59:08]] [INFO] Ey86YRVRzU=pass
[[13:59:03]] [INFO] Ey86YRVRzU=running
[[13:59:03]] [INFO] Executing action 278/643: Tap on element with accessibility_id: Flybuys barcode number
[[13:59:02]] [SUCCESS] Screenshot refreshed successfully
[[13:59:02]] [SUCCESS] Screenshot refreshed
[[13:59:02]] [INFO] Refreshing screenshot...
[[13:59:02]] [INFO] Gxhf3XGc6e=pass
[[13:58:57]] [INFO] Gxhf3XGc6e=running
[[13:58:57]] [INFO] Executing action 277/643: Tap on element with accessibility_id: btnLinkFlyBuys
[[13:58:57]] [SUCCESS] Screenshot refreshed successfully
[[13:58:56]] [SUCCESS] Screenshot refreshed
[[13:58:56]] [INFO] Refreshing screenshot...
[[13:58:56]] [INFO] BracBsfa3Y=pass
[[13:58:52]] [INFO] BracBsfa3Y=running
[[13:58:52]] [INFO] Executing action 276/643: Tap on Text: "Flybuys"
[[13:58:52]] [SUCCESS] Screenshot refreshed successfully
[[13:58:52]] [SUCCESS] Screenshot refreshed
[[13:58:52]] [INFO] Refreshing screenshot...
[[13:58:52]] [INFO] Ds5GfNVb3x=pass
[[13:58:46]] [INFO] Ds5GfNVb3x=running
[[13:58:46]] [INFO] Executing action 275/643: Tap on element with accessibility_id: btnRemove
[[13:58:46]] [SUCCESS] Screenshot refreshed successfully
[[13:58:46]] [SUCCESS] Screenshot refreshed
[[13:58:46]] [INFO] Refreshing screenshot...
[[13:58:46]] [INFO] 3ZFgwFaiXp=pass
[[13:58:40]] [INFO] 3ZFgwFaiXp=running
[[13:58:40]] [INFO] Executing action 274/643: Tap on element with accessibility_id: Remove card
[[13:58:40]] [SUCCESS] Screenshot refreshed successfully
[[13:58:40]] [SUCCESS] Screenshot refreshed
[[13:58:40]] [INFO] Refreshing screenshot...
[[13:58:40]] [INFO] 40hnWPsQ9P=pass
[[13:58:34]] [INFO] 40hnWPsQ9P=running
[[13:58:34]] [INFO] Executing action 273/643: Tap on element with accessibility_id: btneditFlybuysCard
[[13:58:34]] [SUCCESS] Screenshot refreshed successfully
[[13:58:34]] [SUCCESS] Screenshot refreshed
[[13:58:34]] [INFO] Refreshing screenshot...
[[13:58:34]] [INFO] 40hnWPsQ9P=pass
[[13:58:29]] [INFO] 40hnWPsQ9P=running
[[13:58:29]] [INFO] Executing action 272/643: Wait till accessibility_id=btneditFlybuysCard
[[13:58:29]] [SUCCESS] Screenshot refreshed successfully
[[13:58:29]] [SUCCESS] Screenshot refreshed
[[13:58:29]] [INFO] Refreshing screenshot...
[[13:58:29]] [INFO] BracBsfa3Y=pass
[[13:58:25]] [INFO] BracBsfa3Y=running
[[13:58:25]] [INFO] Executing action 271/643: Tap on Text: "Flybuys"
[[13:58:25]] [SUCCESS] Screenshot refreshed successfully
[[13:58:24]] [SUCCESS] Screenshot refreshed
[[13:58:24]] [INFO] Refreshing screenshot...
[[13:58:24]] [INFO] MkTFxfzubv=pass
[[13:58:21]] [INFO] MkTFxfzubv=running
[[13:58:21]] [INFO] Executing action 270/643: Tap on image: env[device-back-img]
[[13:58:21]] [SUCCESS] Screenshot refreshed successfully
[[13:58:20]] [SUCCESS] Screenshot refreshed
[[13:58:20]] [INFO] Refreshing screenshot...
[[13:58:20]] [INFO] EO3cMmdUyM=pass
[[13:58:17]] [INFO] EO3cMmdUyM=running
[[13:58:17]] [INFO] Executing action 269/643: Tap on image: env[device-back-img]
[[13:58:17]] [SUCCESS] Screenshot refreshed successfully
[[13:58:16]] [SUCCESS] Screenshot refreshed
[[13:58:16]] [INFO] Refreshing screenshot...
[[13:58:16]] [INFO] napKDohf3Z=pass
[[13:58:12]] [INFO] napKDohf3Z=running
[[13:58:12]] [INFO] Executing action 268/643: Tap on Text: "payment"
[[13:58:12]] [SUCCESS] Screenshot refreshed successfully
[[13:58:12]] [SUCCESS] Screenshot refreshed
[[13:58:12]] [INFO] Refreshing screenshot...
[[13:58:12]] [INFO] ekqt95ZRol=pass
[[13:58:08]] [INFO] ekqt95ZRol=running
[[13:58:08]] [INFO] Executing action 267/643: Tap on image: env[device-back-img]
[[13:58:08]] [SUCCESS] Screenshot refreshed successfully
[[13:58:08]] [SUCCESS] Screenshot refreshed
[[13:58:08]] [INFO] Refreshing screenshot...
[[13:58:08]] [INFO] 20qUCJgpE9=pass
[[13:58:04]] [INFO] 20qUCJgpE9=running
[[13:58:04]] [INFO] Executing action 266/643: Tap on Text: "address"
[[13:58:04]] [SUCCESS] Screenshot refreshed successfully
[[13:58:03]] [SUCCESS] Screenshot refreshed
[[13:58:03]] [INFO] Refreshing screenshot...
[[13:58:03]] [INFO] 6HR2weiXoT=pass
[[13:58:00]] [INFO] 6HR2weiXoT=running
[[13:58:00]] [INFO] Executing action 265/643: Tap on image: env[device-back-img]
[[13:57:59]] [SUCCESS] Screenshot refreshed successfully
[[13:57:59]] [SUCCESS] Screenshot refreshed
[[13:57:59]] [INFO] Refreshing screenshot...
[[13:57:59]] [INFO] 3hOTINBVMf=pass
[[13:57:55]] [INFO] 3hOTINBVMf=running
[[13:57:55]] [INFO] Executing action 264/643: Tap on Text: "details"
[[13:57:55]] [SUCCESS] Screenshot refreshed successfully
[[13:57:55]] [SUCCESS] Screenshot refreshed
[[13:57:55]] [INFO] Refreshing screenshot...
[[13:57:55]] [INFO] yJi0WxnERj=pass
[[13:57:51]] [INFO] yJi0WxnERj=running
[[13:57:51]] [INFO] Executing action 263/643: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[13:57:51]] [SUCCESS] Screenshot refreshed successfully
[[13:57:51]] [SUCCESS] Screenshot refreshed
[[13:57:51]] [INFO] Refreshing screenshot...
[[13:57:51]] [INFO] PbfHAtFQPP=pass
[[13:57:47]] [INFO] PbfHAtFQPP=running
[[13:57:47]] [INFO] Executing action 262/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:57:46]] [SUCCESS] Screenshot refreshed successfully
[[13:57:46]] [SUCCESS] Screenshot refreshed
[[13:57:46]] [INFO] Refreshing screenshot...
[[13:57:46]] [INFO] 6qZnk86hGg=pass
[[13:57:42]] [INFO] 6qZnk86hGg=running
[[13:57:42]] [INFO] Executing action 261/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[13:57:42]] [SUCCESS] Screenshot refreshed successfully
[[13:57:42]] [SUCCESS] Screenshot refreshed
[[13:57:42]] [INFO] Refreshing screenshot...
[[13:57:42]] [INFO] FAvQgIuHc1=pass
[[13:57:38]] [INFO] FAvQgIuHc1=running
[[13:57:38]] [INFO] Executing action 260/643: Tap on Text: "Return"
[[13:57:37]] [SUCCESS] Screenshot refreshed successfully
[[13:57:37]] [SUCCESS] Screenshot refreshed
[[13:57:37]] [INFO] Refreshing screenshot...
[[13:57:37]] [INFO] vmc01sHkbr=pass
[[13:57:31]] [INFO] vmc01sHkbr=running
[[13:57:31]] [INFO] Executing action 259/643: Wait for 5 ms
[[13:57:31]] [SUCCESS] Screenshot refreshed successfully
[[13:57:31]] [SUCCESS] Screenshot refreshed
[[13:57:31]] [INFO] Refreshing screenshot...
[[13:57:31]] [INFO] zeu0wd1vcF=pass
[[13:57:18]] [INFO] zeu0wd1vcF=running
[[13:57:18]] [INFO] Executing action 258/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:57:18]] [SUCCESS] Screenshot refreshed successfully
[[13:57:18]] [SUCCESS] Screenshot refreshed
[[13:57:18]] [INFO] Refreshing screenshot...
[[13:57:18]] [INFO] OwWeZes4aT=pass
[[13:57:14]] [INFO] OwWeZes4aT=running
[[13:57:14]] [INFO] Executing action 257/643: Tap on image: env[device-back-img]
[[13:57:14]] [SUCCESS] Screenshot refreshed successfully
[[13:57:13]] [SUCCESS] Screenshot refreshed
[[13:57:13]] [INFO] Refreshing screenshot...
[[13:57:13]] [INFO] aAaTtUE92h=pass
[[13:57:11]] [INFO] aAaTtUE92h=running
[[13:57:11]] [INFO] Executing action 256/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[13:57:10]] [SUCCESS] Screenshot refreshed successfully
[[13:57:10]] [SUCCESS] Screenshot refreshed
[[13:57:10]] [INFO] Refreshing screenshot...
[[13:57:10]] [INFO] 9iOZGMqAZK=pass
[[13:57:06]] [INFO] 9iOZGMqAZK=running
[[13:57:06]] [INFO] Executing action 255/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[13:57:06]] [SUCCESS] Screenshot refreshed successfully
[[13:57:06]] [SUCCESS] Screenshot refreshed
[[13:57:06]] [INFO] Refreshing screenshot...
[[13:57:06]] [INFO] mRTYzOFRRw=pass
[[13:57:03]] [INFO] mRTYzOFRRw=running
[[13:57:03]] [INFO] Executing action 254/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[13:57:03]] [SUCCESS] Screenshot refreshed successfully
[[13:57:03]] [SUCCESS] Screenshot refreshed
[[13:57:03]] [INFO] Refreshing screenshot...
[[13:57:03]] [INFO] 7g6MFJSGIO=pass
[[13:56:59]] [INFO] 7g6MFJSGIO=running
[[13:56:59]] [INFO] Executing action 253/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[13:56:58]] [SUCCESS] Screenshot refreshed successfully
[[13:56:58]] [SUCCESS] Screenshot refreshed
[[13:56:58]] [INFO] Refreshing screenshot...
[[13:56:58]] [INFO] zNwyPagPE1=pass
[[13:56:52]] [INFO] zNwyPagPE1=running
[[13:56:52]] [INFO] Executing action 252/643: Wait for 5 ms
[[13:56:52]] [SUCCESS] Screenshot refreshed successfully
[[13:56:51]] [SUCCESS] Screenshot refreshed
[[13:56:51]] [INFO] Refreshing screenshot...
[[13:56:51]] [INFO] qXsL3wzg6J=pass
[[13:56:48]] [INFO] qXsL3wzg6J=running
[[13:56:48]] [INFO] Executing action 251/643: Tap on image: env[device-back-img]
[[13:56:48]] [SUCCESS] Screenshot refreshed successfully
[[13:56:47]] [SUCCESS] Screenshot refreshed
[[13:56:47]] [INFO] Refreshing screenshot...
[[13:56:47]] [INFO] YuuQe2KupX=pass
[[13:56:43]] [INFO] YuuQe2KupX=running
[[13:56:43]] [INFO] Executing action 250/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[13:56:43]] [SUCCESS] Screenshot refreshed successfully
[[13:56:43]] [SUCCESS] Screenshot refreshed
[[13:56:43]] [INFO] Refreshing screenshot...
[[13:56:43]] [INFO] g0PE7Mofye=pass
[[13:56:37]] [INFO] g0PE7Mofye=running
[[13:56:37]] [INFO] Executing action 249/643: Tap on element with accessibility_id: Print order details
[[13:56:37]] [SUCCESS] Screenshot refreshed successfully
[[13:56:36]] [SUCCESS] Screenshot refreshed
[[13:56:36]] [INFO] Refreshing screenshot...
[[13:56:36]] [INFO] GgQaBLWYkb=pass
[[13:56:33]] [INFO] GgQaBLWYkb=running
[[13:56:33]] [INFO] Executing action 248/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[13:56:32]] [SUCCESS] Screenshot refreshed successfully
[[13:56:32]] [SUCCESS] Screenshot refreshed
[[13:56:32]] [INFO] Refreshing screenshot...
[[13:56:32]] [INFO] f3OrHHzTFN=pass
[[13:56:16]] [INFO] f3OrHHzTFN=running
[[13:56:16]] [INFO] Executing action 247/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[13:56:16]] [SUCCESS] Screenshot refreshed successfully
[[13:56:16]] [SUCCESS] Screenshot refreshed
[[13:56:16]] [INFO] Refreshing screenshot...
[[13:56:16]] [INFO] 7g6MFJSGIO=pass
[[13:56:12]] [INFO] 7g6MFJSGIO=running
[[13:56:12]] [INFO] Executing action 246/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[13:56:12]] [SUCCESS] Screenshot refreshed successfully
[[13:56:12]] [SUCCESS] Screenshot refreshed
[[13:56:12]] [INFO] Refreshing screenshot...
[[13:56:12]] [INFO] Z6g3sGuHTp=pass
[[13:56:05]] [INFO] Z6g3sGuHTp=running
[[13:56:05]] [INFO] Executing action 245/643: Wait for 5 ms
[[13:56:05]] [SUCCESS] Screenshot refreshed successfully
[[13:56:05]] [SUCCESS] Screenshot refreshed
[[13:56:05]] [INFO] Refreshing screenshot...
[[13:56:05]] [INFO] pFlYwTS53v=pass
[[13:56:01]] [INFO] pFlYwTS53v=running
[[13:56:01]] [INFO] Executing action 244/643: Tap on Text: "receipts"
[[13:56:00]] [SUCCESS] Screenshot refreshed successfully
[[13:56:00]] [SUCCESS] Screenshot refreshed
[[13:56:00]] [INFO] Refreshing screenshot...
[[13:56:00]] [INFO] V59u3l1wkM=pass
[[13:55:57]] [INFO] V59u3l1wkM=running
[[13:55:57]] [INFO] Executing action 243/643: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[13:55:57]] [SUCCESS] Screenshot refreshed successfully
[[13:55:57]] [SUCCESS] Screenshot refreshed
[[13:55:57]] [INFO] Refreshing screenshot...
[[13:55:57]] [INFO] sl3Wk1gK8X=pass
[[13:55:52]] [SUCCESS] Screenshot refreshed successfully
[[13:55:51]] [INFO] sl3Wk1gK8X=running
[[13:55:51]] [INFO] Executing action 242/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:55:51]] [SUCCESS] Screenshot refreshed
[[13:55:51]] [INFO] Refreshing screenshot...
[[13:55:51]] [SUCCESS] Screenshot refreshed successfully
[[13:55:51]] [SUCCESS] Screenshot refreshed
[[13:55:51]] [INFO] Refreshing screenshot...
[[13:55:47]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[13:55:46]] [SUCCESS] Screenshot refreshed successfully
[[13:55:46]] [SUCCESS] Screenshot refreshed
[[13:55:46]] [INFO] Refreshing screenshot...
[[13:55:42]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[13:55:42]] [SUCCESS] Screenshot refreshed successfully
[[13:55:42]] [SUCCESS] Screenshot refreshed
[[13:55:42]] [INFO] Refreshing screenshot...
[[13:55:37]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[13:55:37]] [SUCCESS] Screenshot refreshed successfully
[[13:55:37]] [SUCCESS] Screenshot refreshed
[[13:55:37]] [INFO] Refreshing screenshot...
[[13:55:33]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[13:55:33]] [SUCCESS] Screenshot refreshed successfully
[[13:55:32]] [SUCCESS] Screenshot refreshed
[[13:55:32]] [INFO] Refreshing screenshot...
[[13:55:27]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:55:27]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[13:55:27]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[13:55:27]] [INFO] vjK6GqOF3r=running
[[13:55:27]] [INFO] Executing action 241/643: Execute Test Case: Kmart-Signin (8 steps)
[[13:55:27]] [SUCCESS] Screenshot refreshed successfully
[[13:55:26]] [SUCCESS] Screenshot refreshed
[[13:55:26]] [INFO] Refreshing screenshot...
[[13:55:26]] [INFO] ly2oT3zqmf=pass
[[13:55:24]] [INFO] ly2oT3zqmf=running
[[13:55:24]] [INFO] Executing action 240/643: iOS Function: alert_accept
[[13:55:24]] [SUCCESS] Screenshot refreshed successfully
[[13:55:23]] [SUCCESS] Screenshot refreshed
[[13:55:23]] [INFO] Refreshing screenshot...
[[13:55:23]] [INFO] xAPeBnVHrT=pass
[[13:55:16]] [INFO] xAPeBnVHrT=running
[[13:55:16]] [INFO] Executing action 239/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:55:16]] [SUCCESS] Screenshot refreshed successfully
[[13:55:16]] [SUCCESS] Screenshot refreshed
[[13:55:16]] [INFO] Refreshing screenshot...
[[13:55:16]] [INFO] u6bRYZZFAv=pass
[[13:55:10]] [INFO] u6bRYZZFAv=running
[[13:55:10]] [INFO] Executing action 238/643: Wait for 5 ms
[[13:55:09]] [SUCCESS] Screenshot refreshed successfully
[[13:55:09]] [SUCCESS] Screenshot refreshed
[[13:55:09]] [INFO] Refreshing screenshot...
[[13:55:09]] [INFO] pjFNt3w5Fr=pass
[[13:54:57]] [SUCCESS] Screenshot refreshed successfully
[[13:54:57]] [INFO] pjFNt3w5Fr=running
[[13:54:57]] [INFO] Executing action 237/643: Restart app: env[appid]
[[13:54:57]] [SUCCESS] Screenshot refreshed
[[13:54:57]] [INFO] Refreshing screenshot...
[[13:54:56]] [SUCCESS] Screenshot refreshed successfully
[[13:54:56]] [SUCCESS] Screenshot refreshed
[[13:54:56]] [INFO] Refreshing screenshot...
[[13:54:54]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[13:54:53]] [SUCCESS] Screenshot refreshed successfully
[[13:54:53]] [SUCCESS] Screenshot refreshed
[[13:54:53]] [INFO] Refreshing screenshot...
[[13:54:42]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[13:54:42]] [SUCCESS] Screenshot refreshed successfully
[[13:54:41]] [SUCCESS] Screenshot refreshed
[[13:54:41]] [INFO] Refreshing screenshot...
[[13:54:38]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[13:54:37]] [SUCCESS] Screenshot refreshed successfully
[[13:54:37]] [SUCCESS] Screenshot refreshed
[[13:54:37]] [INFO] Refreshing screenshot...
[[13:54:34]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:54:33]] [SUCCESS] Screenshot refreshed successfully
[[13:54:33]] [SUCCESS] Screenshot refreshed
[[13:54:33]] [INFO] Refreshing screenshot...
[[13:54:27]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[13:54:27]] [SUCCESS] Screenshot refreshed successfully
[[13:54:26]] [SUCCESS] Screenshot refreshed
[[13:54:26]] [INFO] Refreshing screenshot...
[[13:54:22]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[13:54:22]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[13:54:22]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[13:54:22]] [INFO] PGvsG6rpU4=running
[[13:54:22]] [INFO] Executing action 236/643: cleanupSteps action
[[13:54:21]] [SUCCESS] Screenshot refreshed successfully
[[13:54:21]] [SUCCESS] Screenshot refreshed
[[13:54:21]] [INFO] Refreshing screenshot...
[[13:54:21]] [INFO] LzGkAcsQyE=pass
[[13:54:19]] [INFO] LzGkAcsQyE=running
[[13:54:19]] [INFO] Executing action 235/643: Terminate app: env[appid]
[[13:54:18]] [SUCCESS] Screenshot refreshed successfully
[[13:54:18]] [SUCCESS] Screenshot refreshed
[[13:54:18]] [INFO] Refreshing screenshot...
[[13:54:18]] [INFO] Bdhe5AoUlM=pass
[[13:54:14]] [INFO] Bdhe5AoUlM=running
[[13:54:14]] [INFO] Executing action 234/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:54:14]] [SUCCESS] Screenshot refreshed successfully
[[13:54:14]] [SUCCESS] Screenshot refreshed
[[13:54:14]] [INFO] Refreshing screenshot...
[[13:54:14]] [INFO] FciJcOsMsB=pass
[[13:54:07]] [INFO] FciJcOsMsB=running
[[13:54:07]] [INFO] Executing action 233/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:54:07]] [SUCCESS] Screenshot refreshed successfully
[[13:54:07]] [SUCCESS] Screenshot refreshed
[[13:54:07]] [INFO] Refreshing screenshot...
[[13:54:07]] [INFO] FARWZvOj0x=pass
[[13:54:03]] [INFO] FARWZvOj0x=running
[[13:54:03]] [INFO] Executing action 232/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:54:03]] [SUCCESS] Screenshot refreshed successfully
[[13:54:02]] [SUCCESS] Screenshot refreshed
[[13:54:02]] [INFO] Refreshing screenshot...
[[13:54:02]] [INFO] bZCkx4U9Gk=pass
[[13:53:57]] [INFO] bZCkx4U9Gk=running
[[13:53:57]] [INFO] Executing action 231/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[13:53:56]] [SUCCESS] Screenshot refreshed successfully
[[13:53:56]] [SUCCESS] Screenshot refreshed
[[13:53:56]] [INFO] Refreshing screenshot...
[[13:53:56]] [INFO] vwFwkK6ydQ=pass
[[13:53:52]] [INFO] vwFwkK6ydQ=running
[[13:53:52]] [INFO] Executing action 230/643: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[13:53:51]] [SUCCESS] Screenshot refreshed successfully
[[13:53:51]] [SUCCESS] Screenshot refreshed
[[13:53:51]] [INFO] Refreshing screenshot...
[[13:53:51]] [INFO] xLGm9FefWE=pass
[[13:53:47]] [INFO] xLGm9FefWE=running
[[13:53:47]] [INFO] Executing action 229/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[13:53:47]] [SUCCESS] Screenshot refreshed successfully
[[13:53:47]] [SUCCESS] Screenshot refreshed
[[13:53:47]] [INFO] Refreshing screenshot...
[[13:53:47]] [INFO] UtVRXwa86e=pass
[[13:53:40]] [INFO] UtVRXwa86e=running
[[13:53:40]] [INFO] Executing action 228/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Sign in with Google"]" is visible
[[13:53:40]] [SUCCESS] Screenshot refreshed successfully
[[13:53:40]] [SUCCESS] Screenshot refreshed
[[13:53:40]] [INFO] Refreshing screenshot...
[[13:53:40]] [INFO] SDtskxyVpg=pass
[[13:53:36]] [INFO] SDtskxyVpg=running
[[13:53:36]] [INFO] Executing action 227/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:53:36]] [SUCCESS] Screenshot refreshed successfully
[[13:53:36]] [SUCCESS] Screenshot refreshed
[[13:53:36]] [INFO] Refreshing screenshot...
[[13:53:36]] [INFO] 6HhScBaqQp=pass
[[13:53:33]] [INFO] 6HhScBaqQp=running
[[13:53:33]] [INFO] Executing action 226/643: iOS Function: alert_accept
[[13:53:33]] [SUCCESS] Screenshot refreshed successfully
[[13:53:33]] [SUCCESS] Screenshot refreshed
[[13:53:33]] [INFO] Refreshing screenshot...
[[13:53:33]] [INFO] quzlwPw42x=pass
[[13:53:27]] [INFO] quzlwPw42x=running
[[13:53:27]] [INFO] Executing action 225/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:53:27]] [SUCCESS] Screenshot refreshed successfully
[[13:53:26]] [SUCCESS] Screenshot refreshed
[[13:53:26]] [INFO] Refreshing screenshot...
[[13:53:26]] [INFO] jQYHQIvQ8l=pass
[[13:53:23]] [INFO] jQYHQIvQ8l=running
[[13:53:23]] [INFO] Executing action 224/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[13:53:23]] [SUCCESS] Screenshot refreshed successfully
[[13:53:22]] [SUCCESS] Screenshot refreshed
[[13:53:22]] [INFO] Refreshing screenshot...
[[13:53:22]] [INFO] ts3qyFxyMf=pass
[[13:53:19]] [INFO] ts3qyFxyMf=running
[[13:53:19]] [INFO] Executing action 223/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:53:18]] [SUCCESS] Screenshot refreshed successfully
[[13:53:18]] [SUCCESS] Screenshot refreshed
[[13:53:18]] [INFO] Refreshing screenshot...
[[13:53:18]] [INFO] FciJcOsMsB=pass
[[13:53:11]] [INFO] FciJcOsMsB=running
[[13:53:11]] [INFO] Executing action 222/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:53:11]] [SUCCESS] Screenshot refreshed successfully
[[13:53:11]] [SUCCESS] Screenshot refreshed
[[13:53:11]] [INFO] Refreshing screenshot...
[[13:53:11]] [INFO] CWkqGp5ndO=pass
[[13:53:07]] [INFO] CWkqGp5ndO=running
[[13:53:07]] [INFO] Executing action 221/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:53:07]] [SUCCESS] Screenshot refreshed successfully
[[13:53:07]] [SUCCESS] Screenshot refreshed
[[13:53:07]] [INFO] Refreshing screenshot...
[[13:53:07]] [INFO] KfMHchi8cx=pass
[[13:52:58]] [INFO] KfMHchi8cx=running
[[13:52:58]] [INFO] Executing action 220/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[13:52:58]] [SUCCESS] Screenshot refreshed successfully
[[13:52:58]] [SUCCESS] Screenshot refreshed
[[13:52:58]] [INFO] Refreshing screenshot...
[[13:52:58]] [INFO] zsVeGHiIgX=pass
[[13:52:55]] [INFO] zsVeGHiIgX=running
[[13:52:55]] [INFO] Executing action 219/643: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[13:52:54]] [SUCCESS] Screenshot refreshed successfully
[[13:52:54]] [SUCCESS] Screenshot refreshed
[[13:52:54]] [INFO] Refreshing screenshot...
[[13:52:54]] [INFO] 5nsUXQ5L7u=pass
[[13:52:51]] [INFO] 5nsUXQ5L7u=running
[[13:52:51]] [INFO] Executing action 218/643: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[13:52:51]] [SUCCESS] Screenshot refreshed successfully
[[13:52:51]] [SUCCESS] Screenshot refreshed
[[13:52:51]] [INFO] Refreshing screenshot...
[[13:52:51]] [INFO] iSckENpXrN=pass
[[13:52:48]] [INFO] iSckENpXrN=running
[[13:52:48]] [INFO] Executing action 217/643: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[13:52:48]] [SUCCESS] Screenshot refreshed successfully
[[13:52:48]] [SUCCESS] Screenshot refreshed
[[13:52:48]] [INFO] Refreshing screenshot...
[[13:52:48]] [INFO] J7BPGVnRJI=pass
[[13:52:45]] [INFO] J7BPGVnRJI=running
[[13:52:45]] [INFO] Executing action 216/643: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[13:52:44]] [SUCCESS] Screenshot refreshed successfully
[[13:52:44]] [SUCCESS] Screenshot refreshed
[[13:52:44]] [INFO] Refreshing screenshot...
[[13:52:44]] [INFO] 0pwZCYAtOv=pass
[[13:52:41]] [INFO] 0pwZCYAtOv=running
[[13:52:41]] [INFO] Executing action 215/643: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[13:52:41]] [SUCCESS] Screenshot refreshed successfully
[[13:52:41]] [SUCCESS] Screenshot refreshed
[[13:52:41]] [INFO] Refreshing screenshot...
[[13:52:41]] [INFO] soKM0KayFJ=pass
[[13:52:38]] [INFO] soKM0KayFJ=running
[[13:52:38]] [INFO] Executing action 214/643: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[13:52:38]] [SUCCESS] Screenshot refreshed successfully
[[13:52:38]] [SUCCESS] Screenshot refreshed
[[13:52:38]] [INFO] Refreshing screenshot...
[[13:52:38]] [INFO] hnH3ayslCh=pass
[[13:52:35]] [INFO] hnH3ayslCh=running
[[13:52:35]] [INFO] Executing action 213/643: Tap on Text: "Passcode"
[[13:52:34]] [SUCCESS] Screenshot refreshed successfully
[[13:52:34]] [SUCCESS] Screenshot refreshed
[[13:52:34]] [INFO] Refreshing screenshot...
[[13:52:34]] [INFO] CzVeOTdAX9=pass
[[13:52:23]] [INFO] CzVeOTdAX9=running
[[13:52:23]] [INFO] Executing action 212/643: Wait for 10 ms
[[13:52:22]] [SUCCESS] Screenshot refreshed successfully
[[13:52:22]] [SUCCESS] Screenshot refreshed
[[13:52:22]] [INFO] Refreshing screenshot...
[[13:52:22]] [INFO] NL2gtj6qIu=pass
[[13:52:18]] [INFO] NL2gtj6qIu=running
[[13:52:18]] [INFO] Executing action 211/643: Tap on Text: "Apple"
[[13:52:17]] [SUCCESS] Screenshot refreshed successfully
[[13:52:17]] [SUCCESS] Screenshot refreshed
[[13:52:17]] [INFO] Refreshing screenshot...
[[13:52:17]] [INFO] VsSlyhXuVD=pass
[[13:52:13]] [INFO] VsSlyhXuVD=running
[[13:52:13]] [INFO] Executing action 210/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:52:13]] [SUCCESS] Screenshot refreshed successfully
[[13:52:12]] [SUCCESS] Screenshot refreshed
[[13:52:12]] [INFO] Refreshing screenshot...
[[13:52:12]] [INFO] CJ88OgjKXp=pass
[[13:52:09]] [INFO] CJ88OgjKXp=running
[[13:52:09]] [INFO] Executing action 209/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:52:09]] [SUCCESS] Screenshot refreshed successfully
[[13:52:08]] [SUCCESS] Screenshot refreshed
[[13:52:08]] [INFO] Refreshing screenshot...
[[13:52:08]] [INFO] AYiwFSLTBD=pass
[[13:52:06]] [INFO] AYiwFSLTBD=running
[[13:52:06]] [INFO] Executing action 208/643: iOS Function: alert_accept
[[13:52:06]] [SUCCESS] Screenshot refreshed successfully
[[13:52:06]] [SUCCESS] Screenshot refreshed
[[13:52:06]] [INFO] Refreshing screenshot...
[[13:52:06]] [INFO] HJzOYZNnGr=pass
[[13:52:00]] [INFO] HJzOYZNnGr=running
[[13:52:00]] [INFO] Executing action 207/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:51:59]] [SUCCESS] Screenshot refreshed successfully
[[13:51:59]] [SUCCESS] Screenshot refreshed
[[13:51:59]] [INFO] Refreshing screenshot...
[[13:51:59]] [INFO] taf19mtrUT=pass
[[13:51:56]] [INFO] taf19mtrUT=running
[[13:51:56]] [INFO] Executing action 206/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[13:51:55]] [SUCCESS] Screenshot refreshed successfully
[[13:51:55]] [SUCCESS] Screenshot refreshed
[[13:51:55]] [INFO] Refreshing screenshot...
[[13:51:55]] [INFO] oiPcknTonJ=pass
[[13:51:51]] [INFO] oiPcknTonJ=running
[[13:51:51]] [INFO] Executing action 205/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:51:51]] [SUCCESS] Screenshot refreshed successfully
[[13:51:51]] [SUCCESS] Screenshot refreshed
[[13:51:51]] [INFO] Refreshing screenshot...
[[13:51:51]] [INFO] FciJcOsMsB=pass
[[13:51:45]] [INFO] FciJcOsMsB=running
[[13:51:45]] [INFO] Executing action 204/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:51:45]] [SUCCESS] Screenshot refreshed successfully
[[13:51:45]] [SUCCESS] Screenshot refreshed
[[13:51:45]] [INFO] Refreshing screenshot...
[[13:51:45]] [INFO] 2qOXZcEmK8=pass
[[13:51:41]] [INFO] 2qOXZcEmK8=running
[[13:51:41]] [INFO] Executing action 203/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:51:41]] [SUCCESS] Screenshot refreshed successfully
[[13:51:41]] [SUCCESS] Screenshot refreshed
[[13:51:41]] [INFO] Refreshing screenshot...
[[13:51:41]] [INFO] M6HdLxu76S=pass
[[13:51:36]] [INFO] M6HdLxu76S=running
[[13:51:36]] [INFO] Executing action 202/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[13:51:36]] [SUCCESS] Screenshot refreshed successfully
[[13:51:36]] [SUCCESS] Screenshot refreshed
[[13:51:36]] [INFO] Refreshing screenshot...
[[13:51:36]] [INFO] 6wYIn0igez=pass
[[13:51:31]] [INFO] 6wYIn0igez=running
[[13:51:31]] [INFO] Executing action 201/643: iOS Function: text - Text: "Wonderbaby@6"
[[13:51:31]] [SUCCESS] Screenshot refreshed successfully
[[13:51:31]] [SUCCESS] Screenshot refreshed
[[13:51:31]] [INFO] Refreshing screenshot...
[[13:51:31]] [INFO] DaVBARRwft=pass
[[13:51:27]] [INFO] DaVBARRwft=running
[[13:51:27]] [INFO] Executing action 200/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[13:51:27]] [SUCCESS] Screenshot refreshed successfully
[[13:51:26]] [SUCCESS] Screenshot refreshed
[[13:51:26]] [INFO] Refreshing screenshot...
[[13:51:26]] [INFO] 2fmkjdQPtJ=pass
[[13:51:23]] [INFO] 2fmkjdQPtJ=running
[[13:51:23]] [INFO] Executing action 199/643: Tap on image: captha-chkbox-op-ios.png
[[13:51:22]] [SUCCESS] Screenshot refreshed successfully
[[13:51:22]] [SUCCESS] Screenshot refreshed
[[13:51:22]] [INFO] Refreshing screenshot...
[[13:51:22]] [INFO] Gb2Do6AtSN=pass
[[13:51:18]] [INFO] Gb2Do6AtSN=running
[[13:51:18]] [INFO] Executing action 198/643: iOS Function: text - Text: "<EMAIL>"
[[13:51:17]] [SUCCESS] Screenshot refreshed successfully
[[13:51:17]] [SUCCESS] Screenshot refreshed
[[13:51:17]] [INFO] Refreshing screenshot...
[[13:51:17]] [INFO] y8ZMTkG38M=pass
[[13:51:13]] [INFO] y8ZMTkG38M=running
[[13:51:13]] [INFO] Executing action 197/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[13:51:13]] [SUCCESS] Screenshot refreshed successfully
[[13:51:12]] [SUCCESS] Screenshot refreshed
[[13:51:12]] [INFO] Refreshing screenshot...
[[13:51:12]] [INFO] UUhQjmzfO2=pass
[[13:51:08]] [INFO] UUhQjmzfO2=running
[[13:51:08]] [INFO] Executing action 196/643: Tap on Text: "OnePass"
[[13:51:08]] [SUCCESS] Screenshot refreshed successfully
[[13:51:08]] [SUCCESS] Screenshot refreshed
[[13:51:08]] [INFO] Refreshing screenshot...
[[13:51:08]] [INFO] NCyuT8W5Xz=pass
[[13:51:04]] [INFO] NCyuT8W5Xz=running
[[13:51:04]] [INFO] Executing action 195/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:51:04]] [SUCCESS] Screenshot refreshed successfully
[[13:51:03]] [SUCCESS] Screenshot refreshed
[[13:51:03]] [INFO] Refreshing screenshot...
[[13:51:03]] [INFO] 2kwu2VBmuZ=pass
[[13:51:01]] [INFO] 2kwu2VBmuZ=running
[[13:51:01]] [INFO] Executing action 194/643: iOS Function: alert_accept
[[13:51:01]] [SUCCESS] Screenshot refreshed successfully
[[13:51:01]] [SUCCESS] Screenshot refreshed
[[13:51:01]] [INFO] Refreshing screenshot...
[[13:51:01]] [INFO] cJDpd7aK3d=pass
[[13:50:55]] [INFO] cJDpd7aK3d=running
[[13:50:55]] [INFO] Executing action 193/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:50:55]] [SUCCESS] Screenshot refreshed successfully
[[13:50:54]] [SUCCESS] Screenshot refreshed
[[13:50:54]] [INFO] Refreshing screenshot...
[[13:50:54]] [INFO] FlEukNkjlS=pass
[[13:50:51]] [INFO] FlEukNkjlS=running
[[13:50:51]] [INFO] Executing action 192/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[13:50:51]] [SUCCESS] Screenshot refreshed successfully
[[13:50:50]] [SUCCESS] Screenshot refreshed
[[13:50:50]] [INFO] Refreshing screenshot...
[[13:50:50]] [INFO] LlRfimKPrn=pass
[[13:50:47]] [INFO] LlRfimKPrn=running
[[13:50:47]] [INFO] Executing action 191/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:50:46]] [SUCCESS] Screenshot refreshed successfully
[[13:50:46]] [SUCCESS] Screenshot refreshed
[[13:50:46]] [INFO] Refreshing screenshot...
[[13:50:46]] [INFO] FciJcOsMsB=pass
[[13:50:39]] [INFO] FciJcOsMsB=running
[[13:50:39]] [INFO] Executing action 190/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:50:39]] [SUCCESS] Screenshot refreshed successfully
[[13:50:39]] [SUCCESS] Screenshot refreshed
[[13:50:39]] [INFO] Refreshing screenshot...
[[13:50:39]] [INFO] 08NzsvhQXK=pass
[[13:50:35]] [INFO] 08NzsvhQXK=running
[[13:50:35]] [INFO] Executing action 189/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:50:35]] [SUCCESS] Screenshot refreshed successfully
[[13:50:35]] [SUCCESS] Screenshot refreshed
[[13:50:35]] [INFO] Refreshing screenshot...
[[13:50:35]] [INFO] IsGWxLFpIn=pass
[[13:50:32]] [INFO] IsGWxLFpIn=running
[[13:50:32]] [INFO] Executing action 188/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[13:50:32]] [SUCCESS] Screenshot refreshed successfully
[[13:50:31]] [SUCCESS] Screenshot refreshed
[[13:50:31]] [INFO] Refreshing screenshot...
[[13:50:31]] [INFO] dyECdbRifp=pass
[[13:50:27]] [INFO] dyECdbRifp=running
[[13:50:27]] [INFO] Executing action 187/643: iOS Function: text - Text: "Wonderbaby@5"
[[13:50:27]] [SUCCESS] Screenshot refreshed successfully
[[13:50:26]] [SUCCESS] Screenshot refreshed
[[13:50:26]] [INFO] Refreshing screenshot...
[[13:50:26]] [INFO] I5bRbYY1hD=pass
[[13:50:23]] [INFO] I5bRbYY1hD=running
[[13:50:23]] [INFO] Executing action 186/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[13:50:22]] [SUCCESS] Screenshot refreshed successfully
[[13:50:22]] [SUCCESS] Screenshot refreshed
[[13:50:22]] [INFO] Refreshing screenshot...
[[13:50:22]] [INFO] WMl5g82CCq=pass
[[13:50:18]] [INFO] WMl5g82CCq=running
[[13:50:18]] [INFO] Executing action 185/643: iOS Function: text - Text: "<EMAIL>"
[[13:50:17]] [SUCCESS] Screenshot refreshed successfully
[[13:50:17]] [SUCCESS] Screenshot refreshed
[[13:50:17]] [INFO] Refreshing screenshot...
[[13:50:17]] [INFO] 8OsQmoVYqW=pass
[[13:50:13]] [INFO] 8OsQmoVYqW=running
[[13:50:13]] [INFO] Executing action 184/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[13:50:13]] [SUCCESS] Screenshot refreshed successfully
[[13:50:13]] [SUCCESS] Screenshot refreshed
[[13:50:13]] [INFO] Refreshing screenshot...
[[13:50:13]] [INFO] ImienLpJEN=pass
[[13:50:09]] [INFO] ImienLpJEN=running
[[13:50:09]] [INFO] Executing action 183/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:50:09]] [SUCCESS] Screenshot refreshed successfully
[[13:50:09]] [SUCCESS] Screenshot refreshed
[[13:50:09]] [INFO] Refreshing screenshot...
[[13:50:09]] [INFO] q4hPXCBtx4=pass
[[13:50:06]] [INFO] q4hPXCBtx4=running
[[13:50:06]] [INFO] Executing action 182/643: iOS Function: alert_accept
[[13:50:06]] [SUCCESS] Screenshot refreshed successfully
[[13:50:06]] [SUCCESS] Screenshot refreshed
[[13:50:06]] [INFO] Refreshing screenshot...
[[13:50:06]] [INFO] 2cTZvK1psn=pass
[[13:49:59]] [INFO] 2cTZvK1psn=running
[[13:49:59]] [INFO] Executing action 181/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:49:59]] [SUCCESS] Screenshot refreshed successfully
[[13:49:58]] [SUCCESS] Screenshot refreshed
[[13:49:58]] [INFO] Refreshing screenshot...
[[13:49:58]] [INFO] Vxt7QOYeDD=pass
[[13:49:55]] [INFO] Vxt7QOYeDD=running
[[13:49:55]] [INFO] Executing action 180/643: Restart app: env[appid]
[[13:49:53]] [INFO] === RETRYING TEST CASE: KmartProdSignin_20250426221008.json (Attempt 2 of 3) ===
[[13:49:53]] [INFO] M6HdLxu76S=fail
[[13:49:53]] [ERROR] Action 202 failed: Element not found: xpath='//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]'
[[13:49:36]] [INFO] M6HdLxu76S=running
[[13:49:36]] [INFO] Executing action 202/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[13:49:36]] [SUCCESS] Screenshot refreshed successfully
[[13:49:36]] [SUCCESS] Screenshot refreshed
[[13:49:36]] [INFO] Refreshing screenshot...
[[13:49:36]] [INFO] 6wYIn0igez=pass
[[13:49:31]] [INFO] 6wYIn0igez=running
[[13:49:31]] [INFO] Executing action 201/643: iOS Function: text - Text: "Wonderbaby@6"
[[13:49:31]] [SUCCESS] Screenshot refreshed successfully
[[13:49:31]] [SUCCESS] Screenshot refreshed
[[13:49:31]] [INFO] Refreshing screenshot...
[[13:49:31]] [INFO] DaVBARRwft=pass
[[13:49:27]] [INFO] DaVBARRwft=running
[[13:49:27]] [INFO] Executing action 200/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[13:49:26]] [SUCCESS] Screenshot refreshed successfully
[[13:49:26]] [SUCCESS] Screenshot refreshed
[[13:49:26]] [INFO] Refreshing screenshot...
[[13:49:26]] [INFO] 2fmkjdQPtJ=pass
[[13:49:22]] [INFO] 2fmkjdQPtJ=running
[[13:49:22]] [INFO] Executing action 199/643: Tap on image: captha-chkbox-op-ios.png
[[13:49:22]] [SUCCESS] Screenshot refreshed successfully
[[13:49:22]] [SUCCESS] Screenshot refreshed
[[13:49:22]] [INFO] Refreshing screenshot...
[[13:49:22]] [INFO] Gb2Do6AtSN=pass
[[13:49:17]] [INFO] Gb2Do6AtSN=running
[[13:49:17]] [INFO] Executing action 198/643: iOS Function: text - Text: "<EMAIL>"
[[13:49:17]] [SUCCESS] Screenshot refreshed successfully
[[13:49:16]] [SUCCESS] Screenshot refreshed
[[13:49:16]] [INFO] Refreshing screenshot...
[[13:49:16]] [INFO] y8ZMTkG38M=pass
[[13:49:13]] [INFO] y8ZMTkG38M=running
[[13:49:13]] [INFO] Executing action 197/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[13:49:12]] [SUCCESS] Screenshot refreshed successfully
[[13:49:12]] [SUCCESS] Screenshot refreshed
[[13:49:12]] [INFO] Refreshing screenshot...
[[13:49:12]] [INFO] UUhQjmzfO2=pass
[[13:49:08]] [INFO] UUhQjmzfO2=running
[[13:49:08]] [INFO] Executing action 196/643: Tap on Text: "OnePass"
[[13:49:07]] [SUCCESS] Screenshot refreshed successfully
[[13:49:07]] [SUCCESS] Screenshot refreshed
[[13:49:07]] [INFO] Refreshing screenshot...
[[13:49:07]] [INFO] NCyuT8W5Xz=pass
[[13:49:04]] [INFO] NCyuT8W5Xz=running
[[13:49:04]] [INFO] Executing action 195/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:49:04]] [SUCCESS] Screenshot refreshed successfully
[[13:49:03]] [SUCCESS] Screenshot refreshed
[[13:49:03]] [INFO] Refreshing screenshot...
[[13:49:03]] [INFO] 2kwu2VBmuZ=pass
[[13:49:01]] [INFO] 2kwu2VBmuZ=running
[[13:49:01]] [INFO] Executing action 194/643: iOS Function: alert_accept
[[13:49:01]] [SUCCESS] Screenshot refreshed successfully
[[13:49:00]] [SUCCESS] Screenshot refreshed
[[13:49:00]] [INFO] Refreshing screenshot...
[[13:49:00]] [INFO] cJDpd7aK3d=pass
[[13:48:55]] [INFO] cJDpd7aK3d=running
[[13:48:55]] [INFO] Executing action 193/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:48:54]] [SUCCESS] Screenshot refreshed successfully
[[13:48:54]] [SUCCESS] Screenshot refreshed
[[13:48:54]] [INFO] Refreshing screenshot...
[[13:48:54]] [INFO] FlEukNkjlS=pass
[[13:48:51]] [INFO] FlEukNkjlS=running
[[13:48:51]] [INFO] Executing action 192/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[13:48:51]] [SUCCESS] Screenshot refreshed successfully
[[13:48:50]] [SUCCESS] Screenshot refreshed
[[13:48:50]] [INFO] Refreshing screenshot...
[[13:48:50]] [INFO] LlRfimKPrn=pass
[[13:48:46]] [INFO] LlRfimKPrn=running
[[13:48:46]] [INFO] Executing action 191/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:48:46]] [SUCCESS] Screenshot refreshed successfully
[[13:48:46]] [SUCCESS] Screenshot refreshed
[[13:48:46]] [INFO] Refreshing screenshot...
[[13:48:46]] [INFO] FciJcOsMsB=pass
[[13:48:39]] [INFO] FciJcOsMsB=running
[[13:48:39]] [INFO] Executing action 190/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:48:39]] [SUCCESS] Screenshot refreshed successfully
[[13:48:39]] [SUCCESS] Screenshot refreshed
[[13:48:39]] [INFO] Refreshing screenshot...
[[13:48:39]] [INFO] 08NzsvhQXK=pass
[[13:48:35]] [INFO] 08NzsvhQXK=running
[[13:48:35]] [INFO] Executing action 189/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:48:35]] [SUCCESS] Screenshot refreshed successfully
[[13:48:34]] [SUCCESS] Screenshot refreshed
[[13:48:34]] [INFO] Refreshing screenshot...
[[13:48:34]] [INFO] IsGWxLFpIn=pass
[[13:48:30]] [INFO] IsGWxLFpIn=running
[[13:48:30]] [INFO] Executing action 188/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[13:48:30]] [SUCCESS] Screenshot refreshed successfully
[[13:48:30]] [SUCCESS] Screenshot refreshed
[[13:48:30]] [INFO] Refreshing screenshot...
[[13:48:30]] [INFO] dyECdbRifp=pass
[[13:48:25]] [INFO] dyECdbRifp=running
[[13:48:25]] [INFO] Executing action 187/643: iOS Function: text - Text: "Wonderbaby@5"
[[13:48:25]] [SUCCESS] Screenshot refreshed successfully
[[13:48:25]] [SUCCESS] Screenshot refreshed
[[13:48:25]] [INFO] Refreshing screenshot...
[[13:48:25]] [INFO] I5bRbYY1hD=pass
[[13:48:21]] [INFO] I5bRbYY1hD=running
[[13:48:21]] [INFO] Executing action 186/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[13:48:21]] [SUCCESS] Screenshot refreshed successfully
[[13:48:20]] [SUCCESS] Screenshot refreshed
[[13:48:20]] [INFO] Refreshing screenshot...
[[13:48:20]] [INFO] WMl5g82CCq=pass
[[13:48:16]] [INFO] WMl5g82CCq=running
[[13:48:16]] [INFO] Executing action 185/643: iOS Function: text - Text: "<EMAIL>"
[[13:48:16]] [SUCCESS] Screenshot refreshed successfully
[[13:48:15]] [SUCCESS] Screenshot refreshed
[[13:48:15]] [INFO] Refreshing screenshot...
[[13:48:15]] [INFO] 8OsQmoVYqW=pass
[[13:48:12]] [INFO] 8OsQmoVYqW=running
[[13:48:12]] [INFO] Executing action 184/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[13:48:11]] [SUCCESS] Screenshot refreshed successfully
[[13:48:11]] [SUCCESS] Screenshot refreshed
[[13:48:11]] [INFO] Refreshing screenshot...
[[13:48:11]] [INFO] ImienLpJEN=pass
[[13:48:08]] [INFO] ImienLpJEN=running
[[13:48:08]] [INFO] Executing action 183/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:48:07]] [SUCCESS] Screenshot refreshed successfully
[[13:48:07]] [SUCCESS] Screenshot refreshed
[[13:48:07]] [INFO] Refreshing screenshot...
[[13:48:07]] [INFO] q4hPXCBtx4=pass
[[13:48:05]] [INFO] q4hPXCBtx4=running
[[13:48:05]] [INFO] Executing action 182/643: iOS Function: alert_accept
[[13:48:04]] [SUCCESS] Screenshot refreshed successfully
[[13:48:04]] [SUCCESS] Screenshot refreshed
[[13:48:04]] [INFO] Refreshing screenshot...
[[13:48:04]] [INFO] 2cTZvK1psn=pass
[[13:47:57]] [INFO] 2cTZvK1psn=running
[[13:47:57]] [INFO] Executing action 181/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:47:57]] [SUCCESS] Screenshot refreshed successfully
[[13:47:57]] [SUCCESS] Screenshot refreshed
[[13:47:57]] [INFO] Refreshing screenshot...
[[13:47:57]] [INFO] Vxt7QOYeDD=pass
[[13:47:45]] [SUCCESS] Screenshot refreshed successfully
[[13:47:44]] [INFO] Vxt7QOYeDD=running
[[13:47:44]] [INFO] Executing action 180/643: Restart app: env[appid]
[[13:47:44]] [SUCCESS] Screenshot refreshed
[[13:47:44]] [INFO] Refreshing screenshot...
[[13:47:44]] [SUCCESS] Screenshot refreshed successfully
[[13:47:44]] [SUCCESS] Screenshot refreshed
[[13:47:44]] [INFO] Refreshing screenshot...
[[13:47:42]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[13:47:41]] [SUCCESS] Screenshot refreshed successfully
[[13:47:41]] [SUCCESS] Screenshot refreshed
[[13:47:41]] [INFO] Refreshing screenshot...
[[13:47:36]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[13:47:36]] [SUCCESS] Screenshot refreshed successfully
[[13:47:36]] [SUCCESS] Screenshot refreshed
[[13:47:36]] [INFO] Refreshing screenshot...
[[13:47:32]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[13:47:32]] [SUCCESS] Screenshot refreshed successfully
[[13:47:32]] [SUCCESS] Screenshot refreshed
[[13:47:32]] [INFO] Refreshing screenshot...
[[13:47:28]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:47:28]] [SUCCESS] Screenshot refreshed successfully
[[13:47:28]] [SUCCESS] Screenshot refreshed
[[13:47:28]] [INFO] Refreshing screenshot...
[[13:47:21]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[13:47:21]] [SUCCESS] Screenshot refreshed successfully
[[13:47:21]] [SUCCESS] Screenshot refreshed
[[13:47:21]] [INFO] Refreshing screenshot...
[[13:47:15]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[13:47:15]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[13:47:15]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[13:47:15]] [INFO] DYWpUY7xB6=running
[[13:47:15]] [INFO] Executing action 179/643: cleanupSteps action
[[13:47:15]] [INFO] Skipping remaining steps in failed test case (moving from action 131 to 178), but preserving cleanup steps
[[13:47:15]] [INFO] rkL0oz4kiL=fail
[[13:47:15]] [ERROR] Action 131 failed: Element not found or not tappable: accessibility_id='txtHomeAccountCtaSignIn'
[[13:47:02]] [INFO] rkL0oz4kiL=running
[[13:47:02]] [INFO] Executing action 131/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:47:01]] [SUCCESS] Screenshot refreshed successfully
[[13:47:01]] [SUCCESS] Screenshot refreshed
[[13:47:01]] [INFO] Refreshing screenshot...
[[13:47:01]] [INFO] HotUJOd6oB=pass
[[13:46:58]] [INFO] HotUJOd6oB=running
[[13:46:58]] [INFO] Executing action 130/643: Restart app: env[appid]
[[13:46:56]] [INFO] === RETRYING TEST CASE: WishList_20250510110236.json (Attempt 3 of 3) ===
[[13:46:56]] [INFO] rkL0oz4kiL=fail
[[13:46:56]] [ERROR] Action 131 failed: Element not found or not tappable: accessibility_id='txtHomeAccountCtaSignIn'
[[13:46:42]] [INFO] rkL0oz4kiL=running
[[13:46:42]] [INFO] Executing action 131/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:46:42]] [SUCCESS] Screenshot refreshed successfully
[[13:46:41]] [SUCCESS] Screenshot refreshed
[[13:46:41]] [INFO] Refreshing screenshot...
[[13:46:41]] [INFO] HotUJOd6oB=pass
[[13:46:38]] [INFO] HotUJOd6oB=running
[[13:46:38]] [INFO] Executing action 130/643: Restart app: env[appid]
[[13:46:36]] [INFO] === RETRYING TEST CASE: WishList_20250510110236.json (Attempt 2 of 3) ===
[[13:46:36]] [INFO] H3IAmq3r3i=fail
[[13:46:36]] [ERROR] Action 144 failed: Element not visible after 3 swipe(s)
[[13:46:22]] [INFO] H3IAmq3r3i=running
[[13:46:22]] [INFO] Executing action 144/643: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[13:46:22]] [SUCCESS] Screenshot refreshed successfully
[[13:46:22]] [SUCCESS] Screenshot refreshed
[[13:46:22]] [INFO] Refreshing screenshot...
[[13:46:22]] [INFO] ITHvSyXXmu=pass
[[13:46:18]] [INFO] ITHvSyXXmu=running
[[13:46:18]] [INFO] Executing action 143/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[13:46:18]] [SUCCESS] Screenshot refreshed successfully
[[13:46:17]] [SUCCESS] Screenshot refreshed
[[13:46:17]] [INFO] Refreshing screenshot...
[[13:46:17]] [INFO] eLxHVWKeDQ=pass
[[13:46:13]] [INFO] eLxHVWKeDQ=running
[[13:46:13]] [INFO] Executing action 142/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[13:46:13]] [SUCCESS] Screenshot refreshed successfully
[[13:46:13]] [SUCCESS] Screenshot refreshed
[[13:46:13]] [INFO] Refreshing screenshot...
[[13:46:13]] [INFO] nAB6Q8LAdv=pass
[[13:46:09]] [INFO] nAB6Q8LAdv=running
[[13:46:09]] [INFO] Executing action 141/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[13:46:09]] [SUCCESS] Screenshot refreshed successfully
[[13:46:09]] [SUCCESS] Screenshot refreshed
[[13:46:09]] [INFO] Refreshing screenshot...
[[13:46:09]] [INFO] sc2KH9bG6H=pass
[[13:46:05]] [INFO] sc2KH9bG6H=running
[[13:46:05]] [INFO] Executing action 140/643: iOS Function: text - Text: "Uno card"
[[13:46:05]] [SUCCESS] Screenshot refreshed successfully
[[13:46:04]] [SUCCESS] Screenshot refreshed
[[13:46:04]] [INFO] Refreshing screenshot...
[[13:46:04]] [INFO] rqLJpAP0mA=pass
[[13:46:00]] [INFO] rqLJpAP0mA=running
[[13:46:00]] [INFO] Executing action 139/643: Tap on Text: "Find"
[[13:46:00]] [SUCCESS] Screenshot refreshed successfully
[[13:45:59]] [SUCCESS] Screenshot refreshed
[[13:45:59]] [INFO] Refreshing screenshot...
[[13:45:59]] [INFO] yiKyF5FJwN=pass
[[13:45:55]] [INFO] yiKyF5FJwN=running
[[13:45:55]] [INFO] Executing action 138/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[13:45:55]] [SUCCESS] Screenshot refreshed successfully
[[13:45:55]] [SUCCESS] Screenshot refreshed
[[13:45:55]] [INFO] Refreshing screenshot...
[[13:45:55]] [INFO] sTtseHOKfa=pass
[[13:45:50]] [INFO] sTtseHOKfa=running
[[13:45:50]] [INFO] Executing action 137/643: iOS Function: text - Text: "Wonderbaby@5"
[[13:45:50]] [SUCCESS] Screenshot refreshed successfully
[[13:45:50]] [SUCCESS] Screenshot refreshed
[[13:45:50]] [INFO] Refreshing screenshot...
[[13:45:50]] [INFO] T3MmUw30SF=pass
[[13:45:46]] [INFO] T3MmUw30SF=running
[[13:45:46]] [INFO] Executing action 136/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[13:45:46]] [SUCCESS] Screenshot refreshed successfully
[[13:45:45]] [SUCCESS] Screenshot refreshed
[[13:45:45]] [INFO] Refreshing screenshot...
[[13:45:45]] [INFO] PPIBJbaXNx=pass
[[13:45:41]] [INFO] PPIBJbaXNx=running
[[13:45:41]] [INFO] Executing action 135/643: iOS Function: text - Text: "<EMAIL>"
[[13:45:41]] [SUCCESS] Screenshot refreshed successfully
[[13:45:40]] [SUCCESS] Screenshot refreshed
[[13:45:40]] [INFO] Refreshing screenshot...
[[13:45:40]] [INFO] LDkFLWks00=pass
[[13:45:36]] [INFO] LDkFLWks00=running
[[13:45:36]] [INFO] Executing action 134/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[13:45:36]] [SUCCESS] Screenshot refreshed successfully
[[13:45:36]] [SUCCESS] Screenshot refreshed
[[13:45:36]] [INFO] Refreshing screenshot...
[[13:45:36]] [INFO] 3caMBvQX7k=pass
[[13:45:33]] [INFO] 3caMBvQX7k=running
[[13:45:33]] [INFO] Executing action 133/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:45:32]] [SUCCESS] Screenshot refreshed successfully
[[13:45:32]] [SUCCESS] Screenshot refreshed
[[13:45:32]] [INFO] Refreshing screenshot...
[[13:45:32]] [INFO] yUJyVO5Wev=pass
[[13:45:30]] [INFO] yUJyVO5Wev=running
[[13:45:30]] [INFO] Executing action 132/643: iOS Function: alert_accept
[[13:45:29]] [SUCCESS] Screenshot refreshed successfully
[[13:45:29]] [SUCCESS] Screenshot refreshed
[[13:45:29]] [INFO] Refreshing screenshot...
[[13:45:29]] [INFO] rkL0oz4kiL=pass
[[13:45:22]] [INFO] rkL0oz4kiL=running
[[13:45:22]] [INFO] Executing action 131/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:45:22]] [SUCCESS] Screenshot refreshed successfully
[[13:45:22]] [SUCCESS] Screenshot refreshed
[[13:45:22]] [INFO] Refreshing screenshot...
[[13:45:22]] [INFO] HotUJOd6oB=pass
[[13:45:10]] [SUCCESS] Screenshot refreshed successfully
[[13:45:09]] [INFO] HotUJOd6oB=running
[[13:45:09]] [INFO] Executing action 130/643: Restart app: env[appid]
[[13:45:09]] [SUCCESS] Screenshot refreshed
[[13:45:09]] [INFO] Refreshing screenshot...
[[13:45:09]] [SUCCESS] Screenshot refreshed successfully
[[13:45:09]] [SUCCESS] Screenshot refreshed
[[13:45:09]] [INFO] Refreshing screenshot...
[[13:45:06]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[13:45:06]] [SUCCESS] Screenshot refreshed successfully
[[13:45:06]] [SUCCESS] Screenshot refreshed
[[13:45:06]] [INFO] Refreshing screenshot...
[[13:44:54]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[13:44:53]] [SUCCESS] Screenshot refreshed successfully
[[13:44:53]] [SUCCESS] Screenshot refreshed
[[13:44:53]] [INFO] Refreshing screenshot...
[[13:44:50]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[13:44:49]] [SUCCESS] Screenshot refreshed successfully
[[13:44:49]] [SUCCESS] Screenshot refreshed
[[13:44:49]] [INFO] Refreshing screenshot...
[[13:44:45]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:44:45]] [SUCCESS] Screenshot refreshed successfully
[[13:44:45]] [SUCCESS] Screenshot refreshed
[[13:44:45]] [INFO] Refreshing screenshot...
[[13:44:38]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[13:44:38]] [SUCCESS] Screenshot refreshed successfully
[[13:44:38]] [SUCCESS] Screenshot refreshed
[[13:44:38]] [INFO] Refreshing screenshot...
[[13:44:32]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[13:44:32]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[13:44:32]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[13:44:32]] [INFO] IR7wnjW7C8=running
[[13:44:32]] [INFO] Executing action 129/643: cleanupSteps action
[[13:44:32]] [SUCCESS] Screenshot refreshed successfully
[[13:44:32]] [SUCCESS] Screenshot refreshed
[[13:44:32]] [INFO] Refreshing screenshot...
[[13:44:32]] [INFO] 7WYExJTqjp=pass
[[13:44:28]] [INFO] 7WYExJTqjp=running
[[13:44:28]] [INFO] Executing action 128/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:44:28]] [SUCCESS] Screenshot refreshed successfully
[[13:44:28]] [SUCCESS] Screenshot refreshed
[[13:44:28]] [INFO] Refreshing screenshot...
[[13:44:28]] [INFO] 4WfPFN961S=pass
[[13:44:21]] [INFO] 4WfPFN961S=running
[[13:44:21]] [INFO] Executing action 127/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:44:21]] [SUCCESS] Screenshot refreshed successfully
[[13:44:20]] [SUCCESS] Screenshot refreshed
[[13:44:20]] [INFO] Refreshing screenshot...
[[13:44:20]] [INFO] NurQsFoMkE=pass
[[13:44:17]] [INFO] NurQsFoMkE=running
[[13:44:17]] [INFO] Executing action 126/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:44:16]] [SUCCESS] Screenshot refreshed successfully
[[13:44:16]] [SUCCESS] Screenshot refreshed
[[13:44:16]] [INFO] Refreshing screenshot...
[[13:44:16]] [INFO] CkfAScJNq8=pass
[[13:44:13]] [INFO] CkfAScJNq8=running
[[13:44:13]] [INFO] Executing action 125/643: Tap on image: env[closebtnimage]
[[13:44:12]] [SUCCESS] Screenshot refreshed successfully
[[13:44:12]] [SUCCESS] Screenshot refreshed
[[13:44:12]] [INFO] Refreshing screenshot...
[[13:44:12]] [INFO] 1NWfFsDiTQ=pass
[[13:44:09]] [INFO] 1NWfFsDiTQ=running
[[13:44:09]] [INFO] Executing action 124/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[13:44:08]] [SUCCESS] Screenshot refreshed successfully
[[13:44:08]] [SUCCESS] Screenshot refreshed
[[13:44:08]] [INFO] Refreshing screenshot...
[[13:44:08]] [INFO] tufIibCj03=pass
[[13:44:04]] [INFO] tufIibCj03=running
[[13:44:04]] [INFO] Executing action 123/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[13:44:04]] [SUCCESS] Screenshot refreshed successfully
[[13:44:04]] [SUCCESS] Screenshot refreshed
[[13:44:04]] [INFO] Refreshing screenshot...
[[13:44:04]] [INFO] uNbKV4slh0=pass
[[13:43:56]] [SUCCESS] Screenshot refreshed successfully
[[13:43:56]] [INFO] uNbKV4slh0=running
[[13:43:56]] [INFO] Executing action 122/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[13:43:56]] [SUCCESS] Screenshot refreshed
[[13:43:56]] [INFO] Refreshing screenshot...
[[13:43:56]] [SUCCESS] Screenshot refreshed successfully
[[13:43:55]] [SUCCESS] Screenshot refreshed
[[13:43:55]] [INFO] Refreshing screenshot...
[[13:43:51]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[13:43:51]] [SUCCESS] Screenshot refreshed successfully
[[13:43:51]] [SUCCESS] Screenshot refreshed
[[13:43:51]] [INFO] Refreshing screenshot...
[[13:43:47]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[13:43:47]] [SUCCESS] Screenshot refreshed successfully
[[13:43:46]] [SUCCESS] Screenshot refreshed
[[13:43:46]] [INFO] Refreshing screenshot...
[[13:43:42]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[13:43:41]] [SUCCESS] Screenshot refreshed successfully
[[13:43:41]] [SUCCESS] Screenshot refreshed
[[13:43:41]] [INFO] Refreshing screenshot...
[[13:43:37]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[13:43:37]] [SUCCESS] Screenshot refreshed successfully
[[13:43:37]] [SUCCESS] Screenshot refreshed
[[13:43:37]] [INFO] Refreshing screenshot...
[[13:43:31]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:43:31]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[13:43:31]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[13:43:31]] [INFO] L5CIZqzpQK=running
[[13:43:31]] [INFO] Executing action 121/643: Execute Test Case: Kmart-Signin (5 steps)
[[13:43:31]] [SUCCESS] Screenshot refreshed successfully
[[13:43:31]] [SUCCESS] Screenshot refreshed
[[13:43:31]] [INFO] Refreshing screenshot...
[[13:43:31]] [INFO] q9ZiyYoE5B=pass
[[13:43:29]] [INFO] q9ZiyYoE5B=running
[[13:43:29]] [INFO] Executing action 120/643: iOS Function: alert_accept
[[13:43:28]] [SUCCESS] Screenshot refreshed successfully
[[13:43:28]] [SUCCESS] Screenshot refreshed
[[13:43:28]] [INFO] Refreshing screenshot...
[[13:43:28]] [INFO] STEdg5jOU8=pass
[[13:43:24]] [INFO] STEdg5jOU8=running
[[13:43:24]] [INFO] Executing action 119/643: Tap on Text: "in"
[[13:43:23]] [SUCCESS] Screenshot refreshed successfully
[[13:43:23]] [SUCCESS] Screenshot refreshed
[[13:43:23]] [INFO] Refreshing screenshot...
[[13:43:23]] [INFO] LDH2hlTZT9=pass
[[13:43:17]] [INFO] LDH2hlTZT9=running
[[13:43:17]] [INFO] Executing action 118/643: Wait for 5 ms
[[13:43:17]] [SUCCESS] Screenshot refreshed successfully
[[13:43:16]] [SUCCESS] Screenshot refreshed
[[13:43:16]] [INFO] Refreshing screenshot...
[[13:43:16]] [INFO] 5Dk9h5bQWl=pass
[[13:43:11]] [INFO] 5Dk9h5bQWl=running
[[13:43:11]] [INFO] Executing action 117/643: Tap on element with accessibility_id: Continue to details
[[13:43:10]] [SUCCESS] Screenshot refreshed successfully
[[13:43:10]] [SUCCESS] Screenshot refreshed
[[13:43:10]] [INFO] Refreshing screenshot...
[[13:43:10]] [INFO] VMzFZ2uTwl=pass
[[13:43:02]] [SUCCESS] Screenshot refreshed successfully
[[13:43:02]] [INFO] VMzFZ2uTwl=running
[[13:43:02]] [INFO] Executing action 116/643: Swipe up till element accessibilityid: "Continue to details" is visible
[[13:43:02]] [SUCCESS] Screenshot refreshed
[[13:43:02]] [INFO] Refreshing screenshot...
[[13:43:01]] [SUCCESS] Screenshot refreshed successfully
[[13:43:01]] [SUCCESS] Screenshot refreshed
[[13:43:01]] [INFO] Refreshing screenshot...
[[13:42:58]] [INFO] Executing Multi Step action step 8/8: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[13:42:58]] [SUCCESS] Screenshot refreshed successfully
[[13:42:57]] [SUCCESS] Screenshot refreshed
[[13:42:57]] [INFO] Refreshing screenshot...
[[13:42:53]] [INFO] Executing Multi Step action step 7/8: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[13:42:52]] [SUCCESS] Screenshot refreshed successfully
[[13:42:52]] [SUCCESS] Screenshot refreshed
[[13:42:52]] [INFO] Refreshing screenshot...
[[13:42:46]] [INFO] Executing Multi Step action step 6/8: Wait for 5 ms
[[13:42:46]] [SUCCESS] Screenshot refreshed successfully
[[13:42:46]] [SUCCESS] Screenshot refreshed
[[13:42:46]] [INFO] Refreshing screenshot...
[[13:42:42]] [INFO] Executing Multi Step action step 5/8: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[13:42:41]] [SUCCESS] Screenshot refreshed successfully
[[13:42:41]] [SUCCESS] Screenshot refreshed
[[13:42:41]] [INFO] Refreshing screenshot...
[[13:42:37]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[13:42:37]] [SUCCESS] Screenshot refreshed successfully
[[13:42:37]] [SUCCESS] Screenshot refreshed
[[13:42:37]] [INFO] Refreshing screenshot...
[[13:42:33]] [INFO] Executing Multi Step action step 3/8: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[13:42:33]] [SUCCESS] Screenshot refreshed successfully
[[13:42:33]] [SUCCESS] Screenshot refreshed
[[13:42:33]] [INFO] Refreshing screenshot...
[[13:42:29]] [INFO] Executing Multi Step action step 2/8: iOS Function: text - Text: "Notebook"
[[13:42:29]] [SUCCESS] Screenshot refreshed successfully
[[13:42:28]] [SUCCESS] Screenshot refreshed
[[13:42:28]] [INFO] Refreshing screenshot...
[[13:42:21]] [INFO] Executing Multi Step action step 1/8: Tap on Text: "Find"
[[13:42:21]] [INFO] Loaded 8 steps from test case: Search and Add (Notebooks)
[[13:42:21]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[13:42:21]] [INFO] 1XUKKmBanM=running
[[13:42:21]] [INFO] Executing action 115/643: Execute Test Case: Search and Add (Notebooks) (8 steps)
[[13:42:21]] [SUCCESS] Screenshot refreshed successfully
[[13:42:21]] [SUCCESS] Screenshot refreshed
[[13:42:21]] [INFO] Refreshing screenshot...
[[13:42:21]] [INFO] NurQsFoMkE=pass
[[13:42:17]] [INFO] NurQsFoMkE=running
[[13:42:17]] [INFO] Executing action 114/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[13:42:16]] [SUCCESS] Screenshot refreshed successfully
[[13:42:16]] [SUCCESS] Screenshot refreshed
[[13:42:16]] [INFO] Refreshing screenshot...
[[13:42:16]] [INFO] 7QpmNS6hif=pass
[[13:42:13]] [INFO] 7QpmNS6hif=running
[[13:42:13]] [INFO] Executing action 113/643: Restart app: env[appid]
[[13:42:12]] [SUCCESS] Screenshot refreshed successfully
[[13:42:12]] [SUCCESS] Screenshot refreshed
[[13:42:12]] [INFO] Refreshing screenshot...
[[13:42:12]] [INFO] 7WYExJTqjp=pass
[[13:42:08]] [INFO] 7WYExJTqjp=running
[[13:42:08]] [INFO] Executing action 112/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:42:08]] [SUCCESS] Screenshot refreshed successfully
[[13:42:08]] [SUCCESS] Screenshot refreshed
[[13:42:08]] [INFO] Refreshing screenshot...
[[13:42:08]] [INFO] 4WfPFN961S=pass
[[13:42:01]] [INFO] 4WfPFN961S=running
[[13:42:01]] [INFO] Executing action 111/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:42:01]] [SUCCESS] Screenshot refreshed successfully
[[13:42:00]] [SUCCESS] Screenshot refreshed
[[13:42:00]] [INFO] Refreshing screenshot...
[[13:42:00]] [INFO] NurQsFoMkE=pass
[[13:41:57]] [INFO] NurQsFoMkE=running
[[13:41:57]] [INFO] Executing action 110/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:41:57]] [SUCCESS] Screenshot refreshed successfully
[[13:41:56]] [SUCCESS] Screenshot refreshed
[[13:41:56]] [INFO] Refreshing screenshot...
[[13:41:56]] [INFO] CkfAScJNq8=pass
[[13:41:53]] [INFO] CkfAScJNq8=running
[[13:41:53]] [INFO] Executing action 109/643: Tap on image: env[closebtnimage]
[[13:41:52]] [SUCCESS] Screenshot refreshed successfully
[[13:41:52]] [SUCCESS] Screenshot refreshed
[[13:41:52]] [INFO] Refreshing screenshot...
[[13:41:52]] [INFO] 1NWfFsDiTQ=pass
[[13:41:48]] [INFO] 1NWfFsDiTQ=running
[[13:41:48]] [INFO] Executing action 108/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[13:41:48]] [SUCCESS] Screenshot refreshed successfully
[[13:41:48]] [SUCCESS] Screenshot refreshed
[[13:41:48]] [INFO] Refreshing screenshot...
[[13:41:48]] [INFO] tufIibCj03=pass
[[13:41:44]] [INFO] tufIibCj03=running
[[13:41:44]] [INFO] Executing action 107/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[13:41:44]] [SUCCESS] Screenshot refreshed successfully
[[13:41:44]] [SUCCESS] Screenshot refreshed
[[13:41:44]] [INFO] Refreshing screenshot...
[[13:41:44]] [INFO] g8u66qfKkX=pass
[[13:41:40]] [INFO] g8u66qfKkX=running
[[13:41:40]] [INFO] Executing action 106/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[13:41:40]] [SUCCESS] Screenshot refreshed successfully
[[13:41:40]] [SUCCESS] Screenshot refreshed
[[13:41:40]] [INFO] Refreshing screenshot...
[[13:41:40]] [INFO] mg4S62Rdtq=pass
[[13:41:33]] [INFO] mg4S62Rdtq=running
[[13:41:33]] [INFO] Executing action 105/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[13:41:33]] [SUCCESS] Screenshot refreshed successfully
[[13:41:33]] [SUCCESS] Screenshot refreshed
[[13:41:33]] [INFO] Refreshing screenshot...
[[13:41:33]] [INFO] pCPTAtSZbf=pass
[[13:41:28]] [INFO] pCPTAtSZbf=running
[[13:41:28]] [INFO] Executing action 104/643: iOS Function: text - Text: "Wonderbaby@5"
[[13:41:28]] [SUCCESS] Screenshot refreshed successfully
[[13:41:28]] [SUCCESS] Screenshot refreshed
[[13:41:28]] [INFO] Refreshing screenshot...
[[13:41:28]] [INFO] DaVBARRwft=pass
[[13:41:24]] [INFO] DaVBARRwft=running
[[13:41:24]] [INFO] Executing action 103/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[13:41:24]] [SUCCESS] Screenshot refreshed successfully
[[13:41:23]] [SUCCESS] Screenshot refreshed
[[13:41:23]] [INFO] Refreshing screenshot...
[[13:41:23]] [INFO] e1RoZWCZJb=pass
[[13:41:19]] [INFO] e1RoZWCZJb=running
[[13:41:19]] [INFO] Executing action 102/643: iOS Function: text - Text: "<EMAIL>"
[[13:41:19]] [SUCCESS] Screenshot refreshed successfully
[[13:41:18]] [SUCCESS] Screenshot refreshed
[[13:41:18]] [INFO] Refreshing screenshot...
[[13:41:18]] [INFO] 50Z2jrodNd=pass
[[13:41:14]] [INFO] 50Z2jrodNd=running
[[13:41:14]] [INFO] Executing action 101/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[13:41:14]] [SUCCESS] Screenshot refreshed successfully
[[13:41:14]] [SUCCESS] Screenshot refreshed
[[13:41:14]] [INFO] Refreshing screenshot...
[[13:41:14]] [INFO] q9ZiyYoE5B=pass
[[13:41:12]] [INFO] q9ZiyYoE5B=running
[[13:41:12]] [INFO] Executing action 100/643: iOS Function: alert_accept
[[13:41:11]] [SUCCESS] Screenshot refreshed successfully
[[13:41:11]] [SUCCESS] Screenshot refreshed
[[13:41:11]] [INFO] Refreshing screenshot...
[[13:41:11]] [INFO] 6PL8P3rT57=pass
[[13:41:07]] [INFO] 6PL8P3rT57=running
[[13:41:07]] [INFO] Executing action 99/643: Tap on Text: "Sign"
[[13:41:06]] [SUCCESS] Screenshot refreshed successfully
[[13:41:06]] [SUCCESS] Screenshot refreshed
[[13:41:06]] [INFO] Refreshing screenshot...
[[13:41:06]] [INFO] 2YGctqXNED=pass
[[13:41:00]] [INFO] 2YGctqXNED=running
[[13:41:00]] [INFO] Executing action 98/643: Tap on element with accessibility_id: Continue to details
[[13:41:00]] [SUCCESS] Screenshot refreshed successfully
[[13:41:00]] [SUCCESS] Screenshot refreshed
[[13:41:00]] [INFO] Refreshing screenshot...
[[13:41:00]] [INFO] 2YGctqXNED=pass
[[13:40:52]] [INFO] 2YGctqXNED=running
[[13:40:52]] [INFO] Executing action 97/643: Swipe up till element accessibilityid: "Continue to details" is visible
[[13:40:51]] [SUCCESS] Screenshot refreshed successfully
[[13:40:51]] [SUCCESS] Screenshot refreshed
[[13:40:51]] [INFO] Refreshing screenshot...
[[13:40:51]] [INFO] tufIibCj03=pass
[[13:40:47]] [INFO] tufIibCj03=running
[[13:40:47]] [INFO] Executing action 96/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[13:40:47]] [SUCCESS] Screenshot refreshed successfully
[[13:40:47]] [SUCCESS] Screenshot refreshed
[[13:40:47]] [INFO] Refreshing screenshot...
[[13:40:47]] [INFO] g8u66qfKkX=pass
[[13:40:44]] [INFO] g8u66qfKkX=running
[[13:40:44]] [INFO] Executing action 95/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[13:40:43]] [SUCCESS] Screenshot refreshed successfully
[[13:40:43]] [SUCCESS] Screenshot refreshed
[[13:40:43]] [INFO] Refreshing screenshot...
[[13:40:43]] [INFO] ZBXuV4sJUR=pass
[[13:40:38]] [INFO] ZBXuV4sJUR=running
[[13:40:38]] [INFO] Executing action 94/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[13:40:38]] [SUCCESS] Screenshot refreshed successfully
[[13:40:38]] [SUCCESS] Screenshot refreshed
[[13:40:38]] [INFO] Refreshing screenshot...
[[13:40:38]] [INFO] XryN8qR1DX=pass
[[13:40:34]] [INFO] XryN8qR1DX=running
[[13:40:34]] [INFO] Executing action 93/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[13:40:34]] [SUCCESS] Screenshot refreshed successfully
[[13:40:33]] [SUCCESS] Screenshot refreshed
[[13:40:33]] [INFO] Refreshing screenshot...
[[13:40:33]] [INFO] XcWXIMtv1E=pass
[[13:40:27]] [INFO] XcWXIMtv1E=running
[[13:40:27]] [INFO] Executing action 92/643: Wait for 5 ms
[[13:40:27]] [SUCCESS] Screenshot refreshed successfully
[[13:40:26]] [SUCCESS] Screenshot refreshed
[[13:40:26]] [INFO] Refreshing screenshot...
[[13:40:26]] [INFO] S1cQQxksEj=pass
[[13:40:19]] [INFO] S1cQQxksEj=running
[[13:40:19]] [INFO] Executing action 91/643: Tap on element with accessibility_id: Add to bag
[[13:40:19]] [SUCCESS] Screenshot refreshed successfully
[[13:40:19]] [SUCCESS] Screenshot refreshed
[[13:40:19]] [INFO] Refreshing screenshot...
[[13:40:19]] [INFO] K2w9XUGwnb=pass
[[13:40:11]] [INFO] K2w9XUGwnb=running
[[13:40:11]] [INFO] Executing action 90/643: Swipe up till element accessibility_id: "Add to bag" is visible
[[13:40:11]] [SUCCESS] Screenshot refreshed successfully
[[13:40:10]] [SUCCESS] Screenshot refreshed
[[13:40:10]] [INFO] Refreshing screenshot...
[[13:40:10]] [INFO] BTYxjEaZEk=pass
[[13:40:06]] [INFO] BTYxjEaZEk=running
[[13:40:06]] [INFO] Executing action 89/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[13:40:06]] [SUCCESS] Screenshot refreshed successfully
[[13:40:06]] [SUCCESS] Screenshot refreshed
[[13:40:06]] [INFO] Refreshing screenshot...
[[13:40:06]] [INFO] YC6bBrKQgq=pass
[[13:40:02]] [INFO] YC6bBrKQgq=running
[[13:40:02]] [INFO] Executing action 88/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[13:40:02]] [SUCCESS] Screenshot refreshed successfully
[[13:40:02]] [SUCCESS] Screenshot refreshed
[[13:40:02]] [INFO] Refreshing screenshot...
[[13:40:02]] [INFO] aRgHcQcLDP=pass
[[13:39:58]] [INFO] aRgHcQcLDP=running
[[13:39:58]] [INFO] Executing action 87/643: iOS Function: text - Text: "uno card"
[[13:39:58]] [SUCCESS] Screenshot refreshed successfully
[[13:39:58]] [SUCCESS] Screenshot refreshed
[[13:39:58]] [INFO] Refreshing screenshot...
[[13:39:58]] [INFO] 4PZC1vVWJW=pass
[[13:39:53]] [INFO] 4PZC1vVWJW=running
[[13:39:53]] [INFO] Executing action 86/643: Tap on Text: "Find"
[[13:39:52]] [SUCCESS] Screenshot refreshed successfully
[[13:39:52]] [SUCCESS] Screenshot refreshed
[[13:39:52]] [INFO] Refreshing screenshot...
[[13:39:52]] [INFO] XryN8qR1DX=pass
[[13:39:48]] [INFO] XryN8qR1DX=running
[[13:39:48]] [INFO] Executing action 85/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[13:39:48]] [SUCCESS] Screenshot refreshed successfully
[[13:39:48]] [SUCCESS] Screenshot refreshed
[[13:39:48]] [INFO] Refreshing screenshot...
[[13:39:48]] [INFO] 7WYExJTqjp=pass
[[13:39:44]] [INFO] 7WYExJTqjp=running
[[13:39:44]] [INFO] Executing action 84/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:39:44]] [SUCCESS] Screenshot refreshed successfully
[[13:39:44]] [SUCCESS] Screenshot refreshed
[[13:39:44]] [INFO] Refreshing screenshot...
[[13:39:44]] [INFO] 4WfPFN961S=pass
[[13:39:37]] [INFO] 4WfPFN961S=running
[[13:39:37]] [INFO] Executing action 83/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:39:36]] [SUCCESS] Screenshot refreshed successfully
[[13:39:36]] [SUCCESS] Screenshot refreshed
[[13:39:36]] [INFO] Refreshing screenshot...
[[13:39:36]] [INFO] NurQsFoMkE=pass
[[13:39:32]] [SUCCESS] Screenshot refreshed successfully
[[13:39:31]] [INFO] NurQsFoMkE=running
[[13:39:31]] [INFO] Executing action 82/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:39:31]] [SUCCESS] Screenshot refreshed
[[13:39:31]] [INFO] Refreshing screenshot...
[[13:39:31]] [SUCCESS] Screenshot refreshed successfully
[[13:39:31]] [SUCCESS] Screenshot refreshed
[[13:39:31]] [INFO] Refreshing screenshot...
[[13:39:27]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[13:39:26]] [SUCCESS] Screenshot refreshed successfully
[[13:39:26]] [SUCCESS] Screenshot refreshed
[[13:39:26]] [INFO] Refreshing screenshot...
[[13:39:22]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[13:39:22]] [SUCCESS] Screenshot refreshed successfully
[[13:39:22]] [SUCCESS] Screenshot refreshed
[[13:39:22]] [INFO] Refreshing screenshot...
[[13:39:17]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[13:39:17]] [SUCCESS] Screenshot refreshed successfully
[[13:39:17]] [SUCCESS] Screenshot refreshed
[[13:39:17]] [INFO] Refreshing screenshot...
[[13:39:13]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[13:39:12]] [SUCCESS] Screenshot refreshed successfully
[[13:39:12]] [SUCCESS] Screenshot refreshed
[[13:39:12]] [INFO] Refreshing screenshot...
[[13:39:07]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:39:07]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[13:39:07]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[13:39:07]] [INFO] APqAlKbucp=running
[[13:39:07]] [INFO] Executing action 81/643: Execute Test Case: Kmart-Signin (5 steps)
[[13:39:06]] [SUCCESS] Screenshot refreshed successfully
[[13:39:06]] [SUCCESS] Screenshot refreshed
[[13:39:06]] [INFO] Refreshing screenshot...
[[13:39:06]] [INFO] byEe7qbCpq=pass
[[13:39:04]] [INFO] byEe7qbCpq=running
[[13:39:04]] [INFO] Executing action 80/643: iOS Function: alert_accept
[[13:39:03]] [SUCCESS] Screenshot refreshed successfully
[[13:39:03]] [SUCCESS] Screenshot refreshed
[[13:39:03]] [INFO] Refreshing screenshot...
[[13:39:03]] [INFO] L6wTorOX8B=pass
[[13:39:00]] [INFO] L6wTorOX8B=running
[[13:39:00]] [INFO] Executing action 79/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[13:38:59]] [SUCCESS] Screenshot refreshed successfully
[[13:38:59]] [SUCCESS] Screenshot refreshed
[[13:38:59]] [INFO] Refreshing screenshot...
[[13:38:59]] [INFO] XryN8qR1DX=pass
[[13:38:55]] [INFO] XryN8qR1DX=running
[[13:38:55]] [INFO] Executing action 78/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:38:55]] [SUCCESS] Screenshot refreshed successfully
[[13:38:55]] [SUCCESS] Screenshot refreshed
[[13:38:55]] [INFO] Refreshing screenshot...
[[13:38:55]] [INFO] lCSewtjn1z=pass
[[13:38:51]] [INFO] lCSewtjn1z=running
[[13:38:51]] [INFO] Executing action 77/643: Restart app: env[appid]
[[13:38:51]] [SUCCESS] Screenshot refreshed successfully
[[13:38:51]] [SUCCESS] Screenshot refreshed
[[13:38:51]] [INFO] Refreshing screenshot...
[[13:38:51]] [INFO] IJh702cxG0=pass
[[13:38:47]] [INFO] IJh702cxG0=running
[[13:38:47]] [INFO] Executing action 76/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:38:47]] [SUCCESS] Screenshot refreshed successfully
[[13:38:47]] [SUCCESS] Screenshot refreshed
[[13:38:47]] [INFO] Refreshing screenshot...
[[13:38:47]] [INFO] 4WfPFN961S=pass
[[13:38:40]] [INFO] 4WfPFN961S=running
[[13:38:40]] [INFO] Executing action 75/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:38:40]] [SUCCESS] Screenshot refreshed successfully
[[13:38:39]] [SUCCESS] Screenshot refreshed
[[13:38:39]] [INFO] Refreshing screenshot...
[[13:38:39]] [INFO] AOcOOSuOsB=pass
[[13:38:35]] [INFO] AOcOOSuOsB=running
[[13:38:35]] [INFO] Executing action 74/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:38:35]] [SUCCESS] Screenshot refreshed successfully
[[13:38:35]] [SUCCESS] Screenshot refreshed
[[13:38:35]] [INFO] Refreshing screenshot...
[[13:38:35]] [INFO] AOcOOSuOsB=pass
[[13:38:27]] [INFO] AOcOOSuOsB=running
[[13:38:27]] [INFO] Executing action 73/643: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:38:27]] [SUCCESS] Screenshot refreshed successfully
[[13:38:27]] [SUCCESS] Screenshot refreshed
[[13:38:27]] [INFO] Refreshing screenshot...
[[13:38:27]] [INFO] N2yjynioko=pass
[[13:38:22]] [INFO] N2yjynioko=running
[[13:38:22]] [INFO] Executing action 72/643: iOS Function: text - Text: "Wonderbaby@5"
[[13:38:22]] [SUCCESS] Screenshot refreshed successfully
[[13:38:22]] [SUCCESS] Screenshot refreshed
[[13:38:22]] [INFO] Refreshing screenshot...
[[13:38:22]] [INFO] SHaIduBnay=pass
[[13:38:18]] [INFO] SHaIduBnay=running
[[13:38:18]] [INFO] Executing action 71/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[13:38:18]] [SUCCESS] Screenshot refreshed successfully
[[13:38:17]] [SUCCESS] Screenshot refreshed
[[13:38:17]] [INFO] Refreshing screenshot...
[[13:38:17]] [INFO] 3XIsWUF1Nj=pass
[[13:38:13]] [INFO] 3XIsWUF1Nj=running
[[13:38:13]] [INFO] Executing action 70/643: Tap on image: captha-chkbox-op-ios.png
[[13:38:13]] [SUCCESS] Screenshot refreshed successfully
[[13:38:13]] [SUCCESS] Screenshot refreshed
[[13:38:13]] [INFO] Refreshing screenshot...
[[13:38:13]] [INFO] wuIMlAwYVA=pass
[[13:38:08]] [INFO] wuIMlAwYVA=running
[[13:38:08]] [INFO] Executing action 69/643: iOS Function: text - Text: "env[uname1]"
[[13:38:08]] [SUCCESS] Screenshot refreshed successfully
[[13:38:08]] [SUCCESS] Screenshot refreshed
[[13:38:08]] [INFO] Refreshing screenshot...
[[13:38:08]] [INFO] 50Z2jrodNd=pass
[[13:38:04]] [INFO] 50Z2jrodNd=running
[[13:38:04]] [INFO] Executing action 68/643: Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,"Email")]
[[13:38:03]] [SUCCESS] Screenshot refreshed successfully
[[13:38:03]] [SUCCESS] Screenshot refreshed
[[13:38:03]] [INFO] Refreshing screenshot...
[[13:38:03]] [INFO] VK2oI6mXSB=pass
[[13:38:00]] [INFO] VK2oI6mXSB=running
[[13:38:00]] [INFO] Executing action 67/643: Wait till xpath=//XCUIElementTypeTextField[contains(@name,"Email")]
[[13:37:59]] [SUCCESS] Screenshot refreshed successfully
[[13:37:59]] [SUCCESS] Screenshot refreshed
[[13:37:59]] [INFO] Refreshing screenshot...
[[13:37:59]] [INFO] q9ZiyYoE5B=pass
[[13:37:57]] [INFO] q9ZiyYoE5B=running
[[13:37:57]] [INFO] Executing action 66/643: iOS Function: alert_accept
[[13:37:56]] [SUCCESS] Screenshot refreshed successfully
[[13:37:56]] [SUCCESS] Screenshot refreshed
[[13:37:56]] [INFO] Refreshing screenshot...
[[13:37:56]] [INFO] 4PZC1vVWJW=pass
[[13:37:51]] [INFO] 4PZC1vVWJW=running
[[13:37:51]] [INFO] Executing action 65/643: Tap on Text: "Sign"
[[13:37:51]] [SUCCESS] Screenshot refreshed successfully
[[13:37:51]] [SUCCESS] Screenshot refreshed
[[13:37:51]] [INFO] Refreshing screenshot...
[[13:37:51]] [INFO] mcscWdhpn2=pass
[[13:37:35]] [INFO] mcscWdhpn2=running
[[13:37:35]] [INFO] Executing action 64/643: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[13:37:35]] [SUCCESS] Screenshot refreshed successfully
[[13:37:34]] [SUCCESS] Screenshot refreshed
[[13:37:34]] [INFO] Refreshing screenshot...
[[13:37:34]] [INFO] 6zUBxjSFym=pass
[[13:37:31]] [INFO] 6zUBxjSFym=running
[[13:37:31]] [INFO] Executing action 63/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[13:37:30]] [SUCCESS] Screenshot refreshed successfully
[[13:37:30]] [SUCCESS] Screenshot refreshed
[[13:37:30]] [INFO] Refreshing screenshot...
[[13:37:30]] [INFO] BTYxjEaZEk=pass
[[13:37:26]] [INFO] BTYxjEaZEk=running
[[13:37:26]] [INFO] Executing action 62/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[13:37:26]] [SUCCESS] Screenshot refreshed successfully
[[13:37:26]] [SUCCESS] Screenshot refreshed
[[13:37:26]] [INFO] Refreshing screenshot...
[[13:37:26]] [INFO] YC6bBrKQgq=pass
[[13:37:22]] [INFO] YC6bBrKQgq=running
[[13:37:22]] [INFO] Executing action 61/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[13:37:22]] [SUCCESS] Screenshot refreshed successfully
[[13:37:22]] [SUCCESS] Screenshot refreshed
[[13:37:22]] [INFO] Refreshing screenshot...
[[13:37:22]] [INFO] aRgHcQcLDP=pass
[[13:37:18]] [INFO] aRgHcQcLDP=running
[[13:37:18]] [INFO] Executing action 60/643: iOS Function: text - Text: "uno card"
[[13:37:17]] [SUCCESS] Screenshot refreshed successfully
[[13:37:17]] [SUCCESS] Screenshot refreshed
[[13:37:17]] [INFO] Refreshing screenshot...
[[13:37:17]] [INFO] 4PZC1vVWJW=pass
[[13:37:12]] [INFO] 4PZC1vVWJW=running
[[13:37:12]] [INFO] Executing action 59/643: Tap on Text: "Find"
[[13:37:12]] [SUCCESS] Screenshot refreshed successfully
[[13:37:12]] [SUCCESS] Screenshot refreshed
[[13:37:12]] [INFO] Refreshing screenshot...
[[13:37:12]] [INFO] lCSewtjn1z=pass
[[13:37:08]] [INFO] lCSewtjn1z=running
[[13:37:08]] [INFO] Executing action 58/643: Restart app: env[appid]
[[13:37:08]] [SUCCESS] Screenshot refreshed successfully
[[13:37:08]] [SUCCESS] Screenshot refreshed
[[13:37:08]] [INFO] Refreshing screenshot...
[[13:37:08]] [INFO] A1Wz7p1iVG=pass
[[13:37:04]] [INFO] A1Wz7p1iVG=running
[[13:37:04]] [INFO] Executing action 57/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:37:04]] [SUCCESS] Screenshot refreshed successfully
[[13:37:03]] [SUCCESS] Screenshot refreshed
[[13:37:03]] [INFO] Refreshing screenshot...
[[13:37:03]] [INFO] ehyLmdZWP2=pass
[[13:36:57]] [INFO] ehyLmdZWP2=running
[[13:36:57]] [INFO] Executing action 56/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:36:56]] [SUCCESS] Screenshot refreshed successfully
[[13:36:56]] [SUCCESS] Screenshot refreshed
[[13:36:56]] [INFO] Refreshing screenshot...
[[13:36:56]] [INFO] ydRnBBO1vR=pass
[[13:36:52]] [INFO] ydRnBBO1vR=running
[[13:36:52]] [INFO] Executing action 55/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:36:52]] [SUCCESS] Screenshot refreshed successfully
[[13:36:52]] [SUCCESS] Screenshot refreshed
[[13:36:52]] [INFO] Refreshing screenshot...
[[13:36:52]] [INFO] quZwUwj3a8=pass
[[13:36:49]] [INFO] quZwUwj3a8=running
[[13:36:49]] [INFO] Executing action 54/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[13:36:48]] [SUCCESS] Screenshot refreshed successfully
[[13:36:48]] [SUCCESS] Screenshot refreshed
[[13:36:48]] [INFO] Refreshing screenshot...
[[13:36:48]] [INFO] FHRlQXe58T=pass
[[13:36:45]] [INFO] FHRlQXe58T=running
[[13:36:45]] [INFO] Executing action 53/643: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[13:36:44]] [SUCCESS] Screenshot refreshed successfully
[[13:36:44]] [SUCCESS] Screenshot refreshed
[[13:36:44]] [INFO] Refreshing screenshot...
[[13:36:44]] [INFO] 8uojw2klHA=pass
[[13:36:40]] [INFO] 8uojw2klHA=running
[[13:36:40]] [INFO] Executing action 52/643: iOS Function: text - Text: "env[pwd]"
[[13:36:39]] [SUCCESS] Screenshot refreshed successfully
[[13:36:39]] [SUCCESS] Screenshot refreshed
[[13:36:39]] [INFO] Refreshing screenshot...
[[13:36:39]] [INFO] SHaIduBnay=pass
[[13:36:35]] [INFO] SHaIduBnay=running
[[13:36:35]] [INFO] Executing action 51/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[13:36:35]] [SUCCESS] Screenshot refreshed successfully
[[13:36:35]] [SUCCESS] Screenshot refreshed
[[13:36:35]] [INFO] Refreshing screenshot...
[[13:36:35]] [INFO] TGoXyeQtB7=pass
[[13:36:30]] [INFO] TGoXyeQtB7=running
[[13:36:30]] [INFO] Executing action 50/643: iOS Function: text - Text: "env[uname]"
[[13:36:30]] [SUCCESS] Screenshot refreshed successfully
[[13:36:30]] [SUCCESS] Screenshot refreshed
[[13:36:30]] [INFO] Refreshing screenshot...
[[13:36:30]] [INFO] rLCI6NVxSc=pass
[[13:36:26]] [INFO] rLCI6NVxSc=running
[[13:36:26]] [INFO] Executing action 49/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[13:36:26]] [SUCCESS] Screenshot refreshed successfully
[[13:36:25]] [SUCCESS] Screenshot refreshed
[[13:36:25]] [INFO] Refreshing screenshot...
[[13:36:25]] [INFO] 6mHVWI3j5e=pass
[[13:36:22]] [INFO] 6mHVWI3j5e=running
[[13:36:22]] [INFO] Executing action 48/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:36:22]] [SUCCESS] Screenshot refreshed successfully
[[13:36:21]] [SUCCESS] Screenshot refreshed
[[13:36:21]] [INFO] Refreshing screenshot...
[[13:36:21]] [INFO] rJVGLpLWM3=pass
[[13:36:19]] [INFO] rJVGLpLWM3=running
[[13:36:19]] [INFO] Executing action 47/643: iOS Function: alert_accept
[[13:36:19]] [SUCCESS] Screenshot refreshed successfully
[[13:36:18]] [SUCCESS] Screenshot refreshed
[[13:36:18]] [INFO] Refreshing screenshot...
[[13:36:18]] [INFO] WlISsMf9QA=pass
[[13:36:15]] [INFO] WlISsMf9QA=running
[[13:36:15]] [INFO] Executing action 46/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[13:36:15]] [SUCCESS] Screenshot refreshed successfully
[[13:36:14]] [SUCCESS] Screenshot refreshed
[[13:36:14]] [INFO] Refreshing screenshot...
[[13:36:14]] [INFO] IvqPpScAJa=pass
[[13:36:11]] [INFO] IvqPpScAJa=running
[[13:36:11]] [INFO] Executing action 45/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[13:36:10]] [SUCCESS] Screenshot refreshed successfully
[[13:36:10]] [SUCCESS] Screenshot refreshed
[[13:36:10]] [INFO] Refreshing screenshot...
[[13:36:10]] [INFO] bGo3feCwBQ=pass
[[13:36:06]] [INFO] bGo3feCwBQ=running
[[13:36:06]] [INFO] Executing action 44/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:36:06]] [SUCCESS] Screenshot refreshed successfully
[[13:36:06]] [SUCCESS] Screenshot refreshed
[[13:36:06]] [INFO] Refreshing screenshot...
[[13:36:06]] [INFO] 4WfPFN961S=pass
[[13:35:59]] [INFO] 4WfPFN961S=running
[[13:35:59]] [INFO] Executing action 43/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:35:59]] [SUCCESS] Screenshot refreshed successfully
[[13:35:59]] [SUCCESS] Screenshot refreshed
[[13:35:59]] [INFO] Refreshing screenshot...
[[13:35:59]] [INFO] F0gZF1jEnT=pass
[[13:35:55]] [INFO] F0gZF1jEnT=running
[[13:35:55]] [INFO] Executing action 42/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:35:55]] [SUCCESS] Screenshot refreshed successfully
[[13:35:54]] [SUCCESS] Screenshot refreshed
[[13:35:54]] [INFO] Refreshing screenshot...
[[13:35:54]] [INFO] EDHl0X27Wi=pass
[[13:35:50]] [INFO] EDHl0X27Wi=running
[[13:35:50]] [INFO] Executing action 41/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[13:35:50]] [SUCCESS] Screenshot refreshed successfully
[[13:35:49]] [SUCCESS] Screenshot refreshed
[[13:35:49]] [INFO] Refreshing screenshot...
[[13:35:49]] [INFO] j8NXU87gV3=pass
[[13:35:45]] [INFO] j8NXU87gV3=running
[[13:35:45]] [INFO] Executing action 40/643: iOS Function: text - Text: "env[pwd]"
[[13:35:45]] [SUCCESS] Screenshot refreshed successfully
[[13:35:45]] [SUCCESS] Screenshot refreshed
[[13:35:45]] [INFO] Refreshing screenshot...
[[13:35:45]] [INFO] dpVaKL19uc=pass
[[13:35:41]] [INFO] dpVaKL19uc=running
[[13:35:41]] [INFO] Executing action 39/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[13:35:40]] [SUCCESS] Screenshot refreshed successfully
[[13:35:40]] [SUCCESS] Screenshot refreshed
[[13:35:40]] [INFO] Refreshing screenshot...
[[13:35:40]] [INFO] eOm1WExcrK=pass
[[13:35:35]] [INFO] eOm1WExcrK=running
[[13:35:35]] [INFO] Executing action 38/643: iOS Function: text - Text: "env[uname]"
[[13:35:35]] [SUCCESS] Screenshot refreshed successfully
[[13:35:35]] [SUCCESS] Screenshot refreshed
[[13:35:35]] [INFO] Refreshing screenshot...
[[13:35:35]] [INFO] 50Z2jrodNd=pass
[[13:35:31]] [INFO] 50Z2jrodNd=running
[[13:35:31]] [INFO] Executing action 37/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[13:35:31]] [SUCCESS] Screenshot refreshed successfully
[[13:35:31]] [SUCCESS] Screenshot refreshed
[[13:35:31]] [INFO] Refreshing screenshot...
[[13:35:31]] [INFO] eJnHS9n9VL=pass
[[13:35:27]] [INFO] eJnHS9n9VL=running
[[13:35:27]] [INFO] Executing action 36/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:35:27]] [SUCCESS] Screenshot refreshed successfully
[[13:35:27]] [SUCCESS] Screenshot refreshed
[[13:35:27]] [INFO] Refreshing screenshot...
[[13:35:27]] [INFO] XuLgjNG74w=pass
[[13:35:24]] [INFO] XuLgjNG74w=running
[[13:35:24]] [INFO] Executing action 35/643: iOS Function: alert_accept
[[13:35:24]] [SUCCESS] Screenshot refreshed successfully
[[13:35:24]] [SUCCESS] Screenshot refreshed
[[13:35:24]] [INFO] Refreshing screenshot...
[[13:35:24]] [INFO] qA1ap4n1m4=pass
[[13:35:17]] [INFO] qA1ap4n1m4=running
[[13:35:17]] [INFO] Executing action 34/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:35:17]] [SUCCESS] Screenshot refreshed successfully
[[13:35:16]] [SUCCESS] Screenshot refreshed
[[13:35:16]] [INFO] Refreshing screenshot...
[[13:35:16]] [INFO] JXFxYCr98V=pass
[[13:35:04]] [SUCCESS] Screenshot refreshed successfully
[[13:35:04]] [INFO] JXFxYCr98V=running
[[13:35:04]] [INFO] Executing action 33/643: Restart app: env[appid]
[[13:35:04]] [SUCCESS] Screenshot refreshed
[[13:35:04]] [INFO] Refreshing screenshot...
[[13:35:04]] [SUCCESS] Screenshot refreshed successfully
[[13:35:03]] [SUCCESS] Screenshot refreshed
[[13:35:03]] [INFO] Refreshing screenshot...
[[13:35:01]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[13:35:01]] [SUCCESS] Screenshot refreshed successfully
[[13:35:00]] [SUCCESS] Screenshot refreshed
[[13:35:00]] [INFO] Refreshing screenshot...
[[13:34:49]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[13:34:49]] [SUCCESS] Screenshot refreshed successfully
[[13:34:49]] [SUCCESS] Screenshot refreshed
[[13:34:49]] [INFO] Refreshing screenshot...
[[13:34:45]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[13:34:45]] [SUCCESS] Screenshot refreshed successfully
[[13:34:45]] [SUCCESS] Screenshot refreshed
[[13:34:45]] [INFO] Refreshing screenshot...
[[13:34:41]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:34:41]] [SUCCESS] Screenshot refreshed successfully
[[13:34:40]] [SUCCESS] Screenshot refreshed
[[13:34:40]] [INFO] Refreshing screenshot...
[[13:34:34]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[13:34:34]] [SUCCESS] Screenshot refreshed successfully
[[13:34:33]] [SUCCESS] Screenshot refreshed
[[13:34:33]] [INFO] Refreshing screenshot...
[[13:34:28]] [SUCCESS] Screenshot refreshed successfully
[[13:34:28]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[13:34:28]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[13:34:28]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[13:34:28]] [INFO] hbIlJIWlVN=running
[[13:34:28]] [INFO] Executing action 32/643: cleanupSteps action
[[13:34:28]] [SUCCESS] Screenshot refreshed
[[13:34:28]] [INFO] Refreshing screenshot...
[[13:34:28]] [SUCCESS] Screenshot refreshed successfully
[[13:34:27]] [SUCCESS] Screenshot refreshed
[[13:34:27]] [INFO] Refreshing screenshot...
[[13:34:24]] [INFO] Executing Multi Step action step 36/36: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[13:34:23]] [SUCCESS] Screenshot refreshed successfully
[[13:34:23]] [SUCCESS] Screenshot refreshed
[[13:34:23]] [INFO] Refreshing screenshot...
[[13:34:19]] [INFO] Executing Multi Step action step 35/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[13:34:19]] [SUCCESS] Screenshot refreshed successfully
[[13:34:19]] [SUCCESS] Screenshot refreshed
[[13:34:19]] [INFO] Refreshing screenshot...
[[13:34:14]] [INFO] Executing Multi Step action step 34/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[13:34:14]] [SUCCESS] Screenshot refreshed successfully
[[13:34:14]] [SUCCESS] Screenshot refreshed
[[13:34:14]] [INFO] Refreshing screenshot...
[[13:34:10]] [INFO] Executing Multi Step action step 33/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[13:34:10]] [SUCCESS] Screenshot refreshed successfully
[[13:34:10]] [SUCCESS] Screenshot refreshed
[[13:34:10]] [INFO] Refreshing screenshot...
[[13:34:06]] [INFO] Executing Multi Step action step 32/36: Tap on image: banner-close-updated.png
[[13:34:05]] [SUCCESS] Screenshot refreshed successfully
[[13:34:05]] [SUCCESS] Screenshot refreshed
[[13:34:05]] [INFO] Refreshing screenshot...
[[13:33:55]] [INFO] Executing Multi Step action step 31/36: Swipe from (50%, 70%) to (50%, 30%)
[[13:33:55]] [SUCCESS] Screenshot refreshed successfully
[[13:33:55]] [SUCCESS] Screenshot refreshed
[[13:33:55]] [INFO] Refreshing screenshot...
[[13:33:51]] [INFO] Executing Multi Step action step 30/36: Tap on image: env[delivery-address-img]
[[13:33:50]] [SUCCESS] Screenshot refreshed successfully
[[13:33:50]] [SUCCESS] Screenshot refreshed
[[13:33:50]] [INFO] Refreshing screenshot...
[[13:33:46]] [INFO] Executing Multi Step action step 29/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[13:33:46]] [SUCCESS] Screenshot refreshed successfully
[[13:33:46]] [SUCCESS] Screenshot refreshed
[[13:33:46]] [INFO] Refreshing screenshot...
[[13:33:39]] [INFO] Executing Multi Step action step 28/36: Tap and Type at (54, 314): "305 238 Flinders"
[[13:33:39]] [SUCCESS] Screenshot refreshed successfully
[[13:33:39]] [SUCCESS] Screenshot refreshed
[[13:33:39]] [INFO] Refreshing screenshot...
[[13:33:34]] [INFO] Executing Multi Step action step 27/36: Tap on Text: "address"
[[13:33:33]] [SUCCESS] Screenshot refreshed successfully
[[13:33:33]] [SUCCESS] Screenshot refreshed
[[13:33:33]] [INFO] Refreshing screenshot...
[[13:33:29]] [INFO] Executing Multi Step action step 26/36: iOS Function: text - Text: " "
[[13:33:29]] [SUCCESS] Screenshot refreshed successfully
[[13:33:29]] [SUCCESS] Screenshot refreshed
[[13:33:29]] [INFO] Refreshing screenshot...
[[13:33:25]] [INFO] Executing Multi Step action step 25/36: textClear action
[[13:33:24]] [SUCCESS] Screenshot refreshed successfully
[[13:33:24]] [SUCCESS] Screenshot refreshed
[[13:33:24]] [INFO] Refreshing screenshot...
[[13:33:20]] [INFO] Executing Multi Step action step 24/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[13:33:20]] [SUCCESS] Screenshot refreshed successfully
[[13:33:20]] [SUCCESS] Screenshot refreshed
[[13:33:20]] [INFO] Refreshing screenshot...
[[13:33:16]] [INFO] Executing Multi Step action step 23/36: textClear action
[[13:33:15]] [SUCCESS] Screenshot refreshed successfully
[[13:33:15]] [SUCCESS] Screenshot refreshed
[[13:33:15]] [INFO] Refreshing screenshot...
[[13:33:11]] [INFO] Executing Multi Step action step 22/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[13:33:11]] [SUCCESS] Screenshot refreshed successfully
[[13:33:11]] [SUCCESS] Screenshot refreshed
[[13:33:11]] [INFO] Refreshing screenshot...
[[13:33:07]] [INFO] Executing Multi Step action step 21/36: textClear action
[[13:33:06]] [SUCCESS] Screenshot refreshed successfully
[[13:33:06]] [SUCCESS] Screenshot refreshed
[[13:33:06]] [INFO] Refreshing screenshot...
[[13:33:02]] [INFO] Executing Multi Step action step 20/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[13:33:02]] [SUCCESS] Screenshot refreshed successfully
[[13:33:02]] [SUCCESS] Screenshot refreshed
[[13:33:02]] [INFO] Refreshing screenshot...
[[13:32:58]] [INFO] Executing Multi Step action step 19/36: textClear action
[[13:32:57]] [SUCCESS] Screenshot refreshed successfully
[[13:32:57]] [SUCCESS] Screenshot refreshed
[[13:32:57]] [INFO] Refreshing screenshot...
[[13:32:54]] [INFO] Executing Multi Step action step 18/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[13:32:53]] [SUCCESS] Screenshot refreshed successfully
[[13:32:53]] [SUCCESS] Screenshot refreshed
[[13:32:53]] [INFO] Refreshing screenshot...
[[13:32:49]] [INFO] Executing Multi Step action step 17/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[13:32:49]] [SUCCESS] Screenshot refreshed successfully
[[13:32:49]] [SUCCESS] Screenshot refreshed
[[13:32:49]] [INFO] Refreshing screenshot...
[[13:32:38]] [INFO] Executing Multi Step action step 16/36: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[13:32:38]] [SUCCESS] Screenshot refreshed successfully
[[13:32:38]] [SUCCESS] Screenshot refreshed
[[13:32:38]] [INFO] Refreshing screenshot...
[[13:32:34]] [INFO] Executing Multi Step action step 15/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[13:32:34]] [SUCCESS] Screenshot refreshed successfully
[[13:32:34]] [SUCCESS] Screenshot refreshed
[[13:32:34]] [INFO] Refreshing screenshot...
[[13:32:30]] [INFO] Executing Multi Step action step 14/36: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[13:32:30]] [SUCCESS] Screenshot refreshed successfully
[[13:32:30]] [SUCCESS] Screenshot refreshed
[[13:32:30]] [INFO] Refreshing screenshot...
[[13:32:25]] [INFO] Executing Multi Step action step 13/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[13:32:25]] [SUCCESS] Screenshot refreshed successfully
[[13:32:25]] [SUCCESS] Screenshot refreshed
[[13:32:25]] [INFO] Refreshing screenshot...
[[13:32:21]] [INFO] Executing Multi Step action step 12/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[13:32:20]] [SUCCESS] Screenshot refreshed successfully
[[13:32:20]] [SUCCESS] Screenshot refreshed
[[13:32:20]] [INFO] Refreshing screenshot...
[[13:32:14]] [INFO] Executing Multi Step action step 11/36: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[13:32:14]] [SUCCESS] Screenshot refreshed successfully
[[13:32:13]] [SUCCESS] Screenshot refreshed
[[13:32:13]] [INFO] Refreshing screenshot...
[[13:32:10]] [INFO] Executing Multi Step action step 10/36: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[13:32:09]] [SUCCESS] Screenshot refreshed successfully
[[13:32:09]] [SUCCESS] Screenshot refreshed
[[13:32:09]] [INFO] Refreshing screenshot...
[[13:32:05]] [INFO] Executing Multi Step action step 9/36: iOS Function: text - Text: "Uno card"
[[13:32:05]] [SUCCESS] Screenshot refreshed successfully
[[13:32:05]] [SUCCESS] Screenshot refreshed
[[13:32:05]] [INFO] Refreshing screenshot...
[[13:32:00]] [INFO] Executing Multi Step action step 8/36: Tap on Text: "Find"
[[13:32:00]] [SUCCESS] Screenshot refreshed successfully
[[13:31:59]] [SUCCESS] Screenshot refreshed
[[13:31:59]] [INFO] Refreshing screenshot...
[[13:31:48]] [INFO] Executing Multi Step action step 7/36: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]"
[[13:31:47]] [SUCCESS] Screenshot refreshed successfully
[[13:31:47]] [SUCCESS] Screenshot refreshed
[[13:31:47]] [INFO] Refreshing screenshot...
[[13:31:35]] [INFO] Executing Multi Step action step 6/36: Tap if locator exists: accessibility_id="btnUpdate"
[[13:31:35]] [SUCCESS] Screenshot refreshed successfully
[[13:31:35]] [SUCCESS] Screenshot refreshed
[[13:31:35]] [INFO] Refreshing screenshot...
[[13:31:23]] [INFO] Executing Multi Step action step 5/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[13:31:23]] [SUCCESS] Screenshot refreshed successfully
[[13:31:22]] [SUCCESS] Screenshot refreshed
[[13:31:22]] [INFO] Refreshing screenshot...
[[13:31:18]] [INFO] Executing Multi Step action step 4/36: Tap on Text: "Save"
[[13:31:17]] [SUCCESS] Screenshot refreshed successfully
[[13:31:17]] [SUCCESS] Screenshot refreshed
[[13:31:17]] [INFO] Refreshing screenshot...
[[13:31:11]] [INFO] Executing Multi Step action step 3/36: Tap on element with accessibility_id: btnCurrentLocationButton
[[13:31:11]] [SUCCESS] Screenshot refreshed successfully
[[13:31:11]] [SUCCESS] Screenshot refreshed
[[13:31:11]] [INFO] Refreshing screenshot...
[[13:31:06]] [INFO] Executing Multi Step action step 2/36: Wait till accessibility_id=btnCurrentLocationButton
[[13:31:06]] [SUCCESS] Screenshot refreshed successfully
[[13:31:06]] [SUCCESS] Screenshot refreshed
[[13:31:06]] [INFO] Refreshing screenshot...
[[13:30:59]] [INFO] Executing Multi Step action step 1/36: Tap on Text: "Edit"
[[13:30:59]] [INFO] Loaded 36 steps from test case: Delivery  Buy
[[13:30:59]] [INFO] Loading steps for multiStep action: Delivery  Buy
[[13:30:59]] [INFO] 8ZYdW2lMKv=running
[[13:30:59]] [INFO] Executing action 31/643: Execute Test Case: Delivery  Buy (36 steps)
[[13:30:58]] [SUCCESS] Screenshot refreshed successfully
[[13:30:58]] [SUCCESS] Screenshot refreshed
[[13:30:58]] [INFO] Refreshing screenshot...
[[13:30:58]] [INFO] cKNu2QoRC1=pass
[[13:30:54]] [INFO] cKNu2QoRC1=running
[[13:30:54]] [INFO] Executing action 30/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[13:30:54]] [SUCCESS] Screenshot refreshed successfully
[[13:30:54]] [SUCCESS] Screenshot refreshed
[[13:30:54]] [INFO] Refreshing screenshot...
[[13:30:54]] [INFO] OyUowAaBzD=pass
[[13:30:50]] [INFO] OyUowAaBzD=running
[[13:30:50]] [INFO] Executing action 29/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[13:30:50]] [SUCCESS] Screenshot refreshed successfully
[[13:30:50]] [SUCCESS] Screenshot refreshed
[[13:30:50]] [INFO] Refreshing screenshot...
[[13:30:50]] [INFO] Ob26qqcA0p=pass
[[13:30:43]] [INFO] Ob26qqcA0p=running
[[13:30:43]] [INFO] Executing action 28/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:30:43]] [SUCCESS] Screenshot refreshed successfully
[[13:30:43]] [SUCCESS] Screenshot refreshed
[[13:30:43]] [INFO] Refreshing screenshot...
[[13:30:43]] [INFO] k3mu9Mt7Ec=pass
[[13:30:39]] [INFO] k3mu9Mt7Ec=running
[[13:30:39]] [INFO] Executing action 27/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[13:30:39]] [SUCCESS] Screenshot refreshed successfully
[[13:30:39]] [SUCCESS] Screenshot refreshed
[[13:30:39]] [INFO] Refreshing screenshot...
[[13:30:39]] [INFO] 8umPSX0vrr=pass
[[13:30:35]] [INFO] 8umPSX0vrr=running
[[13:30:35]] [INFO] Executing action 26/643: Tap on image: banner-close-updated.png
[[13:30:35]] [SUCCESS] Screenshot refreshed successfully
[[13:30:35]] [SUCCESS] Screenshot refreshed
[[13:30:35]] [INFO] Refreshing screenshot...
[[13:30:35]] [INFO] pr9o8Zsm5p=pass
[[13:30:31]] [INFO] pr9o8Zsm5p=running
[[13:30:31]] [INFO] Executing action 25/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[13:30:30]] [SUCCESS] Screenshot refreshed successfully
[[13:30:30]] [SUCCESS] Screenshot refreshed
[[13:30:30]] [INFO] Refreshing screenshot...
[[13:30:30]] [INFO] XCynRs6gJ3=pass
[[13:30:23]] [INFO] XCynRs6gJ3=running
[[13:30:23]] [INFO] Executing action 24/643: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[13:30:22]] [SUCCESS] Screenshot refreshed successfully
[[13:30:22]] [SUCCESS] Screenshot refreshed
[[13:30:22]] [INFO] Refreshing screenshot...
[[13:30:22]] [INFO] UnxZdeLmYu=pass
[[13:30:11]] [INFO] UnxZdeLmYu=running
[[13:30:11]] [INFO] Executing action 23/643: Wait for 10 ms
[[13:30:11]] [SUCCESS] Screenshot refreshed successfully
[[13:30:10]] [SUCCESS] Screenshot refreshed
[[13:30:10]] [INFO] Refreshing screenshot...
[[13:30:10]] [INFO] qjj0i3rcUh=pass
[[13:30:07]] [INFO] qjj0i3rcUh=running
[[13:30:07]] [INFO] Executing action 22/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[13:30:06]] [SUCCESS] Screenshot refreshed successfully
[[13:30:06]] [SUCCESS] Screenshot refreshed
[[13:30:06]] [INFO] Refreshing screenshot...
[[13:30:06]] [INFO] 42Jm6o7r1t=pass
[[13:30:01]] [INFO] 42Jm6o7r1t=running
[[13:30:01]] [INFO] Executing action 21/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[13:30:01]] [SUCCESS] Screenshot refreshed successfully
[[13:30:01]] [SUCCESS] Screenshot refreshed
[[13:30:01]] [INFO] Refreshing screenshot...
[[13:30:01]] [INFO] lWIRxRm6HE=pass
[[13:29:57]] [INFO] lWIRxRm6HE=running
[[13:29:57]] [INFO] Executing action 20/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[13:29:57]] [SUCCESS] Screenshot refreshed successfully
[[13:29:57]] [SUCCESS] Screenshot refreshed
[[13:29:57]] [INFO] Refreshing screenshot...
[[13:29:57]] [INFO] Q0fomJIDoQ=pass
[[13:29:53]] [INFO] Q0fomJIDoQ=running
[[13:29:53]] [INFO] Executing action 19/643: Tap on image: banner-close-updated.png
[[13:29:53]] [SUCCESS] Screenshot refreshed successfully
[[13:29:53]] [SUCCESS] Screenshot refreshed
[[13:29:53]] [INFO] Refreshing screenshot...
[[13:29:53]] [INFO] 7SpDO20tS2=pass
[[13:29:41]] [INFO] 7SpDO20tS2=running
[[13:29:41]] [INFO] Executing action 18/643: Wait for 10 ms
[[13:29:41]] [SUCCESS] Screenshot refreshed successfully
[[13:29:41]] [SUCCESS] Screenshot refreshed
[[13:29:41]] [INFO] Refreshing screenshot...
[[13:29:41]] [INFO] FKZs2qCWoU=pass
[[13:29:37]] [INFO] FKZs2qCWoU=running
[[13:29:37]] [INFO] Executing action 17/643: Tap on Text: "Tarneit"
[[13:29:36]] [SUCCESS] Screenshot refreshed successfully
[[13:29:36]] [SUCCESS] Screenshot refreshed
[[13:29:36]] [INFO] Refreshing screenshot...
[[13:29:36]] [INFO] Qbg9bipTGs=pass
[[13:29:32]] [INFO] Qbg9bipTGs=running
[[13:29:32]] [INFO] Executing action 16/643: Swipe from (50%, 70%) to (50%, 30%)
[[13:29:32]] [SUCCESS] Screenshot refreshed successfully
[[13:29:31]] [SUCCESS] Screenshot refreshed
[[13:29:31]] [INFO] Refreshing screenshot...
[[13:29:31]] [INFO] qjj0i3rcUh=pass
[[13:29:28]] [INFO] qjj0i3rcUh=running
[[13:29:28]] [INFO] Executing action 15/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[13:29:27]] [SUCCESS] Screenshot refreshed successfully
[[13:29:27]] [SUCCESS] Screenshot refreshed
[[13:29:27]] [INFO] Refreshing screenshot...
[[13:29:27]] [INFO] uM5FOSrU5U=pass
[[13:29:23]] [INFO] uM5FOSrU5U=running
[[13:29:23]] [INFO] Executing action 14/643: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[13:29:23]] [SUCCESS] Screenshot refreshed successfully
[[13:29:23]] [SUCCESS] Screenshot refreshed
[[13:29:23]] [INFO] Refreshing screenshot...
[[13:29:23]] [INFO] QB2bKb0SsP=pass
[[13:29:18]] [INFO] QB2bKb0SsP=running
[[13:29:18]] [INFO] Executing action 13/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[13:29:17]] [SUCCESS] Screenshot refreshed successfully
[[13:29:17]] [SUCCESS] Screenshot refreshed
[[13:29:17]] [INFO] Refreshing screenshot...
[[13:29:17]] [INFO] F1olhgKhUt=pass
[[13:29:13]] [INFO] F1olhgKhUt=running
[[13:29:13]] [INFO] Executing action 12/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[13:29:13]] [SUCCESS] Screenshot refreshed successfully
[[13:29:13]] [SUCCESS] Screenshot refreshed
[[13:29:13]] [INFO] Refreshing screenshot...
[[13:29:13]] [INFO] jY0oPjKbuS=pass
[[13:29:10]] [INFO] jY0oPjKbuS=running
[[13:29:10]] [INFO] Executing action 11/643: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[13:29:10]] [SUCCESS] Screenshot refreshed successfully
[[13:29:09]] [SUCCESS] Screenshot refreshed
[[13:29:09]] [INFO] Refreshing screenshot...
[[13:29:09]] [INFO] FnrbyHq7bU=pass
[[13:29:03]] [INFO] FnrbyHq7bU=running
[[13:29:03]] [INFO] Executing action 10/643: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[13:29:03]] [SUCCESS] Screenshot refreshed successfully
[[13:29:03]] [SUCCESS] Screenshot refreshed
[[13:29:03]] [INFO] Refreshing screenshot...
[[13:29:03]] [INFO] nAB6Q8LAdv=pass
[[13:28:59]] [INFO] nAB6Q8LAdv=running
[[13:28:59]] [INFO] Executing action 9/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[13:28:59]] [SUCCESS] Screenshot refreshed successfully
[[13:28:59]] [SUCCESS] Screenshot refreshed
[[13:28:59]] [INFO] Refreshing screenshot...
[[13:28:59]] [INFO] sc2KH9bG6H=pass
[[13:28:55]] [INFO] sc2KH9bG6H=running
[[13:28:55]] [INFO] Executing action 8/643: iOS Function: text - Text: "Uno card"
[[13:28:54]] [SUCCESS] Screenshot refreshed successfully
[[13:28:54]] [SUCCESS] Screenshot refreshed
[[13:28:54]] [INFO] Refreshing screenshot...
[[13:28:54]] [INFO] ZBXCQNlT8z=pass
[[13:28:50]] [INFO] ZBXCQNlT8z=running
[[13:28:50]] [INFO] Executing action 7/643: Tap on Text: "Find"
[[13:28:49]] [SUCCESS] Screenshot refreshed successfully
[[13:28:49]] [SUCCESS] Screenshot refreshed
[[13:28:49]] [INFO] Refreshing screenshot...
[[13:28:49]] [INFO] HYl6Z7Gvqz=pass
[[13:28:41]] [SUCCESS] Screenshot refreshed successfully
[[13:28:41]] [INFO] HYl6Z7Gvqz=running
[[13:28:41]] [INFO] Executing action 6/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[13:28:41]] [SUCCESS] Screenshot refreshed
[[13:28:41]] [INFO] Refreshing screenshot...
[[13:28:40]] [SUCCESS] Screenshot refreshed successfully
[[13:28:40]] [SUCCESS] Screenshot refreshed
[[13:28:40]] [INFO] Refreshing screenshot...
[[13:28:36]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[13:28:35]] [SUCCESS] Screenshot refreshed successfully
[[13:28:35]] [SUCCESS] Screenshot refreshed
[[13:28:35]] [INFO] Refreshing screenshot...
[[13:28:31]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[13:28:31]] [SUCCESS] Screenshot refreshed successfully
[[13:28:31]] [SUCCESS] Screenshot refreshed
[[13:28:31]] [INFO] Refreshing screenshot...
[[13:28:26]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[13:28:26]] [SUCCESS] Screenshot refreshed successfully
[[13:28:26]] [SUCCESS] Screenshot refreshed
[[13:28:26]] [INFO] Refreshing screenshot...
[[13:28:22]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[13:28:21]] [SUCCESS] Screenshot refreshed successfully
[[13:28:21]] [SUCCESS] Screenshot refreshed
[[13:28:21]] [INFO] Refreshing screenshot...
[[13:28:16]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:28:16]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[13:28:16]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[13:28:16]] [INFO] El6k4IPZly=running
[[13:28:16]] [INFO] Executing action 5/643: Execute Test Case: Kmart-Signin (8 steps)
[[13:28:15]] [SUCCESS] Screenshot refreshed successfully
[[13:28:15]] [SUCCESS] Screenshot refreshed
[[13:28:15]] [INFO] Refreshing screenshot...
[[13:28:15]] [INFO] 3caMBvQX7k=pass
[[13:28:12]] [INFO] 3caMBvQX7k=running
[[13:28:12]] [INFO] Executing action 4/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[13:28:12]] [SUCCESS] Screenshot refreshed successfully
[[13:28:12]] [SUCCESS] Screenshot refreshed
[[13:28:12]] [INFO] Refreshing screenshot...
[[13:28:12]] [INFO] yUJyVO5Wev=pass
[[13:28:09]] [INFO] yUJyVO5Wev=running
[[13:28:09]] [INFO] Executing action 3/643: iOS Function: alert_accept
[[13:28:09]] [SUCCESS] Screenshot refreshed successfully
[[13:28:09]] [SUCCESS] Screenshot refreshed
[[13:28:09]] [INFO] Refreshing screenshot...
[[13:28:09]] [INFO] rkL0oz4kiL=pass
[[13:28:02]] [INFO] rkL0oz4kiL=running
[[13:28:02]] [INFO] Executing action 2/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:28:01]] [SUCCESS] Screenshot refreshed successfully
[[13:28:01]] [SUCCESS] Screenshot refreshed
[[13:28:01]] [INFO] Refreshing screenshot...
[[13:28:01]] [INFO] HotUJOd6oB=pass
[[13:27:57]] [INFO] HotUJOd6oB=running
[[13:27:57]] [INFO] Executing action 1/643: Restart app: env[appid]
[[13:27:57]] [INFO] ExecutionManager: Starting execution of 643 actions...
[[13:27:57]] [SUCCESS] Cleared 0 screenshots from database
[[13:27:57]] [INFO] Clearing screenshots from database before execution...
[[13:27:57]] [SUCCESS] All screenshots deleted successfully
[[13:27:57]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[13:27:57]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_132757/screenshots
[[13:27:57]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_132757
[[13:27:57]] [SUCCESS] Report directory initialized successfully
[[13:27:57]] [INFO] Initializing report directory and screenshots folder for test suite...
[[13:27:43]] [SUCCESS] All screenshots deleted successfully
[[13:27:43]] [INFO] All actions cleared
[[13:27:43]] [INFO] Cleaning up screenshots...
[[13:27:37]] [SUCCESS] Screenshot refreshed successfully
[[13:27:37]] [SUCCESS] Screenshot refreshed
[[13:27:37]] [INFO] Refreshing screenshot...
[[13:27:36]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[13:27:36]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[13:27:12]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[13:27:11]] [SUCCESS] Found 1 device(s)
[[13:27:10]] [INFO] Refreshing device list...
