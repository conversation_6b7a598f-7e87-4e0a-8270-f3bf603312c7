// Report interaction JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize expand/collapse functionality for test suites
    const suiteHeadings = document.querySelectorAll('.suite-heading');
    suiteHeadings.forEach(heading => {
        heading.addEventListener('click', function() {
            const parentElement = this.parentElement;
            parentElement.classList.toggle('expanded');
            const testList = parentElement.querySelector('.test-list');
            if (testList) {
                testList.style.display = parentElement.classList.contains('expanded') ? 'block' : 'none';
            }
        });
    });

    // Initialize expand/collapse functionality for test cases
    const testHeaders = document.querySelectorAll('.test-header');
    testHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const testItem = this.parentElement;
            testItem.classList.toggle('expanded');
            const steps = testItem.querySelector('.test-steps');
            if (steps) {
                steps.style.display = testItem.classList.contains('expanded') ? 'block' : 'none';
            }
        });
    });

    // Expand all suites by default
    suiteHeadings.forEach(heading => {
        const parentElement = heading.parentElement;
        parentElement.classList.add('expanded');
        const testList = parentElement.querySelector('.test-list');
        if (testList) {
            testList.style.display = 'block';
        }
    });

    // Simple function to handle missing screenshots
    window.handleMissingScreenshot = function(imgElement) {
        // Basic fallback if fetching directory is not supported
        imgElement.style.display = 'none';
        const noScreenshotMsg = imgElement.nextElementSibling;
        if (noScreenshotMsg) {
            noScreenshotMsg.style.display = 'block';
            noScreenshotMsg.style.color = 'rgb(108, 117, 125)';
        }
    };

    // Function to show step details
    window.showStepDetails = function(stepId) {
        const detailsPanel = document.getElementById('details-panel');
        if (!detailsPanel) return;

        const [_, testCaseIndex, stepIndex] = stepId.split('-');

        // Get the test data from the script that has it
        const testDataScript = document.querySelector('script:not([src])');
        if (!testDataScript) {
            detailsPanel.innerHTML = '<h3>Error: Test data not found</h3>';
            return;
        }

        try {
            // Extract the test data from the script content
            const scriptContent = testDataScript.textContent;
            const testDataMatch = scriptContent.match(/const testData = (.*?);/s);
            if (!testDataMatch) {
                detailsPanel.innerHTML = '<h3>Error: Test data format not recognized</h3>';
                return;
            }

            const testDataString = testDataMatch[1];
            const testData = JSON.parse(testDataString);

            const testCase = testData.testCases[testCaseIndex];
            const step = testCase.steps[stepIndex];

            // Create content
            // Escape HTML special characters in step name and test case name
            const escapedStepName = (step.name || 'Unnamed Step')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');

            const escapedTestCaseName = (testCase.name || 'Unnamed Test Case')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');

            let detailsContent = '<div class="step-details">' +
                '<h3>' + escapedStepName + '</h3>' +
                '<p><strong>Status:</strong> <span class="status-badge status-badge-' + step.status + '">' + step.status + '</span></p>' +
                '<p><strong>Duration:</strong> ' + step.duration + '</p>' +
                '<p><strong>Test Case:</strong> ' + escapedTestCaseName + '</p>';

            // Get screenshot path from the resolved_screenshot property
            let imgSrc = '';

            // ALWAYS use the action_id for the screenshot if available
            if (step.action_id) {
                // Use the action_id directly for the screenshot
                imgSrc = 'screenshots/' + step.action_id + '.png';
                console.log('Using action_id for screenshot: ' + imgSrc);

                // Set up fallback paths for onerror
                step.fallback_paths = [];

                // Add fallback for prefixed version (with al_)
                if (!step.action_id.startsWith('al_')) {
                    step.fallback_paths.push('screenshots/al_' + step.action_id + '.png');
                }

                // Add the action ID to the step details for display
                const stepActionIdElement = document.getElementById('stepActionId');
                if (stepActionIdElement) {
                    stepActionIdElement.textContent = 'Action ID: ' + step.action_id;
                    stepActionIdElement.style.display = 'block';
                }
            }
            // If no action_id, fall back to other options
            else if (step.resolved_screenshot) {
                imgSrc = step.resolved_screenshot;
                console.log('Using resolved_screenshot: ' + imgSrc);
                const stepActionIdElement = document.getElementById('stepActionId');
                if (stepActionIdElement) {
                    stepActionIdElement.style.display = 'none';
                }
            }
            else if (step.alt_screenshot) {
                imgSrc = step.alt_screenshot;
                console.log('Using alternative screenshot: ' + imgSrc);
                const stepActionIdElement = document.getElementById('stepActionId');
                if (stepActionIdElement) {
                    stepActionIdElement.style.display = 'none';
                }
            }
            else {
                imgSrc = '';
                console.log('No screenshot available for this step');
                const stepActionIdElement = document.getElementById('stepActionId');
                if (stepActionIdElement) {
                    stepActionIdElement.style.display = 'none';
                }
            }

            // Add data-step-id attribute to help with debugging
            // Get the action_id if available
            const actionId = step.action_id || '';

            // Add action_id display if available
            let actionIdHtml = '';
            if (actionId) {
                actionIdHtml = '<div class="action-id">Action ID: <span class="action-id-value">' + actionId + '</span></div>';
            }

            // Create fallback onerror handler with multiple paths to try
            let onerrorHandler = 'onerror="';

            // Add fallbacks for different screenshot naming formats
            if (step.fallback_paths && step.fallback_paths.length > 0) {
                // Create a chain of fallbacks
                for (let i = 0; i < step.fallback_paths.length; i++) {
                    const fallbackPath = step.fallback_paths[i];
                    onerrorHandler += 'this.onerror=null; this.src='' + fallbackPath + ''; ';
                }
                // Final fallback to handle missing screenshot
                onerrorHandler += 'this.onerror=function(){handleMissingScreenshot(this);}';
            } else {
                // Default handler if no fallbacks
                onerrorHandler += 'handleMissingScreenshot(this)';
            }
            onerrorHandler += '"';

            // Only show screenshots for takeScreenshot actions
            const stepName = (step.name || '').toLowerCase();
            const isTakeScreenshotAction = stepName.includes('takescreenshot');

            if (isTakeScreenshotAction) {
                detailsContent += '<div class="screenshot-container">' +
                    '<h4>Screenshot:</h4>' +
                    '<p id="stepActionId" style="display: none;">Action ID: ' + actionId + '</p>' +
                    '<img src="' + imgSrc + '" alt="Step Screenshot" data-step-id="' + stepIndex + '" ' +
                    onerrorHandler + ' />' +
                    '<p id="no-screenshot-' + stepIndex + '" style="display: none;">No screenshot available for this step</p>' +
                    '</div>';
            }

            // Add error info if present
            if (step.error) {
                // Escape HTML special characters in error message
                const escapedError = (step.error || '')
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#039;');

                detailsContent += '<div class="error-details">' +
                    '<h4>Error:</h4>' +
                    '<pre>' + escapedError + '</pre>' +
                    '</div>';
            }

            detailsContent += '</div>';

            detailsPanel.innerHTML = detailsContent;
        } catch (error) {
            detailsPanel.innerHTML = '<h3>Error processing test data: ' + error.message + '</h3>';
        }
    };

    // Highlight active step when clicked
    const testSteps = document.querySelectorAll('.test-step');
    testSteps.forEach(step => {
        step.addEventListener('click', function() {
            // Remove highlight from all steps
            testSteps.forEach(s => s.classList.remove('active'));
            // Add highlight to current step
            this.classList.add('active');
        });
    });
});