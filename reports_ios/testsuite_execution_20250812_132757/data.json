{"name": "UI Execution 12/08/2025, 14:52:13", "testCases": [{"name": "Delivery & CNC\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            32 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2280ms", "action_id": "screenshot_20250812_140408", "screenshot_filename": "screenshot_20250812_140408.png", "report_screenshot": "screenshot_20250812_140408.png", "resolved_screenshot": "screenshots/screenshot_20250812_140408.png", "clean_action_id": "screenshot_20250812_140408", "prefixed_action_id": "al_screenshot_20250812_140408", "action_id_screenshot": "screenshots/screenshot_20250812_140408.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5797ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1171ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1999ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250812_143101", "screenshot_filename": "screenshot_20250812_143101.png", "report_screenshot": "screenshot_20250812_143101.png", "resolved_screenshot": "screenshots/screenshot_20250812_143101.png", "clean_action_id": "screenshot_20250812_143101", "prefixed_action_id": "al_screenshot_20250812_143101", "action_id_screenshot": "screenshots/screenshot_20250812_143101.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "7196ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3445ms", "action_id": "screenshot_20250812_143115", "screenshot_filename": "screenshot_20250812_143115.png", "report_screenshot": "screenshot_20250812_143115.png", "resolved_screenshot": "screenshots/screenshot_20250812_143115.png", "clean_action_id": "screenshot_20250812_143115", "prefixed_action_id": "al_screenshot_20250812_143115", "action_id_screenshot": "screenshots/screenshot_20250812_143115.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2626ms", "action_id": "screenshot_20250812_140434", "screenshot_filename": "screenshot_20250812_140434.png", "report_screenshot": "screenshot_20250812_140434.png", "resolved_screenshot": "screenshots/screenshot_20250812_140434.png", "clean_action_id": "screenshot_20250812_140434", "prefixed_action_id": "al_screenshot_20250812_140434", "action_id_screenshot": "screenshots/screenshot_20250812_140434.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2104ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add", "status": "passed", "duration": "4986ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "1648ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2607ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3501ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Click & Collect\"]\" exists", "status": "passed", "duration": "2843ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2474ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2930ms", "action_id": "screenshot_20250812_144120", "screenshot_filename": "screenshot_20250812_144120.png", "report_screenshot": "screenshot_20250812_144120.png", "resolved_screenshot": "screenshots/screenshot_20250812_144120.png", "clean_action_id": "screenshot_20250812_144120", "prefixed_action_id": "al_screenshot_20250812_144120", "action_id_screenshot": "screenshots/screenshot_20250812_144120.png"}, {"name": "Tap on Text: \"Tarneit\"", "status": "passed", "duration": "2966ms", "action_id": "screenshot_20250812_141058", "screenshot_filename": "screenshot_20250812_141058.png", "report_screenshot": "screenshot_20250812_141058.png", "resolved_screenshot": "screenshots/screenshot_20250812_141058.png", "clean_action_id": "screenshot_20250812_141058", "prefixed_action_id": "al_screenshot_20250812_141058", "action_id_screenshot": "screenshots/screenshot_20250812_141058.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10009ms", "action_id": "screenshot_20250812_144646", "screenshot_filename": "screenshot_20250812_144646.png", "report_screenshot": "screenshot_20250812_144646.png", "resolved_screenshot": "screenshots/screenshot_20250812_144646.png", "clean_action_id": "screenshot_20250812_144646", "prefixed_action_id": "al_screenshot_20250812_144646", "action_id_screenshot": "screenshots/screenshot_20250812_144646.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2560ms", "action_id": "screenshot_20250812_135054", "screenshot_filename": "screenshot_20250812_135054.png", "report_screenshot": "screenshot_20250812_135054.png", "resolved_screenshot": "screenshots/screenshot_20250812_135054.png", "clean_action_id": "screenshot_20250812_135054", "prefixed_action_id": "al_screenshot_20250812_135054", "action_id_screenshot": "screenshots/screenshot_20250812_135054.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2320ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3396ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2596ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10009ms", "action_id": "screenshot_20250812_134438", "screenshot_filename": "screenshot_20250812_134438.png", "report_screenshot": "screenshot_20250812_134438.png", "resolved_screenshot": "screenshots/screenshot_20250812_134438.png", "clean_action_id": "screenshot_20250812_134438", "prefixed_action_id": "al_screenshot_20250812_134438", "action_id_screenshot": "screenshots/screenshot_20250812_134438.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"Remove\")]\" is visible", "status": "passed", "duration": "6344ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2506ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2305ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2284ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5473ms", "action_id": "screenshot_20250812_133419", "screenshot_filename": "screenshot_20250812_133419.png", "report_screenshot": "screenshot_20250812_133419.png", "resolved_screenshot": "screenshots/screenshot_20250812_133419.png", "clean_action_id": "screenshot_20250812_133419", "prefixed_action_id": "al_screenshot_20250812_133419", "action_id_screenshot": "screenshots/screenshot_20250812_133419.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2372ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2462ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Delivery  Buy (36 steps)", "status": "passed", "duration": "0ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "All Sign ins\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            97 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "1178ms", "action_id": "screenshot_20250812_140408", "screenshot_filename": "screenshot_20250812_140408.png", "report_screenshot": "screenshot_20250812_140408.png", "resolved_screenshot": "screenshots/screenshot_20250812_140408.png", "clean_action_id": "screenshot_20250812_140408", "prefixed_action_id": "al_screenshot_20250812_140408", "action_id_screenshot": "screenshots/screenshot_20250812_140408.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5530ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1186ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2130ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2702ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3234ms", "action_id": "screenshot_20250812_143101", "screenshot_filename": "screenshot_20250812_143101.png", "report_screenshot": "screenshot_20250812_143101.png", "resolved_screenshot": "screenshots/screenshot_20250812_143101.png", "clean_action_id": "screenshot_20250812_143101", "prefixed_action_id": "al_screenshot_20250812_143101", "action_id_screenshot": "screenshots/screenshot_20250812_143101.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2674ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3035ms", "action_id": "screenshot_20250812_143115", "screenshot_filename": "screenshot_20250812_143115.png", "report_screenshot": "screenshot_20250812_143115.png", "resolved_screenshot": "screenshots/screenshot_20250812_143115.png", "clean_action_id": "screenshot_20250812_143115", "prefixed_action_id": "al_screenshot_20250812_143115", "action_id_screenshot": "screenshots/screenshot_20250812_143115.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "3187ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2476ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5591ms", "action_id": "screenshot_20250812_140434", "screenshot_filename": "screenshot_20250812_140434.png", "report_screenshot": "screenshot_20250812_140434.png", "resolved_screenshot": "screenshots/screenshot_20250812_140434.png", "clean_action_id": "screenshot_20250812_140434", "prefixed_action_id": "al_screenshot_20250812_140434", "action_id_screenshot": "screenshots/screenshot_20250812_140434.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2364ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2489ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtLog in\"]", "status": "passed", "duration": "2262ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1192ms", "action_id": "screenshot_20250812_144120", "screenshot_filename": "screenshot_20250812_144120.png", "report_screenshot": "screenshot_20250812_144120.png", "resolved_screenshot": "screenshots/screenshot_20250812_144120.png", "clean_action_id": "screenshot_20250812_144120", "prefixed_action_id": "al_screenshot_20250812_144120", "action_id_screenshot": "screenshots/screenshot_20250812_144120.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2167ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2678ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3232ms", "action_id": "screenshot_20250812_141058", "screenshot_filename": "screenshot_20250812_141058.png", "report_screenshot": "screenshot_20250812_141058.png", "resolved_screenshot": "screenshots/screenshot_20250812_141058.png", "clean_action_id": "screenshot_20250812_141058", "prefixed_action_id": "al_screenshot_20250812_141058", "action_id_screenshot": "screenshots/screenshot_20250812_141058.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2676ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3045ms", "action_id": "screenshot_20250812_144646", "screenshot_filename": "screenshot_20250812_144646.png", "report_screenshot": "screenshot_20250812_144646.png", "resolved_screenshot": "screenshots/screenshot_20250812_144646.png", "clean_action_id": "screenshot_20250812_144646", "prefixed_action_id": "al_screenshot_20250812_144646", "action_id_screenshot": "screenshots/screenshot_20250812_144646.png"}, {"name": "Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2209ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "2000ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2494ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5410ms", "action_id": "screenshot_20250812_135054", "screenshot_filename": "screenshot_20250812_135054.png", "report_screenshot": "screenshot_20250812_135054.png", "resolved_screenshot": "screenshots/screenshot_20250812_135054.png", "clean_action_id": "screenshot_20250812_135054", "prefixed_action_id": "al_screenshot_20250812_135054", "action_id_screenshot": "screenshots/screenshot_20250812_135054.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2365ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "2193ms", "action_id": "screenshot_20250812_134438", "screenshot_filename": "screenshot_20250812_134438.png", "report_screenshot": "screenshot_20250812_134438.png", "resolved_screenshot": "screenshots/screenshot_20250812_134438.png", "clean_action_id": "screenshot_20250812_134438", "prefixed_action_id": "al_screenshot_20250812_134438", "action_id_screenshot": "screenshots/screenshot_20250812_134438.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3687ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "iOS Function: text - Text: \"uno card\"", "status": "passed", "duration": "2880ms", "action_id": "screenshot_20250812_133419", "screenshot_filename": "screenshot_20250812_133419.png", "report_screenshot": "screenshot_20250812_133419.png", "resolved_screenshot": "screenshots/screenshot_20250812_133419.png", "clean_action_id": "screenshot_20250812_133419", "prefixed_action_id": "al_screenshot_20250812_133419", "action_id_screenshot": "screenshots/screenshot_20250812_133419.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2063ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2672ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2314ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeStaticText[@name=\"Already a member?\"]\" is visible", "status": "passed", "duration": "14744ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "3527ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1168ms", "action_id": "screenshot_20250812_141500", "screenshot_filename": "screenshot_20250812_141500.png", "report_screenshot": "screenshot_20250812_141500.png", "resolved_screenshot": "screenshots/screenshot_20250812_141500.png", "clean_action_id": "screenshot_20250812_141500", "prefixed_action_id": "al_screenshot_20250812_141500", "action_id_screenshot": "screenshots/screenshot_20250812_141500.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[contains(@name,\"Email\")]", "status": "passed", "duration": "2278ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,\"Email\")]", "status": "passed", "duration": "2840ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname1]\"", "status": "passed", "duration": "3333ms", "action_id": "screenshot_20250812_143317", "screenshot_filename": "screenshot_20250812_143317.png", "report_screenshot": "screenshot_20250812_143317.png", "resolved_screenshot": "screenshots/screenshot_20250812_143317.png", "clean_action_id": "screenshot_20250812_143317", "prefixed_action_id": "al_screenshot_20250812_143317", "action_id_screenshot": "screenshots/screenshot_20250812_143317.png"}, {"name": "Tap on image: captha-chkbox-op-ios.png", "status": "passed", "duration": "2709ms", "action_id": "screenshot_20250812_145028", "screenshot_filename": "screenshot_20250812_145028.png", "report_screenshot": "screenshot_20250812_145028.png", "resolved_screenshot": "screenshots/screenshot_20250812_145028.png", "clean_action_id": "screenshot_20250812_145028", "prefixed_action_id": "al_screenshot_20250812_145028", "action_id_screenshot": "screenshots/screenshot_20250812_145028.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "status": "passed", "duration": "2782ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3122ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "5986ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2889ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5444ms", "action_id": "screenshot_20250812_143459", "screenshot_filename": "screenshot_20250812_143459.png", "report_screenshot": "screenshot_20250812_143459.png", "resolved_screenshot": "screenshots/screenshot_20250812_143459.png", "clean_action_id": "screenshot_20250812_143459", "prefixed_action_id": "al_screenshot_20250812_143459", "action_id_screenshot": "screenshots/screenshot_20250812_143459.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2377ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "2228ms", "action_id": "screenshot_20250812_134148", "screenshot_filename": "screenshot_20250812_134148.png", "report_screenshot": "screenshot_20250812_134148.png", "resolved_screenshot": "screenshots/screenshot_20250812_134148.png", "clean_action_id": "screenshot_20250812_134148", "prefixed_action_id": "al_screenshot_20250812_134148", "action_id_screenshot": "screenshots/screenshot_20250812_134148.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2590ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2293ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1153ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3699ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5442ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2382ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2466ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3725ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "iOS Function: text - Text: \"uno card\"", "status": "passed", "duration": "2604ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2064ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2655ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element accessibility_id: \"Add to bag\" is visible", "status": "passed", "duration": "6487ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5775ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5012ms", "action_id": "screenshot_20250812_141306", "screenshot_filename": "screenshot_20250812_141306.png", "report_screenshot": "screenshot_20250812_141306.png", "resolved_screenshot": "screenshots/screenshot_20250812_141306.png", "clean_action_id": "screenshot_20250812_141306", "prefixed_action_id": "al_screenshot_20250812_141306", "action_id_screenshot": "screenshots/screenshot_20250812_141306.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2879ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3440ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2018ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2464ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element accessibilityid: \"Continue to details\" is visible", "status": "passed", "duration": "6852ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Continue to details", "status": "passed", "duration": "4481ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "3225ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1135ms", "action_id": "screenshot_20250812_144242", "screenshot_filename": "screenshot_20250812_144242.png", "report_screenshot": "screenshot_20250812_144242.png", "resolved_screenshot": "screenshots/screenshot_20250812_144242.png", "clean_action_id": "screenshot_20250812_144242", "prefixed_action_id": "al_screenshot_20250812_144242", "action_id_screenshot": "screenshots/screenshot_20250812_144242.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2749ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3226ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "status": "passed", "duration": "2665ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3023ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "5205ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2215ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2475ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2511ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2489ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2293ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5619ms", "action_id": "qcZQIqosdj", "screenshot_filename": "qcZQIqosdj.png", "report_screenshot": "qcZQIqosdj.png", "resolved_screenshot": "screenshots/qcZQIqosdj.png", "clean_action_id": "qcZQIqosdj", "prefixed_action_id": "al_qcZQIqosdj", "action_id_screenshot": "screenshots/qcZQIqosdj.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2382ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "2221ms", "action_id": "screenshot_20250812_135322", "screenshot_filename": "screenshot_20250812_135322.png", "report_screenshot": "screenshot_20250812_135322.png", "resolved_screenshot": "screenshots/screenshot_20250812_135322.png", "clean_action_id": "screenshot_20250812_135322", "prefixed_action_id": "al_screenshot_20250812_135322", "action_id_screenshot": "screenshots/screenshot_20250812_135322.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2725ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Search and Add (Notebooks) (8 steps)", "status": "passed", "duration": "0ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Swipe up till element accessibilityid: \"Continue to details\" is visible", "status": "passed", "duration": "7006ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Continue to details", "status": "passed", "duration": "4452ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5008ms", "action_id": "screenshot_20250812_135336", "screenshot_filename": "screenshot_20250812_135336.png", "report_screenshot": "screenshot_20250812_135336.png", "resolved_screenshot": "screenshots/screenshot_20250812_135336.png", "clean_action_id": "screenshot_20250812_135336", "prefixed_action_id": "al_screenshot_20250812_135336", "action_id_screenshot": "screenshots/screenshot_20250812_135336.png"}, {"name": "Tap on Text: \"in\"", "status": "passed", "duration": "3176ms", "action_id": "rWuyGodCon", "screenshot_filename": "rWuyGodCon.png", "report_screenshot": "rWuyGodCon.png", "resolved_screenshot": "screenshots/rWuyGodCon.png", "clean_action_id": "rWuyGodCon", "prefixed_action_id": "al_rWuyGodCon", "action_id_screenshot": "screenshots/rWuyGodCon.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1120ms", "action_id": "screenshot_20250812_142828", "screenshot_filename": "screenshot_20250812_142828.png", "report_screenshot": "screenshot_20250812_142828.png", "resolved_screenshot": "screenshots/screenshot_20250812_142828.png", "clean_action_id": "screenshot_20250812_142828", "prefixed_action_id": "al_screenshot_20250812_142828", "action_id_screenshot": "screenshots/screenshot_20250812_142828.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250812_132854", "screenshot_filename": "screenshot_20250812_132854.png", "report_screenshot": "screenshot_20250812_132854.png", "resolved_screenshot": "screenshots/screenshot_20250812_132854.png", "clean_action_id": "screenshot_20250812_132854", "prefixed_action_id": "al_screenshot_20250812_132854", "action_id_screenshot": "screenshots/screenshot_20250812_132854.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "6373ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2621ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2441ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2295ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2335ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5448ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2342ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "WishList\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            50 actions", "status": "failed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2196ms", "action_id": "screenshot_20250812_140408", "screenshot_filename": "screenshot_20250812_140408.png", "report_screenshot": "screenshot_20250812_140408.png", "resolved_screenshot": "screenshots/screenshot_20250812_140408.png", "clean_action_id": "screenshot_20250812_140408", "prefixed_action_id": "al_screenshot_20250812_140408", "action_id_screenshot": "screenshots/screenshot_20250812_140408.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "failed", "duration": "5445ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1189ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2099ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2660ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3200ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2663ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3028ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2845ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3377ms", "action_id": "screenshot_20250812_143101", "screenshot_filename": "screenshot_20250812_143101.png", "report_screenshot": "screenshot_20250812_143101.png", "resolved_screenshot": "screenshots/screenshot_20250812_143101.png", "clean_action_id": "screenshot_20250812_143101", "prefixed_action_id": "al_screenshot_20250812_143101", "action_id_screenshot": "screenshots/screenshot_20250812_143101.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2785ms", "action_id": "screenshot_20250812_143115", "screenshot_filename": "screenshot_20250812_143115.png", "report_screenshot": "screenshot_20250812_143115.png", "resolved_screenshot": "screenshots/screenshot_20250812_143115.png", "clean_action_id": "screenshot_20250812_143115", "prefixed_action_id": "al_screenshot_20250812_143115", "action_id_screenshot": "screenshots/screenshot_20250812_143115.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2089ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2640ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2489ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "unknown", "duration": "6111ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "unknown", "duration": "3248ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"You may also like\"]/following-sibling::*[1]//XCUIElementTypeLink)[1]", "status": "unknown", "duration": "13578ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "unknown", "duration": "2288ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "unknown", "duration": "11455ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "unknown", "duration": "4572ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "unknown", "duration": "3303ms", "action_id": "screenshot_20250812_140434", "screenshot_filename": "screenshot_20250812_140434.png", "report_screenshot": "screenshot_20250812_140434.png", "resolved_screenshot": "screenshots/screenshot_20250812_140434.png", "clean_action_id": "screenshot_20250812_140434", "prefixed_action_id": "al_screenshot_20250812_140434", "action_id_screenshot": "screenshots/screenshot_20250812_140434.png"}, {"name": "Tap on Text: \"Find\"", "status": "unknown", "duration": "4218ms", "action_id": "screenshot_20250812_144120", "screenshot_filename": "screenshot_20250812_144120.png", "report_screenshot": "screenshot_20250812_144120.png", "resolved_screenshot": "screenshots/screenshot_20250812_144120.png", "clean_action_id": "screenshot_20250812_144120", "prefixed_action_id": "al_screenshot_20250812_144120", "action_id_screenshot": "screenshots/screenshot_20250812_144120.png"}, {"name": "iOS Function: text - Text: \"P_43386093\"", "status": "unknown", "duration": "2699ms", "action_id": "screenshot_20250812_141058", "screenshot_filename": "screenshot_20250812_141058.png", "report_screenshot": "screenshot_20250812_141058.png", "resolved_screenshot": "screenshots/screenshot_20250812_141058.png", "clean_action_id": "screenshot_20250812_141058", "prefixed_action_id": "al_screenshot_20250812_141058", "action_id_screenshot": "screenshots/screenshot_20250812_141058.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "unknown", "duration": "2503ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "unknown", "duration": "3672ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "unknown", "duration": "11455ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "unknown", "duration": "4572ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "unknown", "duration": "3364ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "1637ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "2475ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Move\"", "status": "unknown", "duration": "3017ms", "action_id": "screenshot_20250812_144646", "screenshot_filename": "screenshot_20250812_144646.png", "report_screenshot": "screenshot_20250812_144646.png", "resolved_screenshot": "screenshots/screenshot_20250812_144646.png", "clean_action_id": "screenshot_20250812_144646", "prefixed_action_id": "al_screenshot_20250812_144646", "action_id_screenshot": "screenshots/screenshot_20250812_144646.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "2380ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "unknown", "duration": "2912ms", "action_id": "screenshot_20250812_135054", "screenshot_filename": "screenshot_20250812_135054.png", "report_screenshot": "screenshot_20250812_135054.png", "resolved_screenshot": "screenshots/screenshot_20250812_135054.png", "clean_action_id": "screenshot_20250812_135054", "prefixed_action_id": "al_screenshot_20250812_135054", "action_id_screenshot": "screenshots/screenshot_20250812_135054.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]", "status": "unknown", "duration": "3097ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "unknown", "duration": "2933ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "unknown", "duration": "3286ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "unknown", "duration": "3386ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "2943ms", "action_id": "screenshot_20250812_134438", "screenshot_filename": "screenshot_20250812_134438.png", "report_screenshot": "screenshot_20250812_134438.png", "resolved_screenshot": "screenshots/screenshot_20250812_134438.png", "clean_action_id": "screenshot_20250812_134438", "prefixed_action_id": "al_screenshot_20250812_134438", "action_id_screenshot": "screenshots/screenshot_20250812_134438.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "unknown", "duration": "2054ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "unknown", "duration": "2633ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "unknown", "duration": "2560ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "unknown", "duration": "2333ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "ifThenSteps action", "status": "unknown", "duration": "4245ms", "action_id": "ifThenStep", "screenshot_filename": "ifThenStep.png", "report_screenshot": "ifThenStep.png", "resolved_screenshot": "screenshots/ifThenStep.png"}, {"name": "Tap on Text: \"Remove\"", "status": "unknown", "duration": "3024ms", "action_id": "screenshot_20250812_133419", "screenshot_filename": "screenshot_20250812_133419.png", "report_screenshot": "screenshot_20250812_133419.png", "resolved_screenshot": "screenshots/screenshot_20250812_133419.png", "clean_action_id": "screenshot_20250812_133419", "prefixed_action_id": "al_screenshot_20250812_133419", "action_id_screenshot": "screenshots/screenshot_20250812_133419.png"}, {"name": "ifThenSteps action", "status": "unknown", "duration": "5004ms", "action_id": "ifThenStep", "screenshot_filename": "ifThenStep.png", "report_screenshot": "ifThenStep.png", "resolved_screenshot": "screenshots/ifThenStep.png"}, {"name": "Tap on Text: \"Remove\"", "status": "unknown", "duration": "2873ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "unknown", "duration": "2412ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "5496ms", "action_id": "screenshot_20250812_141500", "screenshot_filename": "screenshot_20250812_141500.png", "report_screenshot": "screenshot_20250812_141500.png", "resolved_screenshot": "screenshots/screenshot_20250812_141500.png", "clean_action_id": "screenshot_20250812_141500", "prefixed_action_id": "al_screenshot_20250812_141500", "action_id_screenshot": "screenshots/screenshot_20250812_141500.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "unknown", "duration": "2412ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Kmart-Prod-Signin\n                            \n                            \n                        \n                        \n                            \n                                 <PERSON>try\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            57 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2200ms", "action_id": "screenshot_20250812_140408", "screenshot_filename": "screenshot_20250812_140408.png", "report_screenshot": "screenshot_20250812_140408.png", "resolved_screenshot": "screenshots/screenshot_20250812_140408.png", "clean_action_id": "screenshot_20250812_140408", "prefixed_action_id": "al_screenshot_20250812_140408", "action_id_screenshot": "screenshots/screenshot_20250812_140408.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5424ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1182ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2114ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2706ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3216ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2654ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3005ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "1518ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2479ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5445ms", "action_id": "screenshot_20250812_143101", "screenshot_filename": "screenshot_20250812_143101.png", "report_screenshot": "screenshot_20250812_143101.png", "resolved_screenshot": "screenshots/screenshot_20250812_143101.png", "clean_action_id": "screenshot_20250812_143101", "prefixed_action_id": "al_screenshot_20250812_143101", "action_id_screenshot": "screenshots/screenshot_20250812_143101.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2364ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2105ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4395ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1176ms", "action_id": "screenshot_20250812_143115", "screenshot_filename": "screenshot_20250812_143115.png", "report_screenshot": "screenshot_20250812_143115.png", "resolved_screenshot": "screenshots/screenshot_20250812_143115.png", "clean_action_id": "screenshot_20250812_143115", "prefixed_action_id": "al_screenshot_20250812_143115", "action_id_screenshot": "screenshots/screenshot_20250812_143115.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2312ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"OnePass\"", "status": "passed", "duration": "3006ms", "action_id": "screenshot_20250812_140434", "screenshot_filename": "screenshot_20250812_140434.png", "report_screenshot": "screenshot_20250812_140434.png", "resolved_screenshot": "screenshots/screenshot_20250812_140434.png", "clean_action_id": "screenshot_20250812_140434", "prefixed_action_id": "al_screenshot_20250812_140434", "action_id_screenshot": "screenshots/screenshot_20250812_140434.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email*\"]", "status": "passed", "duration": "2793ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3360ms", "action_id": "kiransah<PERSON>", "screenshot_filename": "kiransahoo.png", "report_screenshot": "kiransahoo.png", "resolved_screenshot": "screenshots/kiransahoo.png"}, {"name": "Tap on image: captha-chkbox-op-ios.png", "status": "passed", "duration": "2548ms", "action_id": "screenshot_20250812_144120", "screenshot_filename": "screenshot_20250812_144120.png", "report_screenshot": "screenshot_20250812_144120.png", "resolved_screenshot": "screenshots/screenshot_20250812_144120.png", "clean_action_id": "screenshot_20250812_144120", "prefixed_action_id": "al_screenshot_20250812_144120", "action_id_screenshot": "screenshots/screenshot_20250812_144120.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password*\"]", "status": "passed", "duration": "2746ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@6\"", "status": "passed", "duration": "3150ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2799ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2539ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4192ms", "action_id": "screenshot_20250812_141058", "screenshot_filename": "screenshot_20250812_141058.png", "report_screenshot": "screenshot_20250812_141058.png", "resolved_screenshot": "screenshots/screenshot_20250812_141058.png", "clean_action_id": "screenshot_20250812_141058", "prefixed_action_id": "al_screenshot_20250812_141058", "action_id_screenshot": "screenshots/screenshot_20250812_141058.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2487ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2091ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4534ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1191ms", "action_id": "screenshot_20250812_144646", "screenshot_filename": "screenshot_20250812_144646.png", "report_screenshot": "screenshot_20250812_144646.png", "resolved_screenshot": "screenshots/screenshot_20250812_144646.png", "clean_action_id": "screenshot_20250812_144646", "prefixed_action_id": "al_screenshot_20250812_144646", "action_id_screenshot": "screenshots/screenshot_20250812_144646.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2139ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3066ms", "action_id": "screenshot_20250812_135054", "screenshot_filename": "screenshot_20250812_135054.png", "report_screenshot": "screenshot_20250812_135054.png", "resolved_screenshot": "screenshots/screenshot_20250812_135054.png", "clean_action_id": "screenshot_20250812_135054", "prefixed_action_id": "al_screenshot_20250812_135054", "action_id_screenshot": "screenshots/screenshot_20250812_135054.png"}, {"name": "Tap on Text: \"Apple\"", "status": "passed", "duration": "3131ms", "action_id": "screenshot_20250812_134438", "screenshot_filename": "screenshot_20250812_134438.png", "report_screenshot": "screenshot_20250812_134438.png", "resolved_screenshot": "screenshots/screenshot_20250812_134438.png", "clean_action_id": "screenshot_20250812_134438", "prefixed_action_id": "al_screenshot_20250812_134438", "action_id_screenshot": "screenshots/screenshot_20250812_134438.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10010ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on Text: \"Passcode\"", "status": "passed", "duration": "1758ms", "action_id": "screenshot_20250812_133419", "screenshot_filename": "screenshot_20250812_133419.png", "report_screenshot": "screenshot_20250812_133419.png", "resolved_screenshot": "screenshots/screenshot_20250812_133419.png", "clean_action_id": "screenshot_20250812_133419", "prefixed_action_id": "al_screenshot_20250812_133419", "action_id_screenshot": "screenshots/screenshot_20250812_133419.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"5\"]", "status": "passed", "duration": "1652ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"9\"]", "status": "passed", "duration": "1612ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"1\"]", "status": "passed", "duration": "1593ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"2\"]", "status": "passed", "duration": "1578ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"3\"]", "status": "passed", "duration": "1583ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"4\"]", "status": "passed", "duration": "1592ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "7044ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2507ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5462ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2509ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2095ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4430ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1256ms", "action_id": "screenshot_20250812_141500", "screenshot_filename": "screenshot_20250812_141500.png", "report_screenshot": "screenshot_20250812_141500.png", "resolved_screenshot": "screenshots/screenshot_20250812_141500.png", "clean_action_id": "screenshot_20250812_141500", "prefixed_action_id": "al_screenshot_20250812_141500", "action_id_screenshot": "screenshots/screenshot_20250812_141500.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2125ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Sign in with Google\"]\" is visible", "status": "passed", "duration": "5170ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Sign in with Google\"]", "status": "passed", "duration": "2692ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]", "status": "passed", "duration": "3193ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "4262ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2512ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5479ms", "action_id": "screenshot_20250812_143317", "screenshot_filename": "screenshot_20250812_143317.png", "report_screenshot": "screenshot_20250812_143317.png", "resolved_screenshot": "screenshots/screenshot_20250812_143317.png", "clean_action_id": "screenshot_20250812_143317", "prefixed_action_id": "al_screenshot_20250812_143317", "action_id_screenshot": "screenshots/screenshot_20250812_143317.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2388ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1143ms", "action_id": "screenshot_20250812_145028", "screenshot_filename": "screenshot_20250812_145028.png", "report_screenshot": "screenshot_20250812_145028.png", "resolved_screenshot": "screenshots/screenshot_20250812_145028.png", "clean_action_id": "screenshot_20250812_145028", "prefixed_action_id": "al_screenshot_20250812_145028", "action_id_screenshot": "screenshots/screenshot_20250812_145028.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "AU- MyAccount\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            62 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "1176ms", "action_id": "screenshot_20250812_140408", "screenshot_filename": "screenshot_20250812_140408.png", "report_screenshot": "screenshot_20250812_140408.png", "resolved_screenshot": "screenshots/screenshot_20250812_140408.png", "clean_action_id": "screenshot_20250812_140408", "prefixed_action_id": "al_screenshot_20250812_140408", "action_id_screenshot": "screenshots/screenshot_20250812_140408.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5011ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5589ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1187ms", "action_id": "screenshot_20250812_143101", "screenshot_filename": "screenshot_20250812_143101.png", "report_screenshot": "screenshot_20250812_143101.png", "resolved_screenshot": "screenshots/screenshot_20250812_143101.png", "clean_action_id": "screenshot_20250812_143101", "prefixed_action_id": "al_screenshot_20250812_143101", "action_id_screenshot": "screenshots/screenshot_20250812_143101.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250812_143115", "screenshot_filename": "screenshot_20250812_143115.png", "report_screenshot": "screenshot_20250812_143115.png", "resolved_screenshot": "screenshots/screenshot_20250812_143115.png", "clean_action_id": "screenshot_20250812_143115", "prefixed_action_id": "al_screenshot_20250812_143115", "action_id_screenshot": "screenshots/screenshot_20250812_143115.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3972ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "1868ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"receipts\"", "status": "passed", "duration": "2647ms", "action_id": "screenshot_20250812_140434", "screenshot_filename": "screenshot_20250812_140434.png", "report_screenshot": "screenshot_20250812_140434.png", "resolved_screenshot": "screenshots/screenshot_20250812_140434.png", "clean_action_id": "screenshot_20250812_140434", "prefixed_action_id": "al_screenshot_20250812_140434", "action_id_screenshot": "screenshots/screenshot_20250812_140434.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5005ms", "action_id": "screenshot_20250812_144120", "screenshot_filename": "screenshot_20250812_144120.png", "report_screenshot": "screenshot_20250812_144120.png", "resolved_screenshot": "screenshots/screenshot_20250812_144120.png", "clean_action_id": "screenshot_20250812_144120", "prefixed_action_id": "al_screenshot_20250812_144120", "action_id_screenshot": "screenshots/screenshot_20250812_144120.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2480ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Email tax invoice\"]\" is visible", "status": "passed", "duration": "14495ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Email tax invoice\"]", "status": "passed", "duration": "2476ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Print order details", "status": "passed", "duration": "4438ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Cancel\"]", "status": "passed", "duration": "2964ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2353ms", "action_id": "screenshot_20250812_141058", "screenshot_filename": "screenshot_20250812_141058.png", "report_screenshot": "screenshot_20250812_141058.png", "resolved_screenshot": "screenshots/screenshot_20250812_141058.png", "clean_action_id": "screenshot_20250812_141058", "prefixed_action_id": "al_screenshot_20250812_141058", "action_id_screenshot": "screenshots/screenshot_20250812_141058.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5008ms", "action_id": "screenshot_20250812_144646", "screenshot_filename": "screenshot_20250812_144646.png", "report_screenshot": "screenshot_20250812_144646.png", "resolved_screenshot": "screenshots/screenshot_20250812_144646.png", "clean_action_id": "screenshot_20250812_144646", "prefixed_action_id": "al_screenshot_20250812_144646", "action_id_screenshot": "screenshots/screenshot_20250812_144646.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2488ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]\" exists", "status": "passed", "duration": "1476ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]", "status": "passed", "duration": "2460ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Exchanges Returns\"]\" exists", "status": "passed", "duration": "1468ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2403ms", "action_id": "screenshot_20250812_135054", "screenshot_filename": "screenshot_20250812_135054.png", "report_screenshot": "screenshot_20250812_135054.png", "resolved_screenshot": "screenshots/screenshot_20250812_135054.png", "clean_action_id": "screenshot_20250812_135054", "prefixed_action_id": "al_screenshot_20250812_135054", "action_id_screenshot": "screenshots/screenshot_20250812_135054.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "11236ms", "action_id": "screenshot_20250812_134438", "screenshot_filename": "screenshot_20250812_134438.png", "report_screenshot": "screenshot_20250812_134438.png", "resolved_screenshot": "screenshots/screenshot_20250812_134438.png", "clean_action_id": "screenshot_20250812_134438", "prefixed_action_id": "al_screenshot_20250812_134438", "action_id_screenshot": "screenshots/screenshot_20250812_134438.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5007ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on Text: \"Return\"", "status": "passed", "duration": "2885ms", "action_id": "screenshot_20250812_133419", "screenshot_filename": "screenshot_20250812_133419.png", "report_screenshot": "screenshot_20250812_133419.png", "resolved_screenshot": "screenshots/screenshot_20250812_133419.png", "clean_action_id": "screenshot_20250812_133419", "prefixed_action_id": "al_screenshot_20250812_133419", "action_id_screenshot": "screenshots/screenshot_20250812_133419.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2433ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2498ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "2384ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"details\"", "status": "passed", "duration": "2683ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2370ms", "action_id": "screenshot_20250812_141500", "screenshot_filename": "screenshot_20250812_141500.png", "report_screenshot": "screenshot_20250812_141500.png", "resolved_screenshot": "screenshots/screenshot_20250812_141500.png", "clean_action_id": "screenshot_20250812_141500", "prefixed_action_id": "al_screenshot_20250812_141500", "action_id_screenshot": "screenshots/screenshot_20250812_141500.png"}, {"name": "Tap on Text: \"address\"", "status": "passed", "duration": "2768ms", "action_id": "screenshot_20250812_143317", "screenshot_filename": "screenshot_20250812_143317.png", "report_screenshot": "screenshot_20250812_143317.png", "resolved_screenshot": "screenshots/screenshot_20250812_143317.png", "clean_action_id": "screenshot_20250812_143317", "prefixed_action_id": "al_screenshot_20250812_143317", "action_id_screenshot": "screenshots/screenshot_20250812_143317.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2289ms", "action_id": "screenshot_20250812_145028", "screenshot_filename": "screenshot_20250812_145028.png", "report_screenshot": "screenshot_20250812_145028.png", "resolved_screenshot": "screenshots/screenshot_20250812_145028.png", "clean_action_id": "screenshot_20250812_145028", "prefixed_action_id": "al_screenshot_20250812_145028", "action_id_screenshot": "screenshots/screenshot_20250812_145028.png"}, {"name": "Tap on Text: \"payment\"", "status": "passed", "duration": "2667ms", "action_id": "screenshot_20250812_143459", "screenshot_filename": "screenshot_20250812_143459.png", "report_screenshot": "screenshot_20250812_143459.png", "resolved_screenshot": "screenshots/screenshot_20250812_143459.png", "clean_action_id": "screenshot_20250812_143459", "prefixed_action_id": "al_screenshot_20250812_143459", "action_id_screenshot": "screenshots/screenshot_20250812_143459.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2319ms", "action_id": "screenshot_20250812_134148", "screenshot_filename": "screenshot_20250812_134148.png", "report_screenshot": "screenshot_20250812_134148.png", "resolved_screenshot": "screenshots/screenshot_20250812_134148.png", "clean_action_id": "screenshot_20250812_134148", "prefixed_action_id": "al_screenshot_20250812_134148", "action_id_screenshot": "screenshots/screenshot_20250812_134148.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2158ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "2662ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait till accessibility_id=btneditFlybuysCard", "status": "passed", "duration": "3204ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btneditFlybuysCard", "status": "passed", "duration": "4169ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Remove card", "status": "passed", "duration": "4136ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnRemove", "status": "passed", "duration": "4104ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "2783ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on element with accessibility_id: btnLinkFlyBuys", "status": "passed", "duration": "4190ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Flybuys barcode number", "status": "passed", "duration": "4186ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Input text: \"2791234567890\"", "status": "passed", "duration": "1678ms", "action_id": "2791234567", "screenshot_filename": "2791234567.png", "report_screenshot": "2791234567.png", "resolved_screenshot": "screenshots/2791234567.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "passed", "duration": "4249ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnSaveFlybuysCard", "status": "passed", "duration": "4161ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2349ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Check if element with accessibility_id=\"txtMy Flybuys card\" exists", "status": "passed", "duration": "2020ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5431ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on Text: \"locator\"", "status": "passed", "duration": "2579ms", "action_id": "screenshot_20250812_141306", "screenshot_filename": "screenshot_20250812_141306.png", "report_screenshot": "screenshot_20250812_141306.png", "resolved_screenshot": "screenshots/screenshot_20250812_141306.png", "clean_action_id": "screenshot_20250812_141306", "prefixed_action_id": "al_screenshot_20250812_141306", "action_id_screenshot": "screenshots/screenshot_20250812_141306.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "5097ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap and Type at (env[store-locator-x], env[store-locator-y]): \"env[store-locator-postcode]\"", "status": "passed", "duration": "5294ms", "action_id": "screenshot_20250812_144242", "screenshot_filename": "screenshot_20250812_144242.png", "report_screenshot": "screenshot_20250812_144242.png", "resolved_screenshot": "screenshots/screenshot_20250812_144242.png", "clean_action_id": "screenshot_20250812_144242", "prefixed_action_id": "al_screenshot_20250812_144242", "action_id_screenshot": "screenshots/screenshot_20250812_144242.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "3202ms", "action_id": "qcZQIqosdj", "screenshot_filename": "qcZQIqosdj.png", "report_screenshot": "qcZQIqosdj.png", "resolved_screenshot": "screenshots/qcZQIqosdj.png", "clean_action_id": "qcZQIqosdj", "prefixed_action_id": "al_qcZQIqosdj", "action_id_screenshot": "screenshots/qcZQIqosdj.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Melbourne Cbd\"]\" exists", "status": "passed", "duration": "1708ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2334ms", "action_id": "screenshot_20250812_135322", "screenshot_filename": "screenshot_20250812_135322.png", "report_screenshot": "screenshot_20250812_135322.png", "resolved_screenshot": "screenshots/screenshot_20250812_135322.png", "clean_action_id": "screenshot_20250812_135322", "prefixed_action_id": "al_screenshot_20250812_135322", "action_id_screenshot": "screenshots/screenshot_20250812_135322.png"}, {"name": "Tap on Text: \"Invite\"", "status": "passed", "duration": "2514ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"I’m loving the new Kmart app\")]\" exists", "status": "passed", "duration": "1767ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2855ms", "action_id": "screenshot_20250812_135336", "screenshot_filename": "screenshot_20250812_135336.png", "report_screenshot": "screenshot_20250812_135336.png", "resolved_screenshot": "screenshots/screenshot_20250812_135336.png", "clean_action_id": "screenshot_20250812_135336", "prefixed_action_id": "al_screenshot_20250812_135336", "action_id_screenshot": "screenshots/screenshot_20250812_135336.png"}, {"name": "Tap on Text: \"Customer\"", "status": "passed", "duration": "2600ms", "action_id": "rWuyGodCon", "screenshot_filename": "rWuyGodCon.png", "report_screenshot": "rWuyGodCon.png", "resolved_screenshot": "screenshots/rWuyGodCon.png", "clean_action_id": "rWuyGodCon", "prefixed_action_id": "al_rWuyGodCon", "action_id_screenshot": "screenshots/rWuyGodCon.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2375ms", "action_id": "screenshot_20250812_142828", "screenshot_filename": "screenshot_20250812_142828.png", "report_screenshot": "screenshot_20250812_142828.png", "resolved_screenshot": "screenshots/screenshot_20250812_142828.png", "clean_action_id": "screenshot_20250812_142828", "prefixed_action_id": "al_screenshot_20250812_142828", "action_id_screenshot": "screenshots/screenshot_20250812_142828.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5481ms", "action_id": "screenshot_20250812_132854", "screenshot_filename": "screenshot_20250812_132854.png", "report_screenshot": "screenshot_20250812_132854.png", "resolved_screenshot": "screenshots/screenshot_20250812_132854.png", "clean_action_id": "screenshot_20250812_132854", "prefixed_action_id": "al_screenshot_20250812_132854", "action_id_screenshot": "screenshots/screenshot_20250812_132854.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2580ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Others\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            43 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "1169ms", "action_id": "screenshot_20250812_140408", "screenshot_filename": "screenshot_20250812_140408.png", "report_screenshot": "screenshot_20250812_140408.png", "resolved_screenshot": "screenshots/screenshot_20250812_140408.png", "clean_action_id": "screenshot_20250812_140408", "prefixed_action_id": "al_screenshot_20250812_140408", "action_id_screenshot": "screenshots/screenshot_20250812_140408.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"btnBarcodeScanner\"]", "status": "passed", "duration": "2609ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "410ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"Barcode Scanner\"]\" exists", "status": "passed", "duration": "1391ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"imgHelp\"]\" exists", "status": "passed", "duration": "1395ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPosition barcode lengthwise within rectangle frame to view helpful product information\"]\" exists", "status": "passed", "duration": "1376ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2712ms", "action_id": "screenshot_20250812_143101", "screenshot_filename": "screenshot_20250812_143101.png", "report_screenshot": "screenshot_20250812_143101.png", "resolved_screenshot": "screenshots/screenshot_20250812_143101.png", "clean_action_id": "screenshot_20250812_143101", "prefixed_action_id": "al_screenshot_20250812_143101", "action_id_screenshot": "screenshots/screenshot_20250812_143101.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2489ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtTrack My Order\"]", "status": "passed", "duration": "2310ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Order number\"]", "status": "passed", "duration": "2310ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"env[searchorder]\"", "status": "passed", "duration": "1637ms", "action_id": "searchorde", "screenshot_filename": "searchorde.png", "report_screenshot": "searchorde.png", "resolved_screenshot": "screenshots/searchorde.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email Address\"] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2480ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"<EMAIL>\"", "status": "passed", "duration": "1820ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Search for order\"]", "status": "passed", "duration": "2451ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"refunded\"]\" exists", "status": "passed", "duration": "1494ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2297ms", "action_id": "screenshot_20250812_143115", "screenshot_filename": "screenshot_20250812_143115.png", "report_screenshot": "screenshot_20250812_143115.png", "resolved_screenshot": "screenshots/screenshot_20250812_143115.png", "clean_action_id": "screenshot_20250812_143115", "prefixed_action_id": "al_screenshot_20250812_143115", "action_id_screenshot": "screenshots/screenshot_20250812_143115.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2309ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1167ms", "action_id": "screenshot_20250812_140434", "screenshot_filename": "screenshot_20250812_140434.png", "report_screenshot": "screenshot_20250812_140434.png", "resolved_screenshot": "screenshots/screenshot_20250812_140434.png", "clean_action_id": "screenshot_20250812_140434", "prefixed_action_id": "al_screenshot_20250812_140434", "action_id_screenshot": "screenshots/screenshot_20250812_140434.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2778ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3182ms", "action_id": "kiransah<PERSON>", "screenshot_filename": "kiransahoo.png", "report_screenshot": "kiransahoo.png", "resolved_screenshot": "screenshots/kiransahoo.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2674ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "2994ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]\" exists", "status": "passed", "duration": "3934ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]", "status": "passed", "duration": "2334ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtOnePassSubscritionBox\"]\" exists", "status": "passed", "duration": "1431ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2313ms", "action_id": "screenshot_20250812_144120", "screenshot_filename": "screenshot_20250812_144120.png", "report_screenshot": "screenshot_20250812_144120.png", "resolved_screenshot": "screenshots/screenshot_20250812_144120.png", "clean_action_id": "screenshot_20250812_144120", "prefixed_action_id": "al_screenshot_20250812_144120", "action_id_screenshot": "screenshots/screenshot_20250812_144120.png"}, {"name": "Tap on Text: \"receipts\"", "status": "passed", "duration": "2689ms", "action_id": "screenshot_20250812_141058", "screenshot_filename": "screenshot_20250812_141058.png", "report_screenshot": "screenshot_20250812_141058.png", "resolved_screenshot": "screenshots/screenshot_20250812_141058.png", "clean_action_id": "screenshot_20250812_141058", "prefixed_action_id": "al_screenshot_20250812_141058", "action_id_screenshot": "screenshots/screenshot_20250812_141058.png"}, {"name": "Tap on Text: \"Store\"", "status": "passed", "duration": "2817ms", "action_id": "screenshot_20250812_144646", "screenshot_filename": "screenshot_20250812_144646.png", "report_screenshot": "screenshot_20250812_144646.png", "resolved_screenshot": "screenshots/screenshot_20250812_144646.png", "clean_action_id": "screenshot_20250812_144646", "prefixed_action_id": "al_screenshot_20250812_144646", "action_id_screenshot": "screenshots/screenshot_20250812_144646.png"}, {"name": "Check if element with xpath=\"(//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]\" exists", "status": "passed", "duration": "1526ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2491ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"<PERSON>ly<PERSON> Receipt\"]/XCUIElementTypeOther[2]\" exists", "status": "passed", "duration": "1955ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"<PERSON>\"]", "status": "passed", "duration": "3112ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "2207ms", "action_id": "screenshot_20250812_135054", "screenshot_filename": "screenshot_20250812_135054.png", "report_screenshot": "screenshot_20250812_135054.png", "resolved_screenshot": "screenshots/screenshot_20250812_135054.png", "clean_action_id": "screenshot_20250812_135054", "prefixed_action_id": "al_screenshot_20250812_135054", "action_id_screenshot": "screenshots/screenshot_20250812_135054.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3034ms", "action_id": "screenshot_20250812_134438", "screenshot_filename": "screenshot_20250812_134438.png", "report_screenshot": "screenshot_20250812_134438.png", "resolved_screenshot": "screenshots/screenshot_20250812_134438.png", "clean_action_id": "screenshot_20250812_134438", "prefixed_action_id": "al_screenshot_20250812_134438", "action_id_screenshot": "screenshots/screenshot_20250812_134438.png"}, {"name": "Tap on Text: \"current\"", "status": "passed", "duration": "2821ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"btnUpdate\"]\"", "status": "passed", "duration": "11097ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2412ms", "action_id": "screenshot_20250812_133419", "screenshot_filename": "screenshot_20250812_133419.png", "report_screenshot": "screenshot_20250812_133419.png", "resolved_screenshot": "screenshots/screenshot_20250812_133419.png", "clean_action_id": "screenshot_20250812_133419", "prefixed_action_id": "al_screenshot_20250812_133419", "action_id_screenshot": "screenshots/screenshot_20250812_133419.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Save my location\"]\"", "status": "passed", "duration": "10273ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2460ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2794ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2349ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1061ms", "action_id": "screenshot_20250812_141500", "screenshot_filename": "screenshot_20250812_141500.png", "report_screenshot": "screenshot_20250812_141500.png", "resolved_screenshot": "screenshots/screenshot_20250812_141500.png", "clean_action_id": "screenshot_20250812_141500", "prefixed_action_id": "al_screenshot_20250812_141500", "action_id_screenshot": "screenshots/screenshot_20250812_141500.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "23226ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Postcode Flow\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            52 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "1164ms", "action_id": "screenshot_20250812_140408", "screenshot_filename": "screenshot_20250812_140408.png", "report_screenshot": "screenshot_20250812_140408.png", "resolved_screenshot": "screenshots/screenshot_20250812_140408.png", "clean_action_id": "screenshot_20250812_140408", "prefixed_action_id": "al_screenshot_20250812_140408", "action_id_screenshot": "screenshots/screenshot_20250812_140408.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5485ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1179ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2122ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250812_143101", "screenshot_filename": "screenshot_20250812_143101.png", "report_screenshot": "screenshot_20250812_143101.png", "resolved_screenshot": "screenshots/screenshot_20250812_143101.png", "clean_action_id": "screenshot_20250812_143101", "prefixed_action_id": "al_screenshot_20250812_143101", "action_id_screenshot": "screenshots/screenshot_20250812_143101.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "2341ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "2500ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "4194ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3339ms", "action_id": "screenshot_20250812_143115", "screenshot_filename": "screenshot_20250812_143115.png", "report_screenshot": "screenshot_20250812_143115.png", "resolved_screenshot": "screenshots/screenshot_20250812_143115.png", "clean_action_id": "screenshot_20250812_143115", "prefixed_action_id": "al_screenshot_20250812_143115", "action_id_screenshot": "screenshots/screenshot_20250812_143115.png"}, {"name": "Tap on Text: \"2000\"", "status": "passed", "duration": "2920ms", "action_id": "screenshot_20250812_140434", "screenshot_filename": "screenshot_20250812_140434.png", "report_screenshot": "screenshot_20250812_140434.png", "resolved_screenshot": "screenshots/screenshot_20250812_140434.png", "clean_action_id": "screenshot_20250812_140434", "prefixed_action_id": "al_screenshot_20250812_140434", "action_id_screenshot": "screenshots/screenshot_20250812_140434.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3273ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "2867ms", "action_id": "screenshot_20250812_144120", "screenshot_filename": "screenshot_20250812_144120.png", "report_screenshot": "screenshot_20250812_144120.png", "resolved_screenshot": "screenshots/screenshot_20250812_144120.png", "clean_action_id": "screenshot_20250812_144120", "prefixed_action_id": "al_screenshot_20250812_144120", "action_id_screenshot": "screenshots/screenshot_20250812_144120.png"}, {"name": "Tap if locator exists: accessibility_id=\"btnUpdate\"", "status": "passed", "duration": "5360ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3370ms", "action_id": "screenshot_20250812_141058", "screenshot_filename": "screenshot_20250812_141058.png", "report_screenshot": "screenshot_20250812_141058.png", "resolved_screenshot": "screenshots/screenshot_20250812_141058.png", "clean_action_id": "screenshot_20250812_141058", "prefixed_action_id": "al_screenshot_20250812_141058", "action_id_screenshot": "screenshots/screenshot_20250812_141058.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2597ms", "action_id": "screenshot_20250812_144646", "screenshot_filename": "screenshot_20250812_144646.png", "report_screenshot": "screenshot_20250812_144646.png", "resolved_screenshot": "screenshots/screenshot_20250812_144646.png", "clean_action_id": "screenshot_20250812_144646", "prefixed_action_id": "al_screenshot_20250812_144646", "action_id_screenshot": "screenshots/screenshot_20250812_144646.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2097ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3321ms", "action_id": "screenshot_20250812_135054", "screenshot_filename": "screenshot_20250812_135054.png", "report_screenshot": "screenshot_20250812_135054.png", "resolved_screenshot": "screenshots/screenshot_20250812_135054.png", "clean_action_id": "screenshot_20250812_135054", "prefixed_action_id": "al_screenshot_20250812_135054", "action_id_screenshot": "screenshots/screenshot_20250812_135054.png"}, {"name": "Wait till accessibility_id=btnCurrentLocationButton", "status": "passed", "duration": "3239ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"current\"", "status": "passed", "duration": "2812ms", "action_id": "screenshot_20250812_134438", "screenshot_filename": "screenshot_20250812_134438.png", "report_screenshot": "screenshot_20250812_134438.png", "resolved_screenshot": "screenshots/screenshot_20250812_134438.png", "clean_action_id": "screenshot_20250812_134438", "prefixed_action_id": "al_screenshot_20250812_134438", "action_id_screenshot": "screenshots/screenshot_20250812_134438.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3263ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "2916ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Check if element with text=\"Tarneit\" exists", "status": "passed", "duration": "13192ms", "action_id": "screenshot_20250812_133419", "screenshot_filename": "screenshot_20250812_133419.png", "report_screenshot": "screenshot_20250812_133419.png", "resolved_screenshot": "screenshots/screenshot_20250812_133419.png", "clean_action_id": "screenshot_20250812_133419", "prefixed_action_id": "al_screenshot_20250812_133419", "action_id_screenshot": "screenshots/screenshot_20250812_133419.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2116ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2662ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2462ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3407ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "4210ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3351ms", "action_id": "screenshot_20250812_141500", "screenshot_filename": "screenshot_20250812_141500.png", "report_screenshot": "screenshot_20250812_141500.png", "resolved_screenshot": "screenshots/screenshot_20250812_141500.png", "clean_action_id": "screenshot_20250812_141500", "prefixed_action_id": "al_screenshot_20250812_141500", "action_id_screenshot": "screenshots/screenshot_20250812_141500.png"}, {"name": "Tap on Text: \"2000\"", "status": "passed", "duration": "2963ms", "action_id": "screenshot_20250812_143317", "screenshot_filename": "screenshot_20250812_143317.png", "report_screenshot": "screenshot_20250812_143317.png", "resolved_screenshot": "screenshots/screenshot_20250812_143317.png", "clean_action_id": "screenshot_20250812_143317", "prefixed_action_id": "al_screenshot_20250812_143317", "action_id_screenshot": "screenshots/screenshot_20250812_143317.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3271ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "2870ms", "action_id": "screenshot_20250812_145028", "screenshot_filename": "screenshot_20250812_145028.png", "report_screenshot": "screenshot_20250812_145028.png", "resolved_screenshot": "screenshots/screenshot_20250812_145028.png", "clean_action_id": "screenshot_20250812_145028", "prefixed_action_id": "al_screenshot_20250812_145028", "action_id_screenshot": "screenshots/screenshot_20250812_145028.png"}, {"name": "Check if element with text=\"Broadway\" exists", "status": "passed", "duration": "11704ms", "action_id": "screenshot_20250812_143459", "screenshot_filename": "screenshot_20250812_143459.png", "report_screenshot": "screenshot_20250812_143459.png", "resolved_screenshot": "screenshots/screenshot_20250812_143459.png", "clean_action_id": "screenshot_20250812_143459", "prefixed_action_id": "al_screenshot_20250812_143459", "action_id_screenshot": "screenshots/screenshot_20250812_143459.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "17021ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "1856ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2883ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3384ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2669ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]", "status": "passed", "duration": "2023ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "3281ms", "action_id": "screenshot_20250812_134148", "screenshot_filename": "screenshot_20250812_134148.png", "report_screenshot": "screenshot_20250812_134148.png", "resolved_screenshot": "screenshots/screenshot_20250812_134148.png", "clean_action_id": "screenshot_20250812_134148", "prefixed_action_id": "al_screenshot_20250812_134148", "action_id_screenshot": "screenshots/screenshot_20250812_134148.png"}, {"name": "Tap on element with accessibility_id: delete", "status": "passed", "duration": "4839ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): \"3000\"", "status": "passed", "duration": "5269ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "3166ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "passed", "duration": "4649ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2922ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2487ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "1817ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "2306ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with text=\"Melbourne\" exists", "status": "passed", "duration": "14447ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2873ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5512ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2348ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "App Settings AU\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            68 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "1170ms", "action_id": "screenshot_20250812_140408", "screenshot_filename": "screenshot_20250812_140408.png", "report_screenshot": "screenshot_20250812_140408.png", "resolved_screenshot": "screenshots/screenshot_20250812_140408.png", "clean_action_id": "screenshot_20250812_140408", "prefixed_action_id": "al_screenshot_20250812_140408", "action_id_screenshot": "screenshots/screenshot_20250812_140408.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "status": "passed", "duration": "2437ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1122ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250812_143101", "screenshot_filename": "screenshot_20250812_143101.png", "report_screenshot": "screenshot_20250812_143101.png", "resolved_screenshot": "screenshots/screenshot_20250812_143101.png", "clean_action_id": "screenshot_20250812_143101", "prefixed_action_id": "al_screenshot_20250812_143101", "action_id_screenshot": "screenshots/screenshot_20250812_143101.png"}, {"name": "Terminate app: com.apple.Preferences", "status": "passed", "duration": "1244ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "1240ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Tap on Text: \"Wi-Fi\"", "status": "passed", "duration": "2213ms", "action_id": "screenshot_20250812_143115", "screenshot_filename": "screenshot_20250812_143115.png", "report_screenshot": "screenshot_20250812_143115.png", "resolved_screenshot": "screenshots/screenshot_20250812_143115.png", "clean_action_id": "screenshot_20250812_143115", "prefixed_action_id": "al_screenshot_20250812_143115", "action_id_screenshot": "screenshots/screenshot_20250812_143115.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5010ms", "action_id": "screenshot_20250812_140434", "screenshot_filename": "screenshot_20250812_140434.png", "report_screenshot": "screenshot_20250812_140434.png", "resolved_screenshot": "screenshots/screenshot_20250812_140434.png", "clean_action_id": "screenshot_20250812_140434", "prefixed_action_id": "al_screenshot_20250812_140434", "action_id_screenshot": "screenshots/screenshot_20250812_140434.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "1001ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5012ms", "action_id": "screenshot_20250812_144120", "screenshot_filename": "screenshot_20250812_144120.png", "report_screenshot": "screenshot_20250812_144120.png", "resolved_screenshot": "screenshots/screenshot_20250812_144120.png", "clean_action_id": "screenshot_20250812_144120", "prefixed_action_id": "al_screenshot_20250812_144120", "action_id_screenshot": "screenshots/screenshot_20250812_144120.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "2236ms", "action_id": "screenshot_20250812_141058", "screenshot_filename": "screenshot_20250812_141058.png", "report_screenshot": "screenshot_20250812_141058.png", "resolved_screenshot": "screenshots/screenshot_20250812_141058.png", "clean_action_id": "screenshot_20250812_141058", "prefixed_action_id": "al_screenshot_20250812_141058", "action_id_screenshot": "screenshots/screenshot_20250812_141058.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "243ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "737ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "192ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"3 of 5\")]", "status": "passed", "duration": "657ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "205ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "636ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "233ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "142ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5013ms", "action_id": "screenshot_20250812_144646", "screenshot_filename": "screenshot_20250812_144646.png", "report_screenshot": "screenshot_20250812_144646.png", "resolved_screenshot": "screenshots/screenshot_20250812_144646.png", "clean_action_id": "screenshot_20250812_144646", "prefixed_action_id": "al_screenshot_20250812_144646", "action_id_screenshot": "screenshots/screenshot_20250812_144646.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "762ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5009ms", "action_id": "screenshot_20250812_135054", "screenshot_filename": "screenshot_20250812_135054.png", "report_screenshot": "screenshot_20250812_135054.png", "resolved_screenshot": "screenshots/screenshot_20250812_135054.png", "clean_action_id": "screenshot_20250812_135054", "prefixed_action_id": "al_screenshot_20250812_135054", "action_id_screenshot": "screenshots/screenshot_20250812_135054.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "2235ms", "action_id": "screenshot_20250812_134438", "screenshot_filename": "screenshot_20250812_134438.png", "report_screenshot": "screenshot_20250812_134438.png", "resolved_screenshot": "screenshots/screenshot_20250812_134438.png", "clean_action_id": "screenshot_20250812_134438", "prefixed_action_id": "al_screenshot_20250812_134438", "action_id_screenshot": "screenshots/screenshot_20250812_134438.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "2754ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2747ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2736ms", "action_id": "screenshot_20250812_133419", "screenshot_filename": "screenshot_20250812_133419.png", "report_screenshot": "screenshot_20250812_133419.png", "resolved_screenshot": "screenshots/screenshot_20250812_133419.png", "clean_action_id": "screenshot_20250812_133419", "prefixed_action_id": "al_screenshot_20250812_133419", "action_id_screenshot": "screenshots/screenshot_20250812_133419.png"}, {"name": "Restart app: com.apple.mobilesafari", "status": "passed", "duration": "2254ms", "action_id": "mobilesafa", "screenshot_filename": "mobilesafa.png", "report_screenshot": "mobilesafa.png", "resolved_screenshot": "screenshots/mobilesafa.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]", "status": "passed", "duration": "1111ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"kmart au\"", "status": "passed", "duration": "1637ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "status": "passed", "duration": "1304ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]\" exists", "status": "passed", "duration": "1530ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "2428ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "2125ms", "action_id": "screenshot_20250812_141500", "screenshot_filename": "screenshot_20250812_141500.png", "report_screenshot": "screenshot_20250812_141500.png", "resolved_screenshot": "screenshots/screenshot_20250812_141500.png", "clean_action_id": "screenshot_20250812_141500", "prefixed_action_id": "al_screenshot_20250812_141500", "action_id_screenshot": "screenshots/screenshot_20250812_141500.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "2625ms", "action_id": "screenshot_20250812_143317", "screenshot_filename": "screenshot_20250812_143317.png", "report_screenshot": "screenshot_20250812_143317.png", "resolved_screenshot": "screenshots/screenshot_20250812_143317.png", "clean_action_id": "screenshot_20250812_143317", "prefixed_action_id": "al_screenshot_20250812_143317", "action_id_screenshot": "screenshots/screenshot_20250812_143317.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "21182ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "3135ms", "action_id": "screenshot_20250812_145028", "screenshot_filename": "screenshot_20250812_145028.png", "report_screenshot": "screenshot_20250812_145028.png", "resolved_screenshot": "screenshots/screenshot_20250812_145028.png", "clean_action_id": "screenshot_20250812_145028", "prefixed_action_id": "al_screenshot_20250812_145028", "action_id_screenshot": "screenshots/screenshot_20250812_145028.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "4200ms", "action_id": "screenshot_20250812_143459", "screenshot_filename": "screenshot_20250812_143459.png", "report_screenshot": "screenshot_20250812_143459.png", "resolved_screenshot": "screenshots/screenshot_20250812_143459.png", "clean_action_id": "screenshot_20250812_143459", "prefixed_action_id": "al_screenshot_20250812_143459", "action_id_screenshot": "screenshots/screenshot_20250812_143459.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2350ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3692ms", "action_id": "screenshot_20250812_134148", "screenshot_filename": "screenshot_20250812_134148.png", "report_screenshot": "screenshot_20250812_134148.png", "resolved_screenshot": "screenshots/screenshot_20250812_134148.png", "clean_action_id": "screenshot_20250812_134148", "prefixed_action_id": "al_screenshot_20250812_134148", "action_id_screenshot": "screenshots/screenshot_20250812_134148.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5467ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "2870ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeStaticText[@name=\"Chat now\"]/preceding-sibling::XCUIElementTypeImage[1]\"", "status": "passed", "duration": "11169ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "2687ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "21259ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "3136ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "4190ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2346ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "14865ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "2910ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3354ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Increase quantity\"]", "status": "passed", "duration": "4718ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Decrease quantity\"]", "status": "passed", "duration": "8385ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "7107ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2571ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"1 of 5\")]", "status": "passed", "duration": "2314ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]\" is visible", "status": "passed", "duration": "4873ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Home & Living\"]", "status": "passed", "duration": "2482ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "status": "passed", "duration": "2475ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2616ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "6666ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap if locator exists: accessibility_id=\"Add to bag\"", "status": "passed", "duration": "6771ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "2268ms", "action_id": "screenshot_20250812_141306", "screenshot_filename": "screenshot_20250812_141306.png", "report_screenshot": "screenshot_20250812_141306.png", "resolved_screenshot": "screenshots/screenshot_20250812_141306.png", "clean_action_id": "screenshot_20250812_141306", "prefixed_action_id": "al_screenshot_20250812_141306", "action_id_screenshot": "screenshots/screenshot_20250812_141306.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "3337ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3433ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "7609ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[contains(@name,\"Remove\")]\"", "status": "passed", "duration": "6071ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5005ms", "action_id": "screenshot_20250812_144242", "screenshot_filename": "screenshot_20250812_144242.png", "report_screenshot": "screenshot_20250812_144242.png", "resolved_screenshot": "screenshots/screenshot_20250812_144242.png", "clean_action_id": "screenshot_20250812_144242", "prefixed_action_id": "al_screenshot_20250812_144242", "action_id_screenshot": "screenshots/screenshot_20250812_144242.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "AU - Performance\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            69 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "1173ms", "action_id": "screenshot_20250812_140408", "screenshot_filename": "screenshot_20250812_140408.png", "report_screenshot": "screenshot_20250812_140408.png", "resolved_screenshot": "screenshots/screenshot_20250812_140408.png", "clean_action_id": "screenshot_20250812_140408", "prefixed_action_id": "al_screenshot_20250812_140408", "action_id_screenshot": "screenshots/screenshot_20250812_140408.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2629ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeStaticText[@name=\"Chat now\"]/preceding-sibling::XCUIElementTypeImage[1]\"", "status": "passed", "duration": "11132ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Help\"", "status": "passed", "duration": "2734ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on Text: \"FAQ\"", "status": "passed", "duration": "2565ms", "action_id": "screenshot_20250812_143101", "screenshot_filename": "screenshot_20250812_143101.png", "report_screenshot": "screenshot_20250812_143101.png", "resolved_screenshot": "screenshots/screenshot_20250812_143101.png", "clean_action_id": "screenshot_20250812_143101", "prefixed_action_id": "al_screenshot_20250812_143101", "action_id_screenshot": "screenshots/screenshot_20250812_143101.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "18640ms", "action_id": "screenshot_20250812_143115", "screenshot_filename": "screenshot_20250812_143115.png", "report_screenshot": "screenshot_20250812_143115.png", "resolved_screenshot": "screenshots/screenshot_20250812_143115.png", "clean_action_id": "screenshot_20250812_143115", "prefixed_action_id": "al_screenshot_20250812_143115", "action_id_screenshot": "screenshots/screenshot_20250812_143115.png"}, {"name": "Tap on Text: \"click\"", "status": "passed", "duration": "2987ms", "action_id": "screenshot_20250812_140434", "screenshot_filename": "screenshot_20250812_140434.png", "report_screenshot": "screenshot_20250812_140434.png", "resolved_screenshot": "screenshots/screenshot_20250812_140434.png", "clean_action_id": "screenshot_20250812_140434", "prefixed_action_id": "al_screenshot_20250812_140434", "action_id_screenshot": "screenshots/screenshot_20250812_140434.png"}, {"name": "Tap on Text: \"1800\"", "status": "passed", "duration": "2771ms", "action_id": "screenshot_20250812_144120", "screenshot_filename": "screenshot_20250812_144120.png", "report_screenshot": "screenshot_20250812_144120.png", "resolved_screenshot": "screenshots/screenshot_20250812_144120.png", "clean_action_id": "screenshot_20250812_144120", "prefixed_action_id": "al_screenshot_20250812_144120", "action_id_screenshot": "screenshots/screenshot_20250812_144120.png"}, {"name": "Tap on Text: \"+61\"", "status": "passed", "duration": "2295ms", "action_id": "screenshot_20250812_141058", "screenshot_filename": "screenshot_20250812_141058.png", "report_screenshot": "screenshot_20250812_141058.png", "resolved_screenshot": "screenshots/screenshot_20250812_141058.png", "clean_action_id": "screenshot_20250812_141058", "prefixed_action_id": "al_screenshot_20250812_141058", "action_id_screenshot": "screenshots/screenshot_20250812_141058.png"}, {"name": "Launch app: env[appid]", "status": "passed", "duration": "116ms", "action_id": "screenshot_20250812_144646", "screenshot_filename": "screenshot_20250812_144646.png", "report_screenshot": "screenshot_20250812_144646.png", "resolved_screenshot": "screenshots/screenshot_20250812_144646.png", "clean_action_id": "screenshot_20250812_144646", "prefixed_action_id": "al_screenshot_20250812_144646", "action_id_screenshot": "screenshots/screenshot_20250812_144646.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2690ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3537ms", "action_id": "screenshot_20250812_135054", "screenshot_filename": "screenshot_20250812_135054.png", "report_screenshot": "screenshot_20250812_135054.png", "resolved_screenshot": "screenshots/screenshot_20250812_135054.png", "clean_action_id": "screenshot_20250812_135054", "prefixed_action_id": "al_screenshot_20250812_135054", "action_id_screenshot": "screenshots/screenshot_20250812_135054.png"}, {"name": "iOS Function: text - Text: \"kids toys\"", "status": "passed", "duration": "2606ms", "action_id": "screenshot_20250812_134438", "screenshot_filename": "screenshot_20250812_134438.png", "report_screenshot": "screenshot_20250812_134438.png", "resolved_screenshot": "screenshots/screenshot_20250812_134438.png", "clean_action_id": "screenshot_20250812_134438", "prefixed_action_id": "al_screenshot_20250812_134438", "action_id_screenshot": "screenshots/screenshot_20250812_134438.png"}, {"name": "Execute Test Case: Click_Paginations (8 steps)", "status": "passed", "duration": "0ms", "action_id": "Pagination", "screenshot_filename": "Pagination.png", "report_screenshot": "Pagination.png", "resolved_screenshot": "screenshots/Pagination.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "2644ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2679ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2600ms", "action_id": "screenshot_20250812_133419", "screenshot_filename": "screenshot_20250812_133419.png", "report_screenshot": "screenshot_20250812_133419.png", "resolved_screenshot": "screenshots/screenshot_20250812_133419.png", "clean_action_id": "screenshot_20250812_133419", "prefixed_action_id": "al_screenshot_20250812_133419", "action_id_screenshot": "screenshots/screenshot_20250812_133419.png"}, {"name": "Tap on Text: \"Age\"", "status": "passed", "duration": "2669ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on Text: \"Months\"", "status": "passed", "duration": "2522ms", "action_id": "screenshot_20250812_141500", "screenshot_filename": "screenshot_20250812_141500.png", "report_screenshot": "screenshot_20250812_141500.png", "resolved_screenshot": "screenshots/screenshot_20250812_141500.png", "clean_action_id": "screenshot_20250812_141500", "prefixed_action_id": "al_screenshot_20250812_141500", "action_id_screenshot": "screenshots/screenshot_20250812_141500.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "3975ms", "action_id": "screenshot_20250812_143317", "screenshot_filename": "screenshot_20250812_143317.png", "report_screenshot": "screenshot_20250812_143317.png", "resolved_screenshot": "screenshots/screenshot_20250812_143317.png", "clean_action_id": "screenshot_20250812_143317", "prefixed_action_id": "al_screenshot_20250812_143317", "action_id_screenshot": "screenshots/screenshot_20250812_143317.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "3768ms", "action_id": "screenshot_20250812_145028", "screenshot_filename": "screenshot_20250812_145028.png", "report_screenshot": "screenshot_20250812_145028.png", "resolved_screenshot": "screenshots/screenshot_20250812_145028.png", "clean_action_id": "screenshot_20250812_145028", "prefixed_action_id": "al_screenshot_20250812_145028", "action_id_screenshot": "screenshots/screenshot_20250812_145028.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2284ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4389ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1180ms", "action_id": "screenshot_20250812_143459", "screenshot_filename": "screenshot_20250812_143459.png", "report_screenshot": "screenshot_20250812_143459.png", "resolved_screenshot": "screenshots/screenshot_20250812_143459.png", "clean_action_id": "screenshot_20250812_143459", "prefixed_action_id": "al_screenshot_20250812_143459", "action_id_screenshot": "screenshots/screenshot_20250812_143459.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2688ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3277ms", "action_id": "screenshot_20250812_134148", "screenshot_filename": "screenshot_20250812_134148.png", "report_screenshot": "screenshot_20250812_134148.png", "resolved_screenshot": "screenshots/screenshot_20250812_134148.png", "clean_action_id": "screenshot_20250812_134148", "prefixed_action_id": "al_screenshot_20250812_134148", "action_id_screenshot": "screenshots/screenshot_20250812_134148.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2698ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3004ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "4153ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3405ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "iOS Function: text - Text: \"env[cooker-id]\"", "status": "passed", "duration": "2595ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2150ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2773ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "3040ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 40%)", "status": "passed", "duration": "4358ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[contains(@name,\"Select to add\") and contains(@name,\"to wishlist\")]\"", "status": "passed", "duration": "15309ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2978ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (90%, 30%) to (30%, 30%)", "status": "passed", "duration": "2117ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Swipe from (90%, 30%) to (30%, 30%)", "status": "passed", "duration": "2136ms", "action_id": "screenshot_20250812_141306", "screenshot_filename": "screenshot_20250812_141306.png", "report_screenshot": "screenshot_20250812_141306.png", "resolved_screenshot": "screenshots/screenshot_20250812_141306.png", "clean_action_id": "screenshot_20250812_141306", "prefixed_action_id": "al_screenshot_20250812_141306", "action_id_screenshot": "screenshots/screenshot_20250812_141306.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2255ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2210ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2612ms", "action_id": "screenshot_20250812_144242", "screenshot_filename": "screenshot_20250812_144242.png", "report_screenshot": "screenshot_20250812_144242.png", "resolved_screenshot": "screenshots/screenshot_20250812_144242.png", "clean_action_id": "screenshot_20250812_144242", "prefixed_action_id": "al_screenshot_20250812_144242", "action_id_screenshot": "screenshots/screenshot_20250812_144242.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2480ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtJoinTodayButton\"]", "status": "passed", "duration": "2473ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1216ms", "action_id": "qcZQIqosdj", "screenshot_filename": "qcZQIqosdj.png", "report_screenshot": "qcZQIqosdj.png", "resolved_screenshot": "screenshots/qcZQIqosdj.png", "clean_action_id": "qcZQIqosdj", "prefixed_action_id": "al_qcZQIqosdj", "action_id_screenshot": "screenshots/qcZQIqosdj.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Create account\"]\" exists", "status": "passed", "duration": "1640ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Cancel\"", "status": "passed", "duration": "3312ms", "action_id": "screenshot_20250812_135322", "screenshot_filename": "screenshot_20250812_135322.png", "report_screenshot": "screenshot_20250812_135322.png", "resolved_screenshot": "screenshots/screenshot_20250812_135322.png", "clean_action_id": "screenshot_20250812_135322", "prefixed_action_id": "al_screenshot_20250812_135322", "action_id_screenshot": "screenshots/screenshot_20250812_135322.png"}, {"name": "Wait till accessibility_id=txtHomeAccountCtaSignIn", "status": "passed", "duration": "3419ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Execute Test Case: Search and Add (Notebooks) (6 steps)", "status": "passed", "duration": "0ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on Text: \"Click\"", "status": "passed", "duration": "3076ms", "action_id": "screenshot_20250812_135336", "screenshot_filename": "screenshot_20250812_135336.png", "report_screenshot": "screenshot_20250812_135336.png", "resolved_screenshot": "screenshots/screenshot_20250812_135336.png", "clean_action_id": "screenshot_20250812_135336", "prefixed_action_id": "al_screenshot_20250812_135336", "action_id_screenshot": "screenshots/screenshot_20250812_135336.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,\"store details\")])[1]", "status": "passed", "duration": "2669ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2636ms", "action_id": "rWuyGodCon", "screenshot_filename": "rWuyGodCon.png", "report_screenshot": "rWuyGodCon.png", "resolved_screenshot": "screenshots/rWuyGodCon.png", "clean_action_id": "rWuyGodCon", "prefixed_action_id": "al_rWuyGodCon", "action_id_screenshot": "screenshots/rWuyGodCon.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]\" is visible", "status": "passed", "duration": "16899ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]", "status": "passed", "duration": "2505ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2290ms", "action_id": "screenshot_20250812_142828", "screenshot_filename": "screenshot_20250812_142828.png", "report_screenshot": "screenshot_20250812_142828.png", "resolved_screenshot": "screenshots/screenshot_20250812_142828.png", "clean_action_id": "screenshot_20250812_142828", "prefixed_action_id": "al_screenshot_20250812_142828", "action_id_screenshot": "screenshots/screenshot_20250812_142828.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"About KHub Stores\"]", "status": "passed", "duration": "2470ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2267ms", "action_id": "screenshot_20250812_132854", "screenshot_filename": "screenshot_20250812_132854.png", "report_screenshot": "screenshot_20250812_132854.png", "resolved_screenshot": "screenshots/screenshot_20250812_132854.png", "clean_action_id": "screenshot_20250812_132854", "prefixed_action_id": "al_screenshot_20250812_132854", "action_id_screenshot": "screenshots/screenshot_20250812_132854.png"}, {"name": "Swipe from (50%, 30%) to (50%, 70%)", "status": "passed", "duration": "2977ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "14140ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2285ms", "action_id": "screenshot_20250812_143707", "screenshot_filename": "screenshot_20250812_143707.png", "report_screenshot": "screenshot_20250812_143707.png", "resolved_screenshot": "screenshots/screenshot_20250812_143707.png", "clean_action_id": "screenshot_20250812_143707", "prefixed_action_id": "al_screenshot_20250812_143707", "action_id_screenshot": "screenshots/screenshot_20250812_143707.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2239ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\"", "status": "passed", "duration": "11021ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Remove item\"]\"", "status": "passed", "duration": "10996ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2372ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountJoinTodayButton\"]", "status": "passed", "duration": "2350ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1190ms", "action_id": "screenshot_20250812_132840", "screenshot_filename": "screenshot_20250812_132840.png", "report_screenshot": "screenshot_20250812_132840.png", "resolved_screenshot": "screenshots/screenshot_20250812_132840.png", "clean_action_id": "screenshot_20250812_132840", "prefixed_action_id": "al_screenshot_20250812_132840", "action_id_screenshot": "screenshots/screenshot_20250812_132840.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Create account\"]\" exists", "status": "passed", "duration": "1679ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1041ms", "action_id": "screenshot_20250812_144726", "screenshot_filename": "screenshot_20250812_144726.png", "report_screenshot": "screenshot_20250812_144726.png", "resolved_screenshot": "screenshots/screenshot_20250812_144726.png", "clean_action_id": "screenshot_20250812_144726", "prefixed_action_id": "al_screenshot_20250812_144726", "action_id_screenshot": "screenshots/screenshot_20250812_144726.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "All Payments Check\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            57 actions", "status": "passed", "steps": [{"name": "Terminate app: au.com.kmart", "status": "passed", "duration": "30ms", "action_id": "screenshot_20250812_140408", "screenshot_filename": "screenshot_20250812_140408.png", "report_screenshot": "screenshot_20250812_140408.png", "resolved_screenshot": "screenshots/screenshot_20250812_140408.png", "clean_action_id": "screenshot_20250812_140408", "prefixed_action_id": "al_screenshot_20250812_140408", "action_id_screenshot": "screenshots/screenshot_20250812_140408.png"}, {"name": "Restart app: au.com.kmart", "status": "passed", "duration": "1165ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5418ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1163ms", "action_id": "screenshot_20250812_143101", "screenshot_filename": "screenshot_20250812_143101.png", "report_screenshot": "screenshot_20250812_143101.png", "resolved_screenshot": "screenshots/screenshot_20250812_143101.png", "clean_action_id": "screenshot_20250812_143101", "prefixed_action_id": "al_screenshot_20250812_143101", "action_id_screenshot": "screenshots/screenshot_20250812_143101.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250812_143115", "screenshot_filename": "screenshot_20250812_143115.png", "report_screenshot": "screenshot_20250812_143115.png", "resolved_screenshot": "screenshots/screenshot_20250812_143115.png", "clean_action_id": "screenshot_20250812_143115", "prefixed_action_id": "al_screenshot_20250812_143115", "action_id_screenshot": "screenshots/screenshot_20250812_143115.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "10973ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3392ms", "action_id": "screenshot_20250812_140434", "screenshot_filename": "screenshot_20250812_140434.png", "report_screenshot": "screenshot_20250812_140434.png", "resolved_screenshot": "screenshots/screenshot_20250812_140434.png", "clean_action_id": "screenshot_20250812_140434", "prefixed_action_id": "al_screenshot_20250812_140434", "action_id_screenshot": "screenshots/screenshot_20250812_140434.png"}, {"name": "iOS Function: text - Text: \"P_42691341\"", "status": "passed", "duration": "2638ms", "action_id": "screenshot_20250812_144120", "screenshot_filename": "screenshot_20250812_144120.png", "report_screenshot": "screenshot_20250812_144120.png", "resolved_screenshot": "screenshots/screenshot_20250812_144120.png", "clean_action_id": "screenshot_20250812_144120", "prefixed_action_id": "al_screenshot_20250812_144120", "action_id_screenshot": "screenshots/screenshot_20250812_144120.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1833ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2427ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 50%)", "status": "passed", "duration": "3419ms", "action_id": "screenshot_20250812_141058", "screenshot_filename": "screenshot_20250812_141058.png", "report_screenshot": "screenshot_20250812_141058.png", "resolved_screenshot": "screenshots/screenshot_20250812_141058.png", "clean_action_id": "screenshot_20250812_141058", "prefixed_action_id": "al_screenshot_20250812_141058", "action_id_screenshot": "screenshots/screenshot_20250812_141058.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5540ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2860ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3374ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2100ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2506ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Continue to details\"]\" is visible", "status": "passed", "duration": "26650ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Continue to details\"]", "status": "passed", "duration": "2489ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"First Name\"]", "status": "passed", "duration": "2381ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5097ms", "action_id": "screenshot_20250812_144646", "screenshot_filename": "screenshot_20250812_144646.png", "report_screenshot": "screenshot_20250812_144646.png", "resolved_screenshot": "screenshots/screenshot_20250812_144646.png", "clean_action_id": "screenshot_20250812_144646", "prefixed_action_id": "al_screenshot_20250812_144646", "action_id_screenshot": "screenshots/screenshot_20250812_144646.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Last Name\"]", "status": "passed", "duration": "2503ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5240ms", "action_id": "screenshot_20250812_135054", "screenshot_filename": "screenshot_20250812_135054.png", "report_screenshot": "screenshot_20250812_135054.png", "resolved_screenshot": "screenshots/screenshot_20250812_135054.png", "clean_action_id": "screenshot_20250812_135054", "prefixed_action_id": "al_screenshot_20250812_135054", "action_id_screenshot": "screenshots/screenshot_20250812_135054.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2565ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5893ms", "action_id": "screenshot_20250812_134438", "screenshot_filename": "screenshot_20250812_134438.png", "report_screenshot": "screenshot_20250812_134438.png", "resolved_screenshot": "screenshots/screenshot_20250812_134438.png", "clean_action_id": "screenshot_20250812_134438", "prefixed_action_id": "al_screenshot_20250812_134438", "action_id_screenshot": "screenshots/screenshot_20250812_134438.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Mobile number\"]", "status": "passed", "duration": "2577ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5242ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "iOS Function: text - Text: \" \"", "status": "passed", "duration": "2565ms", "action_id": "screenshot_20250812_133419", "screenshot_filename": "screenshot_20250812_133419.png", "report_screenshot": "screenshot_20250812_133419.png", "resolved_screenshot": "screenshots/screenshot_20250812_133419.png", "clean_action_id": "screenshot_20250812_133419", "prefixed_action_id": "al_screenshot_20250812_133419", "action_id_screenshot": "screenshots/screenshot_20250812_133419.png"}, {"name": "Tap on Text: \"address\"", "status": "passed", "duration": "3399ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap and Type at (54, 304): \"305 238 Flinders\"", "status": "passed", "duration": "5700ms", "action_id": "screenshot_20250812_141500", "screenshot_filename": "screenshot_20250812_141500.png", "report_screenshot": "screenshot_20250812_141500.png", "resolved_screenshot": "screenshots/screenshot_20250812_141500.png", "clean_action_id": "screenshot_20250812_141500", "prefixed_action_id": "al_screenshot_20250812_141500", "action_id_screenshot": "screenshots/screenshot_20250812_141500.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"<PERSON>\"]", "status": "passed", "duration": "2512ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[delivery-address-img]", "status": "passed", "duration": "2299ms", "action_id": "screenshot_20250812_143317", "screenshot_filename": "screenshot_20250812_143317.png", "report_screenshot": "screenshot_20250812_143317.png", "resolved_screenshot": "screenshots/screenshot_20250812_143317.png", "clean_action_id": "screenshot_20250812_143317", "prefixed_action_id": "al_screenshot_20250812_143317", "action_id_screenshot": "screenshots/screenshot_20250812_143317.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Continue to payment\"]\" is visible", "status": "passed", "duration": "6287ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Continue to payment\"]", "status": "passed", "duration": "2386ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther", "status": "passed", "duration": "6630ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeLink[@name=\"PayPal\"]\" is visible", "status": "passed", "duration": "6402ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"PayPal\"]", "status": "passed", "duration": "2423ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[contains(@name,\"PayPal\")]\" exists", "status": "passed", "duration": "1673ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"close\"]", "status": "passed", "duration": "2506ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther", "status": "passed", "duration": "2418ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"Pay in 4\"]", "status": "passed", "duration": "2454ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Pay in 4 with PayPal\"]\" exists", "status": "passed", "duration": "1556ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"close\"]", "status": "passed", "duration": "2575ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther", "status": "passed", "duration": "2465ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Afterpay, available on orders between $70-$2,000.\"]", "status": "passed", "duration": "2474ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeImage[@name=\"Afterpay\"]\" exists", "status": "passed", "duration": "4168ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2284ms", "action_id": "screenshot_20250812_145028", "screenshot_filename": "screenshot_20250812_145028.png", "report_screenshot": "screenshot_20250812_145028.png", "resolved_screenshot": "screenshots/screenshot_20250812_145028.png", "clean_action_id": "screenshot_20250812_145028", "prefixed_action_id": "al_screenshot_20250812_145028", "action_id_screenshot": "screenshots/screenshot_20250812_145028.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther", "status": "passed", "duration": "2438ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Afterpay, available on orders between $70-$2,000.\"]", "status": "passed", "duration": "2439ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Sign in with your Zip account\"]\" exists", "status": "passed", "duration": "1553ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2295ms", "action_id": "screenshot_20250812_143459", "screenshot_filename": "screenshot_20250812_143459.png", "report_screenshot": "screenshot_20250812_143459.png", "resolved_screenshot": "screenshots/screenshot_20250812_143459.png", "clean_action_id": "screenshot_20250812_143459", "prefixed_action_id": "al_screenshot_20250812_143459", "action_id_screenshot": "screenshots/screenshot_20250812_143459.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2360ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3405ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "3347ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"Remove\")]\" is visible", "status": "passed", "duration": "5041ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2513ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "2300ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Kmart_AU_Cleanup (6 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250812_134148", "screenshot_filename": "screenshot_20250812_134148.png", "report_screenshot": "screenshot_20250812_134148.png", "resolved_screenshot": "screenshots/screenshot_20250812_134148.png", "clean_action_id": "screenshot_20250812_134148", "prefixed_action_id": "al_screenshot_20250812_134148", "action_id_screenshot": "screenshots/screenshot_20250812_134148.png"}]}, {"name": "Browse & PDP\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            56 actions", "status": "failed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2229ms", "action_id": "screenshot_20250812_140408", "screenshot_filename": "screenshot_20250812_140408.png", "report_screenshot": "screenshot_20250812_140408.png", "resolved_screenshot": "screenshots/screenshot_20250812_140408.png", "clean_action_id": "screenshot_20250812_140408", "prefixed_action_id": "al_screenshot_20250812_140408", "action_id_screenshot": "screenshots/screenshot_20250812_140408.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "3638ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1797ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2295ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeImage[@name=\"More\"]\" exists", "status": "passed", "duration": "1481ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Scan barcode\"]\" exists", "status": "passed", "duration": "1475ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2473ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2615ms", "action_id": "screenshot_20250812_143101", "screenshot_filename": "screenshot_20250812_143101.png", "report_screenshot": "screenshot_20250812_143101.png", "resolved_screenshot": "screenshots/screenshot_20250812_143101.png", "clean_action_id": "screenshot_20250812_143101", "prefixed_action_id": "al_screenshot_20250812_143101", "action_id_screenshot": "screenshots/screenshot_20250812_143101.png"}, {"name": "Tap on Text: \"Latest\"", "status": "passed", "duration": "2640ms", "action_id": "screenshot_20250812_143115", "screenshot_filename": "screenshot_20250812_143115.png", "report_screenshot": "screenshot_20250812_143115.png", "resolved_screenshot": "screenshots/screenshot_20250812_143115.png", "clean_action_id": "screenshot_20250812_143115", "prefixed_action_id": "al_screenshot_20250812_143115", "action_id_screenshot": "screenshots/screenshot_20250812_143115.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2078ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2646ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Unchecked In stock only items\"]", "status": "passed", "duration": "2350ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5012ms", "action_id": "screenshot_20250812_140434", "screenshot_filename": "screenshot_20250812_140434.png", "report_screenshot": "screenshot_20250812_140434.png", "resolved_screenshot": "screenshots/screenshot_20250812_140434.png", "clean_action_id": "screenshot_20250812_140434", "prefixed_action_id": "al_screenshot_20250812_140434", "action_id_screenshot": "screenshots/screenshot_20250812_140434.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Show (\")]", "status": "passed", "duration": "2363ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]\" exists", "status": "passed", "duration": "1642ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]", "status": "passed", "duration": "2622ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3215ms", "action_id": "screenshot_20250812_144120", "screenshot_filename": "screenshot_20250812_144120.png", "report_screenshot": "screenshot_20250812_144120.png", "resolved_screenshot": "screenshots/screenshot_20250812_144120.png", "clean_action_id": "screenshot_20250812_144120", "prefixed_action_id": "al_screenshot_20250812_144120", "action_id_screenshot": "screenshots/screenshot_20250812_144120.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10012ms", "action_id": "screenshot_20250812_141058", "screenshot_filename": "screenshot_20250812_141058.png", "report_screenshot": "screenshot_20250812_141058.png", "resolved_screenshot": "screenshots/screenshot_20250812_141058.png", "clean_action_id": "screenshot_20250812_141058", "prefixed_action_id": "al_screenshot_20250812_141058", "action_id_screenshot": "screenshots/screenshot_20250812_141058.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2131ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2662ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[product-share-img]", "status": "passed", "duration": "3354ms", "action_id": "screenshot_20250812_144646", "screenshot_filename": "screenshot_20250812_144646.png", "report_screenshot": "screenshot_20250812_144646.png", "resolved_screenshot": "screenshots/screenshot_20250812_144646.png", "clean_action_id": "screenshot_20250812_144646", "prefixed_action_id": "al_screenshot_20250812_144646", "action_id_screenshot": "screenshots/screenshot_20250812_144646.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"Check out \")]\" exists", "status": "passed", "duration": "1902ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2918ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Wait for 3 ms", "status": "passed", "duration": "3015ms", "action_id": "screenshot_20250812_135054", "screenshot_filename": "screenshot_20250812_135054.png", "report_screenshot": "screenshot_20250812_135054.png", "resolved_screenshot": "screenshots/screenshot_20250812_135054.png", "clean_action_id": "screenshot_20250812_135054", "prefixed_action_id": "al_screenshot_20250812_135054", "action_id_screenshot": "screenshots/screenshot_20250812_135054.png"}, {"name": "Swipe up till element accessibility_id: \"Learn more about AfterPay\" is visible", "status": "passed", "duration": "7015ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Learn more about AfterPay", "status": "passed", "duration": "4849ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Afterpay – Now available in store\"]\" exists", "status": "unknown", "duration": "13773ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2361ms", "action_id": "screenshot_20250812_134438", "screenshot_filename": "screenshot_20250812_134438.png", "report_screenshot": "screenshot_20250812_134438.png", "resolved_screenshot": "screenshots/screenshot_20250812_134438.png", "clean_action_id": "screenshot_20250812_134438", "prefixed_action_id": "al_screenshot_20250812_134438", "action_id_screenshot": "screenshots/screenshot_20250812_134438.png"}, {"name": "Tap on element with accessibility_id: Learn more about Zip", "status": "passed", "duration": "5254ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"What is Zip?\"]\" exists", "status": "passed", "duration": "3299ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2419ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with accessibility_id: Learn more about PayPal Pay in 4", "status": "passed", "duration": "5751ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[paypal-close-img]", "status": "passed", "duration": "2570ms", "action_id": "screenshot_20250812_133419", "screenshot_filename": "screenshot_20250812_133419.png", "report_screenshot": "screenshot_20250812_133419.png", "resolved_screenshot": "screenshots/screenshot_20250812_133419.png", "clean_action_id": "screenshot_20250812_133419", "prefixed_action_id": "al_screenshot_20250812_133419", "action_id_screenshot": "screenshots/screenshot_20250812_133419.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Shop at\"]/following-sibling::XCUIElementTypeButton", "status": "passed", "duration": "2963ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPostCodeSelectionScreenHeader\"]\" exists", "status": "passed", "duration": "1847ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2324ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "2217ms", "action_id": "screenshot_20250812_141500", "screenshot_filename": "screenshot_20250812_141500.png", "report_screenshot": "screenshot_20250812_141500.png", "resolved_screenshot": "screenshots/screenshot_20250812_141500.png", "clean_action_id": "screenshot_20250812_141500", "prefixed_action_id": "al_screenshot_20250812_141500", "action_id_screenshot": "screenshots/screenshot_20250812_141500.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "3683ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2315ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Kid toy\"", "status": "passed", "duration": "2561ms", "action_id": "screenshot_20250812_143317", "screenshot_filename": "screenshot_20250812_143317.png", "report_screenshot": "screenshot_20250812_143317.png", "resolved_screenshot": "screenshots/screenshot_20250812_143317.png", "clean_action_id": "screenshot_20250812_143317", "prefixed_action_id": "al_screenshot_20250812_143317", "action_id_screenshot": "screenshots/screenshot_20250812_143317.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "5037ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "3104ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "3043ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2452ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"mat\"", "status": "passed", "duration": "2504ms", "action_id": "screenshot_20250812_145028", "screenshot_filename": "screenshot_20250812_145028.png", "report_screenshot": "screenshot_20250812_145028.png", "resolved_screenshot": "screenshots/screenshot_20250812_145028.png", "clean_action_id": "screenshot_20250812_145028", "prefixed_action_id": "al_screenshot_20250812_145028", "action_id_screenshot": "screenshots/screenshot_20250812_145028.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "4598ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "3055ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "3148ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "18529ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3654ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3681ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 20%)", "status": "passed", "duration": "10754ms", "action_id": "screenshot_20250812_143459", "screenshot_filename": "screenshot_20250812_143459.png", "report_screenshot": "screenshot_20250812_143459.png", "resolved_screenshot": "screenshots/screenshot_20250812_143459.png", "clean_action_id": "screenshot_20250812_143459", "prefixed_action_id": "al_screenshot_20250812_143459", "action_id_screenshot": "screenshots/screenshot_20250812_143459.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "3032ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "failed", "duration": "6323ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "unknown", "duration": "1076ms", "action_id": "screenshot_20250812_134148", "screenshot_filename": "screenshot_20250812_134148.png", "report_screenshot": "screenshot_20250812_134148.png", "resolved_screenshot": "screenshots/screenshot_20250812_134148.png", "clean_action_id": "screenshot_20250812_134148", "prefixed_action_id": "al_screenshot_20250812_134148", "action_id_screenshot": "screenshots/screenshot_20250812_134148.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}], "passed": 9, "failed": 2, "skipped": 0, "status": "failed"}