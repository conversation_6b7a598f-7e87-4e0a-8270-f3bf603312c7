{"name": "Sample Test with Cleanup", "description": "Test case demonstrating multiStep cleanup functionality", "actions": [{"action_id": "step1", "type": "tap", "locator_type": "id", "locator_value": "login_button", "timestamp": 1640995200000}, {"action_id": "step2", "type": "multiStep", "test_case_id": "login_flow", "test_case_name": "Login Flow", "test_case_steps_count": 3, "cleanup": false, "timestamp": 1640995210000}, {"action_id": "step3", "type": "tap", "locator_type": "xpath", "locator_value": "//button[@text='Submit']", "timestamp": 1640995220000}, {"action_id": "step4", "type": "multiStep", "test_case_id": "cleanup_session", "test_case_name": "Cleanup Session", "test_case_steps_count": 2, "cleanup": true, "timestamp": 1640995230000}]}