#!/usr/bin/env python3
"""
Manually set the correct active environments based on the database investigation
"""

import sys
import os

# Add both app directories to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

def set_ios_active_environment():
    """Set iOS active environment to ID 7 (AU-PROD-IP14)"""
    print("=== SETTING iOS ACTIVE ENVIRONMENT ===")
    
    from utils.directory_paths_db import directory_paths_db as ios_db
    
    # Set environment ID 7 (AU-PROD-IP14) as active for iOS
    target_env_id = 7
    print(f"Setting iOS active environment to ID {target_env_id}")
    
    success = ios_db.set_active_environment(target_env_id)
    if success:
        print("✓ iOS active environment set to ID 7 (AU-PROD-IP14)")
        
        # Verify the setting
        active_env = ios_db.get_active_environment()
        if active_env == target_env_id:
            print(f"✓ Verified: Active environment is now {active_env}")
        else:
            print(f"⚠ Warning: Active environment is {active_env}, expected {target_env_id}")
    else:
        print("✗ Failed to set iOS active environment")

def set_android_active_environment():
    """Set Android active environment to ID 10 (AU-PROD-ANDROID)"""
    print("\n=== SETTING ANDROID ACTIVE ENVIRONMENT ===")
    
    from app_android.utils.directory_paths_db import directory_paths_db as android_db
    
    # Set environment ID 10 (AU-PROD-ANDROID) as active for Android
    target_env_id = 10
    print(f"Setting Android active environment to ID {target_env_id}")
    
    try:
        success = android_db.set_active_environment(target_env_id)
        if success:
            print("✓ Android active environment set to ID 10 (AU-PROD-ANDROID)")
        else:
            print("✗ Failed to set Android active environment")
    except AttributeError:
        print("⚠ Android database doesn't support active environment setting")

def main():
    """Main function to set active environments"""
    print("SETTING CORRECT ACTIVE ENVIRONMENTS\n")
    
    set_ios_active_environment()
    set_android_active_environment()
    
    print("\n=== SUMMARY ===")
    print("✓ iOS active environment: ID 7 (AU-PROD-IP14)")
    print("✓ Android active environment: ID 10 (AU-PROD-ANDROID)")
    print("\nNow run: python3 final_env_test.py")
    print("This will test the complete environment variable resolution system")

if __name__ == "__main__":
    main()
