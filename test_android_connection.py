#!/usr/bin/env python3
"""
Test script to verify Android device connection after element identification fixes
"""

import sys
import os
import time
import logging

# Add the app_android directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_android_device_connection():
    """Test Android device connection with the enhanced element identification fixes"""
    
    print("=" * 80)
    print("Android Device Connection Test")
    print("=" * 80)
    
    try:
        # Test 1: Check ADB connection
        print("\n1. Testing ADB connection...")
        import subprocess
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
        if result.returncode == 0:
            devices = [line for line in result.stdout.split('\n') if 'device' in line and 'List' not in line]
            if devices:
                print(f"✓ ADB devices found: {len(devices)}")
                for device in devices:
                    print(f"  - {device.strip()}")
            else:
                print("✗ No ADB devices found")
                return False
        else:
            print(f"✗ ADB command failed: {result.stderr}")
            return False
        
        # Test 2: Check Appium server
        print("\n2. Testing Appium server connection...")
        import requests
        try:
            response = requests.get("http://127.0.0.1:4724/wd/hub/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                print(f"✓ Appium server is running: {status['value']['message']}")
            else:
                print(f"✗ Appium server returned status {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ Appium server connection failed: {e}")
            return False
        
        # Test 3: Test AppiumDeviceController initialization
        print("\n3. Testing AppiumDeviceController initialization...")
        from app_android.utils.appium_device_controller import AppiumDeviceController
        
        controller = AppiumDeviceController()
        print("✓ AppiumDeviceController created successfully")
        
        # Test 4: Test device connection
        print("\n4. Testing device connection...")
        device_id = "PJTCI7EMSSONYPU8"  # Your Android device ID
        
        success = controller.connect_to_device(device_id, platform='Android')
        
        if success:
            print("✓ Device connection successful")
            
            # Test 5: Test basic driver functionality
            print("\n5. Testing basic driver functionality...")
            if controller.driver:
                try:
                    # Get device info
                    capabilities = controller.driver.capabilities
                    print(f"✓ Device capabilities retrieved:")
                    print(f"  - Platform: {capabilities.get('platformName')}")
                    print(f"  - Device: {capabilities.get('deviceName')}")
                    print(f"  - Automation: {capabilities.get('automationName')}")
                    
                    # Test window size
                    window_size = controller.driver.get_window_size()
                    print(f"  - Window size: {window_size}")
                    
                    # Test enhanced element finder
                    print("\n6. Testing enhanced element finder...")
                    if hasattr(controller, 'enhanced_finder') and controller.enhanced_finder:
                        print("✓ Enhanced element finder is available")
                        stats = controller.enhanced_finder.get_performance_stats()
                        print(f"  - Performance stats: {stats}")
                    else:
                        print("! Enhanced element finder not available")
                    
                    print("\n✓ All tests passed! Android connection is working correctly.")
                    return True
                    
                except Exception as e:
                    print(f"✗ Driver functionality test failed: {e}")
                    return False
                finally:
                    # Clean up
                    print("\n7. Cleaning up...")
                    controller.disconnect()
                    print("✓ Disconnected successfully")
            else:
                print("✗ Driver not available after connection")
                return False
        else:
            print("✗ Device connection failed")
            return False
            
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_element_identification_improvements():
    """Test the element identification improvements specifically"""
    
    print("\n" + "=" * 80)
    print("Element Identification Improvements Test")
    print("=" * 80)
    
    try:
        # Test enhanced element finder
        print("\n1. Testing enhanced element finder...")
        from app_android.utils.enhanced_element_finder import EnhancedElementFinder
        
        # Create a mock controller for testing
        class MockController:
            def __init__(self):
                self.driver = None
        
        mock_controller = MockController()
        finder = EnhancedElementFinder(mock_controller)
        print("✓ Enhanced element finder created successfully")
        
        # Test adaptive timeout calculation
        print("\n2. Testing adaptive timeout calculation...")
        timeout1 = finder._calculate_adaptive_timeout('id', 'simple_id', 10, 'conditional')
        timeout2 = finder._calculate_adaptive_timeout('xpath', '//complex/xpath[contains(@attr, "value")]', 10, 'action')
        print(f"✓ Conditional timeout: {timeout1}s")
        print(f"✓ Action timeout: {timeout2}s")
        print(f"✓ Adaptive timeouts working (conditional <= action: {timeout1 <= timeout2})")
        
        # Test performance tracking
        print("\n3. Testing performance tracking...")
        stats = finder.get_performance_stats()
        print(f"✓ Performance stats: {stats}")
        
        # Test fallback strategies
        print("\n4. Testing fallback strategies...")
        from app_android.actions.base_action import BaseAction
        
        action = BaseAction()
        print("✓ BaseAction created successfully")
        
        # Check if fallback methods exist
        fallback_methods = [
            '_try_fallback_strategies',
            '_try_partial_text_fallback',
            '_try_resource_id_variations',
            '_try_xpath_to_uiselector_conversion',
            '_try_accessibility_fallbacks'
        ]
        
        for method in fallback_methods:
            if hasattr(action, method):
                print(f"✓ {method} available")
            else:
                print(f"✗ {method} missing")
                return False
        
        print("\n✓ All element identification improvements are working correctly!")
        return True
        
    except Exception as e:
        print(f"✗ Element identification test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test execution"""
    
    print("Starting Android Connection and Element Identification Tests...")
    
    # Test 1: Element identification improvements
    improvements_ok = test_element_identification_improvements()
    
    # Test 2: Android device connection
    connection_ok = test_android_device_connection()
    
    print("\n" + "=" * 80)
    print("TEST RESULTS SUMMARY")
    print("=" * 80)
    print(f"Element Identification Improvements: {'✓ PASS' if improvements_ok else '✗ FAIL'}")
    print(f"Android Device Connection: {'✓ PASS' if connection_ok else '✗ FAIL'}")
    
    if improvements_ok and connection_ok:
        print("\n🎉 All tests passed! Android connection is working with element identification improvements.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
