#!/usr/bin/env python3
"""
Test script to verify that cleanup steps execute properly during test failures and retries
"""

import sys
import os
import json
import tempfile
import logging

# Add both app directories to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

def create_test_case_with_cleanup():
    """Create a test case with regular steps and cleanup steps"""
    test_case = {
        "test_case_id": "test_cleanup_execution",
        "actions": [
            {
                "type": "tap",
                "action_id": "step1",
                "test_case_id": "regular_step_1",
                "locator_type": "xpath",
                "locator_value": "//button[@text='Start']",
                "timeout": 10
            },
            {
                "type": "tap", 
                "action_id": "step2",
                "test_case_id": "failing_step",
                "locator_type": "xpath",
                "locator_value": "//button[@text='NonExistentButton']",  # This will fail
                "timeout": 5
            },
            {
                "type": "multiStep",
                "action_id": "cleanup1",
                "test_case_id": "cleanup_step_1",
                "cleanup": True,  # Mark as cleanup step
                "test_case_steps": [
                    {
                        "type": "tap",
                        "locator_type": "xpath", 
                        "locator_value": "//button[@text='Reset']",
                        "timeout": 5
                    }
                ]
            },
            {
                "type": "cleanupSteps",  # Legacy cleanup step
                "action_id": "cleanup2",
                "test_case_id": "legacy_cleanup_step",
                "test_case_steps": [
                    {
                        "type": "tap",
                        "locator_type": "xpath",
                        "locator_value": "//button[@text='Close']",
                        "timeout": 5
                    }
                ]
            }
        ]
    }
    return test_case

def test_android_cleanup_execution():
    """Test Android cleanup step execution"""
    print("=== TESTING ANDROID CLEANUP STEP EXECUTION ===")
    
    try:
        # Create a mock device controller
        class MockDeviceController:
            def __init__(self):
                self.driver = None
                self.device_id = "test_device"
                self.platform_name = "Android"
            
            def find_element(self, *args, **kwargs):
                # Simulate element not found for failing step
                if "NonExistentButton" in str(args):
                    raise Exception("Element not found")
                return MockElement()
            
            def take_screenshot(self, *args, **kwargs):
                return "/tmp/test_screenshot.png"
        
        class MockElement:
            def click(self):
                pass
            def tap(self):
                pass
        
        # Import Android player
        from app_android.utils.player import Player
        
        # Create player with mock controller
        mock_controller = MockDeviceController()
        player = Player(mock_controller)
        
        # Create test case with cleanup steps
        test_case = create_test_case_with_cleanup()
        actions = test_case["actions"]
        
        print(f"Created test case with {len(actions)} actions:")
        for i, action in enumerate(actions):
            action_type = action.get('type')
            is_cleanup = action.get('cleanup', False) or action_type == 'cleanupSteps'
            print(f"  {i+1}. {action_type} - {'CLEANUP' if is_cleanup else 'REGULAR'}")
        
        # Test the play method
        print("\nTesting Android player.play() method:")
        success, message = player.play(actions)
        
        print(f"Execution result: Success={success}, Message='{message}'")
        
        # Test the execute_all_actions method
        print("\nTesting Android player.execute_all_actions() method:")
        success2, message2 = player.execute_all_actions(actions)
        
        print(f"Execute all actions result: Success={success2}, Message='{message2}'")
        
        return True
        
    except Exception as e:
        print(f"Error testing Android cleanup execution: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ios_cleanup_execution():
    """Test iOS cleanup step execution"""
    print("\n=== TESTING iOS CLEANUP STEP EXECUTION ===")
    
    try:
        # Create a mock device controller
        class MockDeviceController:
            def __init__(self):
                self.driver = None
                self.device_id = "test_device"
                self.platform_name = "iOS"
            
            def find_element(self, *args, **kwargs):
                # Simulate element not found for failing step
                if "NonExistentButton" in str(args):
                    raise Exception("Element not found")
                return MockElement()
            
            def take_screenshot(self, *args, **kwargs):
                return "/tmp/test_screenshot.png"
        
        class MockElement:
            def click(self):
                pass
            def tap(self):
                pass
        
        # Import iOS player
        from app.utils.player import Player
        
        # Create player with mock controller
        mock_controller = MockDeviceController()
        player = Player(mock_controller)
        
        # Create test case with cleanup steps
        test_case = create_test_case_with_cleanup()
        actions = test_case["actions"]
        
        print(f"Created test case with {len(actions)} actions:")
        for i, action in enumerate(actions):
            action_type = action.get('type')
            is_cleanup = action.get('cleanup', False) or action_type == 'cleanupSteps'
            print(f"  {i+1}. {action_type} - {'CLEANUP' if is_cleanup else 'REGULAR'}")
        
        # Test the play method
        print("\nTesting iOS player.play() method:")
        success, message = player.play(actions)
        
        print(f"Execution result: Success={success}, Message='{message}'")
        
        # Test the execute_all_actions method
        print("\nTesting iOS player.execute_all_actions() method:")
        success2, message2 = player.execute_all_actions(actions)
        
        print(f"Execute all actions result: Success={success2}, Message='{message2}'")
        
        return True
        
    except Exception as e:
        print(f"Error testing iOS cleanup execution: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cleanup_step_separation():
    """Test that cleanup steps are properly separated from regular actions"""
    print("\n=== TESTING CLEANUP STEP SEPARATION ===")
    
    test_case = create_test_case_with_cleanup()
    actions = test_case["actions"]
    
    # Test Android separation logic
    print("Android separation logic:")
    cleanup_steps = []
    regular_actions = []
    
    for action in actions:
        # Check for multiStep actions with cleanup checkbox enabled
        if action.get('type') == 'multiStep' and action.get('cleanup', False):
            cleanup_steps.append(action)
            print(f"  Found cleanup step action (multiStep with cleanup=True): {action.get('test_case_id', 'unknown')}")
        # Legacy cleanupSteps support (though obsolete, still handle if present)
        elif action.get('type') == 'cleanupSteps':
            cleanup_steps.append(action)
            print(f"  Found legacy cleanup step action: {action.get('test_case_id', 'unknown')}")
        else:
            regular_actions.append(action)
    
    print(f"Android separation result: {len(regular_actions)} regular actions, {len(cleanup_steps)} cleanup steps")
    
    # Test iOS separation logic (should be the same)
    print("\niOS separation logic:")
    ios_cleanup_steps = []
    ios_regular_actions = []
    
    for action in actions:
        # Check for multiStep actions with cleanup checkbox enabled
        if action.get('type') == 'multiStep' and action.get('cleanup', False):
            ios_cleanup_steps.append(action)
            print(f"  Found cleanup step action (multiStep with cleanup=True): {action.get('test_case_id', 'unknown')}")
        # Legacy cleanupSteps support
        elif action.get('type') == 'cleanupSteps':
            ios_cleanup_steps.append(action)
            print(f"  Found cleanup step action: {action.get('test_case_id', 'unknown')}")
        else:
            ios_regular_actions.append(action)
    
    print(f"iOS separation result: {len(ios_regular_actions)} regular actions, {len(ios_cleanup_steps)} cleanup steps")
    
    # Verify consistency
    if len(cleanup_steps) == len(ios_cleanup_steps) and len(regular_actions) == len(ios_regular_actions):
        print("✅ Android and iOS separation logic is consistent!")
        return True
    else:
        print("❌ Android and iOS separation logic is inconsistent!")
        return False

def main():
    """Main test function"""
    print("CLEANUP STEP EXECUTION TEST\n")
    print("Testing cleanup step execution during failures and retries...\n")
    
    # Test cleanup step separation
    separation_success = test_cleanup_step_separation()
    
    # Test Android cleanup execution
    android_success = test_android_cleanup_execution()
    
    # Test iOS cleanup execution  
    ios_success = test_ios_cleanup_execution()
    
    print("\n=== TEST RESULTS ===")
    print(f"Cleanup Step Separation: {'✅ PASS' if separation_success else '❌ FAIL'}")
    print(f"Android Cleanup Execution: {'✅ PASS' if android_success else '❌ FAIL'}")
    print(f"iOS Cleanup Execution: {'✅ PASS' if ios_success else '❌ FAIL'}")
    
    if separation_success and android_success and ios_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Cleanup steps are properly separated from regular actions")
        print("✅ Android cleanup step execution is working")
        print("✅ iOS cleanup step execution is working")
        print("✅ Both platforms have consistent cleanup step handling")
        print("\n🚀 CLEANUP STEP EXECUTION ISSUE RESOLVED!")
        print("Cleanup steps will now execute properly during test failures and before retries.")
    else:
        print("\n⚠️ Some tests failed. Check error messages above.")

if __name__ == "__main__":
    main()
