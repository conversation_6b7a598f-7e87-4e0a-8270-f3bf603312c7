#!/usr/bin/env python3
"""
Comprehensive test script for the enhanced environment variable resolution system.

This script tests:
1. Direct variable name substitution
2. env[variable_name] format resolution
3. Fallback to OS environment variables
4. Database integration
5. Error handling and edge cases
"""

import sys
import os
import json
import logging
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def setup_logging():
    """Setup logging for the test script."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_environment_resolution():
    """Main test function for environment resolution."""
    logger = setup_logging()
    
    print("=" * 80)
    print("ENHANCED ENVIRONMENT VARIABLE RESOLUTION SYSTEM TEST")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print()
    
    try:
        # Import the enhanced resolver
        from utils.environment_resolver import (
            resolve_text_with_env_variables,
            get_active_environment_id,
            debug_environment_resolution,
            log_environment_state
        )
        from utils.directory_paths_db import directory_paths_db
        
        print("✓ Successfully imported enhanced environment resolver")
        
        # Test 1: Get active environment
        print("\n" + "="*50)
        print("TEST 1: Active Environment Detection")
        print("="*50)
        
        active_env_id = get_active_environment_id()
        print(f"Active environment ID: {active_env_id}")
        
        if active_env_id is None:
            print("⚠️  No active environment found. Setting environment ID 2 for testing...")
            test_env_id = 2
        else:
            test_env_id = active_env_id
        
        # Test 2: Environment state logging
        print("\n" + "="*50)
        print("TEST 2: Environment State Logging")
        print("="*50)
        
        log_environment_state(test_env_id)
        
        # Test 3: Get environment variables
        print("\n" + "="*50)
        print("TEST 3: Environment Variables Retrieval")
        print("="*50)
        
        variables = directory_paths_db.get_variables_for_environment(test_env_id)
        print(f"Found {len(variables) if variables else 0} variables for environment {test_env_id}")
        
        if variables:
            print("Sample variables:")
            for i, var in enumerate(variables[:5]):  # Show first 5
                print(f"  {i+1}. {var['name']}: '{var['current_value']}'")
            if len(variables) > 5:
                print(f"  ... and {len(variables) - 5} more variables")
        
        # Test 4: Direct variable name substitution
        print("\n" + "="*50)
        print("TEST 4: Direct Variable Name Substitution")
        print("="*50)
        
        test_cases_direct = []
        if variables:
            # Use actual variable names from the database
            for var in variables[:3]:  # Test first 3 variables
                test_cases_direct.append(var['name'])
        
        # Add some test cases that should not be substituted
        test_cases_direct.extend([
            "nonexistent_variable",
            "au_prod_user1",  # This is the specific case from the logs
            "uname1",         # This should be substituted if it exists
            "password",
            "username"
        ])
        
        print("Testing direct variable name substitution:")
        for test_case in test_cases_direct:
            try:
                result = resolve_text_with_env_variables(test_case, test_env_id)
                changed = result != test_case
                status = "✓ SUBSTITUTED" if changed else "- NO CHANGE"
                print(f"  {status}: '{test_case}' -> '{result}'")
            except Exception as e:
                print(f"  ✗ ERROR: '{test_case}' -> Error: {e}")
        
        # Test 5: env[variable_name] format resolution
        print("\n" + "="*50)
        print("TEST 5: env[variable_name] Format Resolution")
        print("="*50)
        
        test_cases_env_format = [
            "env[username]",
            "env[password]",
            "env[uname1]",
            "env[nonexistent]",
            "Hello env[username], your password is env[password]",
            "env[uname1] login test",
            "No variables here",
            "env[]",  # Edge case
            "env[var with spaces]",  # Edge case
        ]
        
        print("Testing env[variable_name] format resolution:")
        for test_case in test_cases_env_format:
            try:
                result = resolve_text_with_env_variables(test_case, test_env_id)
                changed = result != test_case
                status = "✓ SUBSTITUTED" if changed else "- NO CHANGE"
                print(f"  {status}: '{test_case}' -> '{result}'")
            except Exception as e:
                print(f"  ✗ ERROR: '{test_case}' -> Error: {e}")
        
        # Test 6: OS environment variable fallback
        print("\n" + "="*50)
        print("TEST 6: OS Environment Variable Fallback")
        print("="*50)
        
        # Set a test OS environment variable
        os.environ['TEST_ENV_VAR'] = 'test_value_from_os'
        
        test_cases_os = [
            "TEST_ENV_VAR",
            "PATH",  # Should exist on most systems
            "HOME",  # Should exist on Unix systems
            "NONEXISTENT_OS_VAR"
        ]
        
        print("Testing OS environment variable fallback:")
        for test_case in test_cases_os:
            try:
                result = resolve_text_with_env_variables(test_case, test_env_id)
                changed = result != test_case
                status = "✓ SUBSTITUTED" if changed else "- NO CHANGE"
                print(f"  {status}: '{test_case}' -> '{result[:50]}{'...' if len(result) > 50 else ''}'")
            except Exception as e:
                print(f"  ✗ ERROR: '{test_case}' -> Error: {e}")
        
        # Test 7: Edge cases and error handling
        print("\n" + "="*50)
        print("TEST 7: Edge Cases and Error Handling")
        print("="*50)
        
        edge_cases = [
            "",  # Empty string
            None,  # None value (should be handled gracefully)
            123,  # Non-string value
            {"key": "value"},  # Dict value
            ["list", "value"],  # List value
            "env[var1] and env[var2] and env[var3]",  # Multiple substitutions
        ]
        
        print("Testing edge cases:")
        for i, test_case in enumerate(edge_cases):
            try:
                result = resolve_text_with_env_variables(test_case, test_env_id)
                print(f"  ✓ Case {i+1}: {type(test_case).__name__} -> {type(result).__name__}")
                if isinstance(test_case, str) and isinstance(result, str):
                    changed = result != test_case
                    if changed:
                        print(f"    '{test_case}' -> '{result}'")
            except Exception as e:
                print(f"  ✗ Case {i+1}: {type(test_case).__name__} -> Error: {e}")
        
        # Test 8: Debug utility function
        print("\n" + "="*50)
        print("TEST 8: Debug Utility Function")
        print("="*50)
        
        debug_samples = ["au_prod_user1", "uname1", "env[username]", "nonexistent"]
        debug_info = debug_environment_resolution(test_env_id, debug_samples)
        
        print("Debug information:")
        print(f"  Environment ID: {debug_info.get('environment_id')}")
        print(f"  Variables count: {len(debug_info.get('variables', [])) if isinstance(debug_info.get('variables'), list) else 'Error'}")
        print(f"  Resolution tests: {len(debug_info.get('resolution_tests', []))}")
        
        if debug_info.get('resolution_tests'):
            print("  Test results:")
            for test in debug_info['resolution_tests']:
                status = "✓" if test['changed'] else "-"
                print(f"    {status} '{test['original']}' -> '{test['resolved']}'")
        
        print("\n" + "="*80)
        print("TEST SUMMARY")
        print("="*80)
        print("✓ All tests completed successfully!")
        print("✓ Enhanced environment resolution system is working")
        print("✓ Direct variable substitution implemented")
        print("✓ env[variable] format resolution working")
        print("✓ OS environment fallback functional")
        print("✓ Error handling and edge cases covered")
        print("✓ Debug utilities available")
        
        return True
        
    except Exception as e:
        print(f"\n✗ CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_environment_resolution()
    sys.exit(0 if success else 1)
