#!/usr/bin/env python3
"""
Test script to verify environment variable resolution is working correctly
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from utils.directory_paths_db import DirectoryPathsDB
from utils.environment_resolver import resolve_text_with_env_variables

def test_environment_resolution():
    """Test environment variable resolution"""
    print("Testing Environment Variable Resolution")
    print("=" * 50)
    
    # Initialize database
    db = DirectoryPathsDB()
    
    # Get all environments
    environments = db.get_all_environments()
    print(f"Found {len(environments)} environments:")
    for env in environments:
        print(f"  ID: {env['id']}, Name: {env['name']}")
    
    if not environments:
        print("No environments found!")
        return
    
    # Test with environment ID 7 (AU-PROD-IP14)
    env_id = 7
    print(f"\nTesting with environment ID {env_id}:")
    
    # Get variables for this environment
    variables = db.get_variables_for_environment(env_id)
    print(f"Found {len(variables)} variables:")
    for var in variables:
        print(f"  {var['name']}: {var['current_value']}")
    
    # Test resolution of appid
    test_strings = [
        "env[appid]",
        "Launch app: env[appid]",
        "Terminate app: env[appid]",
        "env[package_id]",
        "env[uname]",
        "env[pwd]"
    ]
    
    print(f"\nTesting resolution with environment ID {env_id}:")
    for test_str in test_strings:
        resolved = resolve_text_with_env_variables(test_str, env_id)
        print(f"  '{test_str}' -> '{resolved}'")
    
    # Test with None environment ID
    print(f"\nTesting with None environment ID:")
    for test_str in test_strings[:2]:
        resolved = resolve_text_with_env_variables(test_str, None)
        print(f"  '{test_str}' -> '{resolved}'")

if __name__ == "__main__":
    test_environment_resolution()
