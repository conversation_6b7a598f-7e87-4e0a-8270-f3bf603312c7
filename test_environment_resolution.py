#!/usr/bin/env python3
"""
Test script to verify environment variable resolution works correctly
"""

import sys
import os

# Test iOS environment resolution
print("=== TESTING iOS ENVIRONMENT RESOLUTION ===")
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
from utils.environment_resolver import resolve_text_with_env_variables

# Test with environment ID 7 (AU-PROD-IP14)
test_cases = [
    "env[package_id]",
    "env[appid]", 
    "env[uname]",
    "hardcoded_value",
    "com.kmart.android"
]

for test_case in test_cases:
    try:
        result = resolve_text_with_env_variables(test_case, 7)
        changed = result != test_case
        print(f"  '{test_case}' -> '{result}' (changed: {changed})")
    except Exception as e:
        print(f"  '{test_case}' -> ERROR: {e}")

print()

# Test Android environment resolution
print("=== TESTING ANDROID ENVIRONMENT RESOLUTION ===")
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))
from utils.environment_resolver import resolve_text_with_env_variables as android_resolve

# Get first Android environment
from utils.directory_paths_db import directory_paths_db as android_db
android_envs = android_db.get_all_environments()
if android_envs:
    android_env_id = android_envs[0]['id']
    print(f"Testing with Android environment ID {android_env_id}")
    
    for test_case in test_cases:
        try:
            result = android_resolve(test_case, android_env_id)
            changed = result != test_case
            print(f"  '{test_case}' -> '{result}' (changed: {changed})")
        except Exception as e:
            print(f"  '{test_case}' -> ERROR: {e}")
else:
    print("No Android environments found!")
