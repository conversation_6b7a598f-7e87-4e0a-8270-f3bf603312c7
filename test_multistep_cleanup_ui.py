#!/usr/bin/env python3
"""
Test script to verify multiStep cleanup checkbox functionality in Android UI
"""

import json
import os
import sys

def test_multistep_cleanup_logic():
    """Test the multiStep cleanup identification logic"""
    print("Testing multiStep cleanup identification logic...")
    
    # Test actions with different cleanup configurations
    test_actions = [
        {
            "action_id": "test1",
            "type": "multiStep",
            "test_case_id": "cleanup_test",
            "test_case_name": "Cleanup Test Case",
            "test_case_steps_count": 5,
            "cleanup": True  # This should be identified as cleanup
        },
        {
            "action_id": "test2", 
            "type": "multiStep",
            "test_case_id": "regular_test",
            "test_case_name": "Regular Test Case",
            "test_case_steps_count": 3,
            "cleanup": False  # This should NOT be identified as cleanup
        },
        {
            "action_id": "test3",
            "type": "multiStep", 
            "test_case_id": "no_cleanup_prop",
            "test_case_name": "No Cleanup Property",
            "test_case_steps_count": 2
            # No cleanup property - should NOT be identified as cleanup
        },
        {
            "action_id": "test4",
            "type": "tap",
            "x": 100,
            "y": 200
            # Regular action - should NOT be identified as cleanup
        }
    ]
    
    # Test Android separation logic (from player.py)
    print("\nAndroid cleanup step identification:")
    cleanup_steps = []
    regular_actions = []
    
    for action in test_actions:
        # Check for multiStep actions with cleanup checkbox enabled
        if action.get('type') == 'multiStep' and action.get('cleanup', False):
            cleanup_steps.append(action)
            print(f"  ✓ Found cleanup step action (multiStep with cleanup=True): {action.get('test_case_id', 'unknown')}")
        # Legacy cleanupSteps support (though obsolete, still handle if present)
        elif action.get('type') == 'cleanupSteps':
            cleanup_steps.append(action)
            print(f"  ✓ Found legacy cleanup step action: {action.get('test_case_id', 'unknown')}")
        else:
            regular_actions.append(action)
            print(f"  - Regular action: {action.get('type', 'unknown')} (cleanup={action.get('cleanup', 'not set')})")
    
    print(f"\nResult: {len(regular_actions)} regular actions, {len(cleanup_steps)} cleanup steps")
    
    # Verify expected results
    expected_cleanup_count = 1  # Only test1 should be cleanup
    expected_regular_count = 3  # test2, test3, test4 should be regular
    
    if len(cleanup_steps) == expected_cleanup_count and len(regular_actions) == expected_regular_count:
        print("✅ Test PASSED: Cleanup step identification working correctly")
        return True
    else:
        print(f"❌ Test FAILED: Expected {expected_cleanup_count} cleanup and {expected_regular_count} regular, got {len(cleanup_steps)} cleanup and {len(regular_actions)} regular")
        return False

def test_action_description():
    """Test the action description with cleanup indicator"""
    print("\nTesting action description with cleanup indicator...")
    
    # Test multiStep actions with and without cleanup
    test_actions = [
        {
            "type": "multiStep",
            "test_case_name": "Login Flow",
            "test_case_steps_count": 5,
            "cleanup": True
        },
        {
            "type": "multiStep", 
            "test_case_name": "Main Navigation",
            "test_case_steps_count": 3,
            "cleanup": False
        },
        {
            "type": "multiStep",
            "test_case_name": "Data Entry",
            "test_case_steps_count": 7
            # No cleanup property
        }
    ]
    
    # Simulate the action description logic from action-description.js
    for action in test_actions:
        test_case_name = action.get('test_case_name', 'Unknown Test Case')
        steps_count = action.get('test_case_steps_count', 0)
        cleanup_indicator = ' [CLEANUP]' if action.get('cleanup') else ''
        description = f"Execute Test Case: {test_case_name} ({steps_count} steps){cleanup_indicator}"
        
        print(f"  Action: {description}")
    
    print("✅ Action description test completed")
    return True

def create_sample_test_case():
    """Create a sample test case with multiStep cleanup actions"""
    print("\nCreating sample test case with multiStep cleanup actions...")
    
    sample_test_case = {
        "name": "Sample Test with Cleanup",
        "description": "Test case demonstrating multiStep cleanup functionality",
        "actions": [
            {
                "action_id": "step1",
                "type": "tap",
                "locator_type": "id",
                "locator_value": "login_button",
                "timestamp": 1640995200000
            },
            {
                "action_id": "step2",
                "type": "multiStep",
                "test_case_id": "login_flow",
                "test_case_name": "Login Flow",
                "test_case_steps_count": 3,
                "cleanup": False,
                "timestamp": 1640995210000
            },
            {
                "action_id": "step3",
                "type": "tap",
                "locator_type": "xpath",
                "locator_value": "//button[@text='Submit']",
                "timestamp": 1640995220000
            },
            {
                "action_id": "step4",
                "type": "multiStep",
                "test_case_id": "cleanup_session",
                "test_case_name": "Cleanup Session",
                "test_case_steps_count": 2,
                "cleanup": True,  # This is a cleanup step
                "timestamp": 1640995230000
            }
        ]
    }
    
    # Save to a sample file
    sample_file = "sample_test_with_cleanup.json"
    with open(sample_file, 'w') as f:
        json.dump(sample_test_case, f, indent=2)
    
    print(f"✅ Sample test case saved to: {sample_file}")
    
    # Analyze the test case
    cleanup_actions = [action for action in sample_test_case['actions'] 
                      if action.get('type') == 'multiStep' and action.get('cleanup', False)]
    regular_actions = [action for action in sample_test_case['actions']
                      if not (action.get('type') == 'multiStep' and action.get('cleanup', False))]
    
    print(f"  Total actions: {len(sample_test_case['actions'])}")
    print(f"  Regular actions: {len(regular_actions)}")
    print(f"  Cleanup actions: {len(cleanup_actions)}")
    
    for cleanup_action in cleanup_actions:
        print(f"    - Cleanup: {cleanup_action['test_case_name']}")
    
    return True

def main():
    """Run all tests"""
    print("🧪 Testing multiStep cleanup checkbox functionality\n")
    
    tests = [
        test_multistep_cleanup_logic,
        test_action_description,
        create_sample_test_case
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! multiStep cleanup functionality is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
