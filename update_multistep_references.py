#!/usr/bin/env python3
"""
<PERSON><PERSON>t to update all test cases that reference a modified test case in multistep actions.
This script will reload the referenced test case steps for all multistep actions.
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add app directory to path
app_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app_android')
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_test_case(file_path):
    """Load a test case from JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load test case {file_path}: {e}")
        return None

def save_test_case(file_path, test_case_data):
    """Save a test case to JSON file"""
    try:
        # Create backup
        backup_path = f"{file_path}.bak"
        if os.path.exists(file_path):
            import shutil
            shutil.copy2(file_path, backup_path)
        
        # Save updated test case
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(test_case_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Updated test case: {os.path.basename(file_path)}")
        return True
    except Exception as e:
        logger.error(f"Failed to save test case {file_path}: {e}")
        return False

def update_multistep_references(test_cases_dir, referenced_test_case_id):
    """
    Update all test cases that reference the specified test case in multistep actions
    
    Args:
        test_cases_dir (str): Path to test cases directory
        referenced_test_case_id (str): ID of the test case that was modified
    """
    
    # Load the referenced test case to get its current steps
    referenced_test_case_path = os.path.join(test_cases_dir, referenced_test_case_id)
    if not os.path.exists(referenced_test_case_path):
        logger.error(f"Referenced test case not found: {referenced_test_case_path}")
        return
    
    referenced_test_case = load_test_case(referenced_test_case_path)
    if not referenced_test_case:
        logger.error(f"Failed to load referenced test case: {referenced_test_case_id}")
        return
    
    referenced_steps = referenced_test_case.get('actions', [])
    logger.info(f"Referenced test case has {len(referenced_steps)} steps")
    
    # Find all test cases that reference this test case
    updated_count = 0
    
    for filename in os.listdir(test_cases_dir):
        if not filename.endswith('.json') or filename.endswith('.bak'):
            continue
        
        file_path = os.path.join(test_cases_dir, filename)
        test_case = load_test_case(file_path)
        
        if not test_case:
            continue
        
        # Check if this test case has multistep actions referencing our target
        modified = False
        actions = test_case.get('actions', [])
        
        for i, action in enumerate(actions):
            if (action.get('type') == 'multiStep' and 
                action.get('test_case_id') == referenced_test_case_id):
                
                logger.info(f"Found reference in {filename} at action {i+1}")
                
                # Update the embedded steps
                action['test_case_steps'] = referenced_steps.copy()
                action['steps_loaded'] = True
                action['test_case_steps_count'] = len(referenced_steps)
                
                # Update test case name if available
                if 'name' in referenced_test_case:
                    action['test_case_name'] = referenced_test_case['name']
                
                modified = True
                logger.info(f"Updated multistep action {i+1} with {len(referenced_steps)} steps")
        
        # Save the updated test case if modified
        if modified:
            if save_test_case(file_path, test_case):
                updated_count += 1
    
    logger.info(f"Updated {updated_count} test cases that reference {referenced_test_case_id}")
    return updated_count

def main():
    """Main function"""
    # Configuration
    test_cases_dir = "/Users/<USER>/Documents/automation-tool/android_data/test_cases"
    referenced_test_case_id = "KmartSigninAUANDROID_20250709191752.json"
    
    logger.info(f"Starting update process for references to: {referenced_test_case_id}")
    logger.info(f"Test cases directory: {test_cases_dir}")
    
    if not os.path.exists(test_cases_dir):
        logger.error(f"Test cases directory not found: {test_cases_dir}")
        return 1
    
    try:
        updated_count = update_multistep_references(test_cases_dir, referenced_test_case_id)
        
        if updated_count > 0:
            logger.info(f"Successfully updated {updated_count} test cases")
            print(f"\n✅ Updated {updated_count} test cases that reference {referenced_test_case_id}")
            print("\nThe following test cases were updated:")
            
            # List the updated files
            test_case_files = [
                "App_Settings_AU_ANDROID_20250715215105.json",
                "WishList_AUAndroid_20250715224301.json", 
                "AU__Performance_Android_20250712160300.json",
                "AU_MyAccount_Android_20250712145003.json",
                "All_Sign_ins_AUANDROID_20250715202114.json",
                "Delivery__CNC_AUANDROID_20250709122628.json"
            ]
            
            for filename in test_case_files:
                print(f"  - {filename}")
                
        else:
            logger.warning("No test cases were updated")
            print(f"\n⚠️  No test cases found that reference {referenced_test_case_id}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error during update process: {e}")
        print(f"\n❌ Error: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())