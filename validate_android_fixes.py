#!/usr/bin/env python3
"""
Comprehensive validation script for Android connection fixes
Tests both the element identification improvements and device connectivity
"""

import sys
import os
import time
import logging
import requests

# Add the current directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_android_web_interface():
    """Test the Android web interface connectivity"""
    
    print("\n" + "=" * 80)
    print("Android Web Interface Test")
    print("=" * 80)
    
    try:
        # Test main page
        print("\n1. Testing main page...")
        response = requests.get("http://localhost:8081/", timeout=10)
        if response.status_code == 200:
            print("✓ Android web interface is accessible")
        else:
            print(f"✗ Android web interface returned status {response.status_code}")
            return False
        
        # Test device connection endpoint
        print("\n2. Testing device connection endpoint...")
        try:
            response = requests.get("http://localhost:8081/api/device/status", timeout=10)
            if response.status_code == 200:
                status = response.json()
                print(f"✓ Device status endpoint working: {status}")
            else:
                print(f"! Device status endpoint returned {response.status_code} (may be normal)")
        except Exception as e:
            print(f"! Device status endpoint not available: {e} (may be normal)")
        
        # Test reports endpoint
        print("\n3. Testing reports endpoint...")
        try:
            response = requests.get("http://localhost:8081/api/reports/latest", timeout=10)
            if response.status_code == 200:
                print("✓ Reports endpoint working")
            else:
                print(f"! Reports endpoint returned {response.status_code} (may be normal)")
        except Exception as e:
            print(f"! Reports endpoint issue: {e} (may be normal)")
        
        print("\n✓ Android web interface is working correctly!")
        return True
        
    except Exception as e:
        print(f"✗ Android web interface test failed: {e}")
        return False

def test_element_identification_integration():
    """Test element identification improvements integration"""
    
    print("\n" + "=" * 80)
    print("Element Identification Integration Test")
    print("=" * 80)
    
    try:
        # Test 1: Import all enhanced components
        print("\n1. Testing enhanced component imports...")
        
        from app_android.utils.enhanced_element_finder import EnhancedElementFinder
        print("✓ Enhanced element finder imported")
        
        from app_android.actions.base_action import BaseAction
        print("✓ Enhanced base action imported")
        
        from app_android.config.performance_config import performance_config
        print("✓ Performance configuration imported")
        
        from app_android.utils.healenium_config import HealeniumConfig
        print("✓ Healenium configuration imported")
        
        # Test 2: Verify performance optimizations
        print("\n2. Testing performance optimizations...")
        
        # Test screenshot optimization
        should_take = performance_config.should_take_screenshot(context='element_search')
        print(f"✓ Screenshot optimization working (skip during search: {not should_take})")
        
        should_take_failure = performance_config.should_take_screenshot(context='failure')
        print(f"✓ Screenshot on failure working (take on failure: {should_take_failure})")
        
        # Test 3: Verify Healenium integration
        print("\n3. Testing Healenium integration...")
        
        healenium_config = HealeniumConfig()
        print(f"✓ Healenium configuration loaded (enabled: {healenium_config.healenium_enabled})")
        
        health = healenium_config.check_healenium_health()
        print(f"✓ Healenium health check working (services available: {any(health.values())})")
        
        # Test 4: Verify enhanced element finder functionality
        print("\n4. Testing enhanced element finder functionality...")
        
        class MockController:
            def __init__(self):
                self.driver = None
        
        finder = EnhancedElementFinder(MockController())
        
        # Test adaptive timeouts
        timeout_conditional = finder._calculate_adaptive_timeout('id', 'test', 10, 'conditional')
        timeout_action = finder._calculate_adaptive_timeout('xpath', '//complex', 10, 'action')
        print(f"✓ Adaptive timeouts working (conditional: {timeout_conditional}s, action: {timeout_action}s)")
        
        # Test performance tracking
        stats = finder.get_performance_stats()
        print(f"✓ Performance tracking working (stats: {stats})")
        
        # Test 5: Verify base action enhancements
        print("\n5. Testing base action enhancements...")
        
        action = BaseAction(MockController())
        
        # Check fallback methods
        fallback_methods = [
            '_try_fallback_strategies',
            '_try_partial_text_fallback',
            '_try_resource_id_variations',
            '_try_xpath_to_uiselector_conversion',
            '_try_accessibility_fallbacks'
        ]
        
        for method in fallback_methods:
            if hasattr(action, method):
                print(f"✓ {method} available")
            else:
                print(f"✗ {method} missing")
                return False
        
        print("\n✓ All element identification improvements are properly integrated!")
        return True
        
    except Exception as e:
        print(f"✗ Element identification integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_android_device_connectivity():
    """Test Android device connectivity with fixes"""
    
    print("\n" + "=" * 80)
    print("Android Device Connectivity Test")
    print("=" * 80)
    
    try:
        # Test 1: ADB connectivity
        print("\n1. Testing ADB connectivity...")
        import subprocess
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
        if result.returncode == 0:
            devices = [line for line in result.stdout.split('\n') if 'device' in line and 'List' not in line]
            if devices:
                print(f"✓ ADB devices found: {len(devices)}")
                device_id = devices[0].split()[0]
                print(f"  - Using device: {device_id}")
            else:
                print("✗ No ADB devices found")
                return False
        else:
            print(f"✗ ADB command failed: {result.stderr}")
            return False
        
        # Test 2: Appium server connectivity
        print("\n2. Testing Appium server connectivity...")
        response = requests.get("http://127.0.0.1:4724/wd/hub/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✓ Appium server ready: {status['value']['message']}")
        else:
            print(f"✗ Appium server not ready: {response.status_code}")
            return False
        
        # Test 3: Device controller connectivity
        print("\n3. Testing device controller connectivity...")
        from app_android.utils.appium_device_controller import AppiumDeviceController
        
        controller = AppiumDeviceController()
        success = controller.connect_to_device(device_id, platform='Android')
        
        if success:
            print("✓ Device controller connected successfully")
            
            # Test basic functionality
            if controller.driver:
                capabilities = controller.driver.capabilities
                print(f"✓ Device capabilities: {capabilities.get('platformName')} {capabilities.get('platformVersion', 'Unknown')}")
                
                # Test enhanced element finder integration
                if hasattr(controller, 'enhanced_finder') and controller.enhanced_finder:
                    print("✓ Enhanced element finder integrated with controller")
                else:
                    print("! Enhanced element finder not integrated (may be normal)")
                
                # Clean up
                controller.disconnect()
                print("✓ Device disconnected successfully")
            else:
                print("✗ Driver not available after connection")
                return False
        else:
            print("✗ Device controller connection failed")
            return False
        
        print("\n✓ Android device connectivity is working correctly!")
        return True
        
    except Exception as e:
        print(f"✗ Android device connectivity test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main validation function"""
    
    print("=" * 80)
    print("Android Connection Fixes - Comprehensive Validation")
    print("=" * 80)
    print("This script validates that all Android connection fixes are working correctly")
    print("and that element identification improvements are properly integrated.")
    
    # Run all tests
    tests = [
        ("Element Identification Integration", test_element_identification_integration),
        ("Android Device Connectivity", test_android_device_connectivity),
        ("Android Web Interface", test_android_web_interface),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n✗ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 80)
    print("VALIDATION RESULTS SUMMARY")
    print("=" * 80)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("Android connection fixes are working correctly.")
        print("Element identification improvements are properly integrated.")
        print("The Android automation framework is ready for use.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please review the output above for details on failed tests.")
        print("You may need to address the issues before using the framework.")
    
    print("=" * 80)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
